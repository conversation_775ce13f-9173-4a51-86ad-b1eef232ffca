#!/bin/bash
# Ubuntu Post-Installation Setup

# System aktualisieren
sudo apt update && sudo apt upgrade -y

# Wichtige Pakete installieren
sudo apt install -y build-essential git curl wget htop neofetch \
  python3-pip python3-venv docker.io docker-compose

# Docker konfigurieren
sudo usermod -aG docker $USER
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json > /dev/null <<DOCKERCONFIG
{
  "data-root": "/var/lib/docker",
  "storage-driver": "overlay2",
  "default-ulimits": {
    "nofile": {
      "Name": "nofile",
      "Hard": 64000,
      "Soft": 64000
    }
  },
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
DOCKERCONFIG

# Systemoptimierungen für LLMs
# (Swappiness wurde bereits auf 10 gesetzt)
echo 'fs.file-max=100000' | sudo tee -a /etc/sysctl.d/99-file-max.conf
sudo sysctl -p /etc/sysctl.d/99-file-max.conf

# LLM-Verzeichnis Berechtigungen anpassen (falls nötig)
sudo chown $USER:$USER /opt/llm

# AMD-Treiber für GPU-Beschleunigung
sudo apt install -y mesa-opencl-icd

echo "Setup abgeschlossen! Bitte System neu starten."
