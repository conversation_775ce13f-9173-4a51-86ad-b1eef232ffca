Hardware:
Intel(R) Core(TM) i7-9700K CPU @ 3.60GHz
Kernel: Darwin 23.3.0 x86_64
RAM 32.00 GB

AMD Radeon RX 570 Metal Default Device Yes, Metal Device Name: AMD Radeon RX 570, Metal LowPower: NOy, Metal Headless: no, AMDFramebufferVIB Ports:5, Quartz Extrem (QE/CI) Yes, 


Intel Iris Plus Graphics 655

Realtek RTL PCI Express Gigabit Ethernet Controller
Broadcom Inc. and subsidiaries BCM4360 802.11ac Wireless Network Adapter
Bluetooth BCM_20702B0
Sound: REaltek ALC882

03:00.0 Network controller: Broadcom Inc. and subsidiaries BCM4360 802.11ac Dual Band Wireless Network Adapter (rev 03)
Subsystem: Apple Inc. BCM4360 802.11ac Dual Band Wireless Network Adapter
Motherboard: Gigabyte Z390

NVMe SSD Controller MAP1602

Bootloader: OpenCore aktuell 0.9.6 / Verfügbar 1.0.4

**Festplatte für UBUNTU**:
 Verbatim Vi7000G Internal PCIe 4 TB M.2 SSD Media 7 disk1
Volumetyp : Physisches Gerät
BSD-Geräteknoten : disk1
Verbindung : PCI-Express
Gerätebaumpfad : IODeviceTree:/PCI0@0/RP09@1D/PXSX@0/IONVMeController
Beschreibbar : Nein
Groß-/Kleinschreibung wird beachtet : Nein
Volumekapazität : 4.096.805.658.624
Verfügbarer Platz (Löschbar + Frei) : 0
Löschbarer Speicherplatz : 0
Frei : 0
Belegt : 4.096.805.658.624
Eigentümer:in aktiviert : Nein
Ist verschlüsselt : Nein
Überprüfbar : Nein
Reparierbar : Nein
Startfähig : Nein
Journaling : Nein
Volume-Nummer : 1
Medienname : Vi7000G Internal PCIe M.2 SSD Media
Medientyp : Allgemein
Auswerfbar : Nein
Solid State : Ja
S.M.A.R.T.-Status : Überprüft


