(gsettings list-recursively org.gnome.settings-daemon.plugins.media-keys)…
  ⎿  org.gnome.settings-daemon.plugins.media-keys battery-status ['']
     org.gnome.settings-daemon.plugins.media-keys battery-status-static ['XF86Battery']
     org.gnome.settings-daemon.plugins.media-keys calculator ['']
     org.gnome.settings-daemon.plugins.media-keys calculator-static ['XF86Calculator']
     org.gnome.settings-daemon.plugins.media-keys control-center ['']
     org.gnome.settings-daemon.plugins.media-keys control-center-static ['XF86Tools']
     org.gnome.settings-daemon.plugins.media-keys custom-keybindings @as []
     org.gnome.settings-daemon.plugins.media-keys decrease-text-size ['']
     org.gnome.settings-daemon.plugins.media-keys eject ['']
     org.gnome.settings-daemon.plugins.media-keys eject-static ['XF86Eject']
     org.gnome.settings-daemon.plugins.media-keys email ['']
     org.gnome.settings-daemon.plugins.media-keys email-static ['XF86Mail']
     org.gnome.settings-daemon.plugins.media-keys help ['', '<Super>F1']
     org.gnome.settings-daemon.plugins.media-keys hibernate ['']
     org.gnome.settings-daemon.plugins.media-keys hibernate-static ['XF86Suspend',
     'XF86Hibernate']
     org.gnome.settings-daemon.plugins.media-keys home ['']
     org.gnome.settings-daemon.plugins.media-keys home-static ['XF86Explorer']
     org.gnome.settings-daemon.plugins.media-keys increase-text-size ['']
     org.gnome.settings-daemon.plugins.media-keys keyboard-brightness-down ['']
     org.gnome.settings-daemon.plugins.media-keys keyboard-brightness-down-static
     ['XF86KbdBrightnessDown']
     org.gnome.settings-daemon.plugins.media-keys keyboard-brightness-toggle ['']
     org.gnome.settings-daemon.plugins.media-keys keyboard-brightness-toggle-static
     ['XF86KbdLightOnOff']
     org.gnome.settings-daemon.plugins.media-keys keyboard-brightness-up ['']
     org.gnome.settings-daemon.plugins.media-keys keyboard-brightness-up-static
     ['XF86KbdBrightnessUp']
     org.gnome.settings-daemon.plugins.media-keys logout ['<Control><Alt>Delete']
     ... (+49 lines)
     org.gnome.settings-daemon.plugins.media-keys touchpad-off ['']
     org.gnome.settings-daemon.plugins.media-keys touchpad-off-static ['XF86TouchpadOff']
     org.gnome.settings-daemon.plugins.media-keys touchpad-on ['']
     org.gnome.settings-daemon.plugins.media-keys touchpad-on-static ['XF86TouchpadOn']
     org.gnome.settings-daemon.plugins.media-keys touchpad-toggle ['']
     org.gnome.settings-daemon.plugins.media-keys touchpad-toggle-static
     ['XF86TouchpadToggle', '<Ctrl><Super>XF86TouchpadToggle']
     org.gnome.settings-daemon.plugins.media-keys volume-down ['']
     org.gnome.settings-daemon.plugins.media-keys volume-down-precise ['']
     org.gnome.settings-daemon.plugins.media-keys volume-down-precise-static
     ['<Shift>XF86AudioLowerVolume', '<Ctrl><Shift>XF86AudioLowerVolume']
     org.gnome.settings-daemon.plugins.media-keys volume-down-quiet ['']
     org.gnome.settings-daemon.plugins.media-keys volume-down-quiet-static
     ['<Alt>XF86AudioLowerVolume', '<Alt><Ctrl>XF86AudioLowerVolume']
     org.gnome.settings-daemon.plugins.media-keys volume-down-static
     ['XF86AudioLowerVolume', '<Ctrl>XF86AudioLowerVolume']
     org.gnome.settings-daemon.plugins.media-keys volume-mute ['']
     org.gnome.settings-daemon.plugins.media-keys volume-mute-quiet ['']
     org.gnome.settings-daemon.plugins.media-keys volume-mute-quiet-static
     ['<Alt>XF86AudioMute']
     org.gnome.settings-daemon.plugins.media-keys volume-mute-static ['XF86AudioMute']
     org.gnome.settings-daemon.plugins.media-keys volume-step 6
     org.gnome.settings-daemon.plugins.media-keys volume-up ['']
     org.gnome.settings-daemon.plugins.media-keys volume-up-precise ['']
     org.gnome.settings-daemon.plugins.media-keys volume-up-precise-static
     ['<Shift>XF86AudioRaiseVolume', '<Ctrl><Shift>XF86AudioRaiseVolume']
     org.gnome.settings-daemon.plugins.media-keys volume-up-quiet ['']
     org.gnome.settings-daemon.plugins.media-keys volume-up-quiet-static
     ['<Alt>XF86AudioRaiseVolume', '<Alt><Ctrl>XF86AudioRaiseVolume']
     org.gnome.settings-daemon.plugins.media-keys volume-up-static
     ['XF86AudioRaiseVolume', '<Ctrl>XF86AudioRaiseVolume']
     org.gnome.settings-daemon.plugins.media-keys www ['']
     org.gnome.settings-daemon.plugins.media-keys www-static ['XF86WWW']
