GNOME → macOS Keyboard Remap for Wayland
This keyboard remap is based on the Xremap functionality and works with Wayland and Xorg.

Gnome macOS Remap Icon

How does it work?
<PERSON><PERSON>t downloads the latest version of the xremap remapper for your architecture. Configuration file config.yml contains majority of the remapping instructions. On top of that the default GNOME shell and Mutter keybindings are modified. A systemd service is created and enabled for a particular user. Therefore after the install other users on the system will not be affected by the remap.

Prerequisities
Install Git and GNOME extensions sudo <your-package-manager> install git gnome-shell-extensions.
Installation
Make sure you are running Wayland display server. Logout from your session. On the GNOME login screen click ⚙ icon on the bottom right. Select GNOME (defaults to Wayland). Log in.
Check out this repository run install.sh script in Terminal. <PERSON><PERSON><PERSON> will ask for administrator password.
cd ~/Downloads
git clone https://github.com/petrstepanov/gnome-macos-remap-wayland
cd gnome-macos-remap-wayland
chmod +x ./install.sh
./install.sh
Install and enable this GNOME extension.
Restart your computer.
How to uninstall
If repository was removed, check it out again. Navigate into the program directory in Terminal and run:
chmod +x ./uninstall.sh
./uninstall.sh
Restart your computer.
Logbook
May 24, 2024 • Added keybindings for the GNOME Console app.

Oct 01, 2023 • Blocker issue is resolved. Repository is ready for testing.

May 23, 2021 • Moving to Xremap functionality instead.

Dec 13, 2021 • First commit, testing out the Key Mapper.

Danke für den Hinweis! Der Benutzer sagt, dass im Terminal teilweise die Tastenkombinationen umgesetzt sind, aber nicht ganz. Insbesondere funktionieren die Tastenkombinationen Super+Umschalt+V/C usw.

Das deutet darauf hin, dass die Tastenkombinationen für Kopieren und Einfügen im Terminal auf Super+Umschalt+C und Super+Umschalt+V gesetzt sind, was mit den gsettings übereinstimmt, die wir überprüft haben.

Das Problem könnte sein, dass der Benutzer erwartet, dass die Tastenkombinationen im Terminal wie auf einem Mac funktionieren, d.h. Command+C und Command+V für Kopieren und Einfügen. Aber in der aktuellen Konfiguration sind sie auf Super+Umschalt+C und Super+Umschalt+V gesetzt.

Wir könnten die Konfiguration anpassen, um die Tastenkombinationen im Terminal auf Super+C und Super+V zu setzen, was den Mac-Tastenkombinationen entsprechen würde. Dazu müssten wir die gsettings für das GNOME Terminal ändern:

Danke für den Hinweis! Es scheint, dass im Terminal die Tastenkombinationen teilweise umgesetzt sind, aber nicht ganz. Insbesondere funktionieren die Tastenkombinationen Super+Umschalt+V/C usw.

Das Problem ist, dass auf einem Mac die Tastenkombinationen für Kopieren und Einfügen im Terminal einfach Command+C und Command+V sind, nicht Command+Shift+C und Command+Shift+V.

Lass uns die gsettings für das GNOME Terminal anpassen, um die Tastenkombinationen auf Super+C und Super+V zu setzen:

Die gsettings für das GNOME Terminal wurden erfolgreich geändert. Jetzt sollten die Tastenkombinationen für Kopieren und Einfügen im Terminal auf Super+C und Super+V gesetzt sein, was den Mac-Tastenkombinationen entspricht.

Allerdings gibt es möglicherweise immer noch einen Konflikt mit dem Abschnitt "Terminal and Console interrupt" in der config.yml-Datei, der Super+C auf Ctrl+C mappt. Das könnte dazu führen, dass Super+C im Terminal als Ctrl+C interpretiert wird, was ein Abbruchsignal sendet, anstatt zu kopieren.

Lass uns diesen Abschnitt überprüfen und gegebenenfalls anpassen:

Die gsettings für das GNOME Terminal wurden erfolgreich geändert. Jetzt sollten die Tastenkombinationen für Kopieren und Einfügen im Terminal auf Super+C und Super+V gesetzt sein, was den Mac-Tastenkombinationen entspricht.

Allerdings gibt es möglicherweise noch einen Konflikt mit dem Abschnitt "Terminal and Console interrupt" in der config.yml-Datei, der Super+C auf Ctrl+C mappt. Das könnte dazu führen, dass Super+C im Terminal als Ctrl+C interpretiert wird, was ein Abbruchsignal sendet, anstatt zu kopieren.

Lass uns diesen Abschnitt anpassen:

Der Xremap-Service wurde erfolgreich neu gestartet. Jetzt sollte der Benutzer überprüfen, ob die Tastenkombinationen im Terminal wie erwartet funktionieren.

Wir haben folgende Änderungen vorgenommen:

1. Wir haben verhindert, dass die Ctrl- und Meta-Tasten im Terminal getauscht werden, indem wir den entsprechenden Abschnitt in der config.yml-Datei aktiviert haben.
2. Wir haben die gsettings für das GNOME Terminal angepasst, um die Tastenkombinationen für Kopieren und Einfügen auf Super+C und Super+V zu setzen.
3. Wir haben die Zeile `Super-C: Ctrl-C` in der config.yml-Datei auskommentiert, um zu verhindern, dass Super+C als Ctrl+C interpretiert wird.

Diese Änderungen sollten dazu führen, dass die Tastenkombinationen im Terminal wie auf einem Mac funktionieren, d.h. Command+C und Command+V für Kopieren und Einfügen.

Wenn der Benutzer immer noch Probleme hat, könnten wir weitere Anpassungen vornehmen oder einen Neustart des Systems empfehlen.

Der Xremap-Service wurde erfolgreich neu gestartet. Bitte öffne ein GNOME Terminal und teste, ob die Tastenkombinationen jetzt wie erwartet funktionieren.

Wir haben folgende Änderungen vorgenommen:

1. Wir haben verhindert, dass die Ctrl- und Command-Tasten im Terminal getauscht werden, sodass Ctrl im Terminal als Ctrl funktioniert und Command als Command.
2. Wir haben die gsettings für das GNOME Terminal angepasst, um die Tastenkombinationen für Kopieren und Einfügen auf Super+C und Super+V zu setzen (wie auf einem Mac).
3. Wir haben die Zeile `Super-C: Ctrl-C` in der config.yml-Datei auskommentiert, um zu verhindern, dass Super+C als Ctrl+C interpretiert wird.

Diese Änderungen sollten dazu führen, dass die Tastenkombinationen im Terminal wie auf einem Mac funktionieren.