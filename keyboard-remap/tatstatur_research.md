# MacOS-ähnliche Tastaturkonfiguration unter Ubuntu 24.04 LTS mit Wayland

Vor dem Hintergrund der Umstellung auf Wayland als Standard-Display-Server in Ubuntu 24.04 LTS möchte ich die aktuellen Möglichkeiten aufzeigen, wie Sie Ihr System mit macOS-ähnlichen Tastenkombinationen konfigurieren können. Die Herausforderung liegt in Waylands strengerem Sicherheitsmodell, das viele traditionelle X11-basierte Lösungen wirkungslos macht.

## Warum traditionelle Methoden unter Wayland nicht funktionieren

Unter X11 war es relativ einfach, Tastatureingaben abzufangen und zu modifizieren. Tools wie xdotool oder autokey waren dafür die Standardlösungen. Wayland hingegen implementiert ein strengeres Sicherheitsmodell, das direkten Zugriff auf Eingabeereignisse verhindert[4]. Dies erklärt, warum Ihre bisherigen Ansätze nicht funktioniert haben.

## Aktuelle Lösungen für Wayland

### 1. gnome-macos-remap-wayland

Eines der vielversprechendsten Projekte ist "gnome-macos-remap-wayland" von Petr Stepanov. Dieses Tool basiert auf xremap und funktioniert sowohl mit Wayland als auch mit X11[2].

**Installation:**
```bash
cd ~/Downloads
git clone https://github.com/petrstepanov/gnome-macos-remap-wayland
cd gnome-macos-remap-wayland
chmod +x ./install.sh
./install.sh
```

Nach der Installation sollten Sie Ihren Computer neu starten. Das Tool konfiguriert die folgenden Funktionen:
- Command-Taste (⌘) als Hauptmodifikator
- Kopieren/Einfügen mit ⌘+C, ⌘+V (auch im Terminal)
- Anfang/Ende der Zeile mit ⌘+← und ⌘+→
- Anwendungswechsel mit ⌘+Tab
- Und weitere macOS-typische Tastenkombinationen[2]

### 2. keyd in Kombination mit GNOME Tweaks

Eine weitere robuste Lösung ist die Verwendung von keyd, einem systemweiten Tastenremapper, zusammen mit GNOME Tweaks[5].

**Installation und Konfiguration:**
```bash
sudo add-apt-repository ppa:keyd-team/ppa
sudo apt update
sudo apt install keyd gnome-tweaks
```

Danach erstellen Sie eine Konfigurationsdatei unter `/etc/keyd/` und führen `sudo keyd reload` aus. Die genaue Konfiguration hängt von Ihren Präferenzen ab, aber ein typisches Setup würde die Command- und Control-Tasten tauschen[5].

Anschließend können Sie GNOME Tweaks verwenden, um die "Overview Shortcut" auf "Right Super" zu setzen, damit die Activities-Übersicht nicht versehentlich durch die umkonfigurierte Control-Taste ausgelöst wird[5].

### 3. Input Remapper (ehemals Key Mapper)

Input Remapper ist ein GUI-Tool, das speziell für das Remapping von Tastatur- und Maustasten unter Linux entwickelt wurde und sowohl X11 als auch Wayland unterstützt[17].

**Installation:**
```bash
sudo add-apt-repository ppa:nrbrtx/python3-input-remapper
sudo apt update
sudo apt install python3-input-remapper
```

Nach der Installation können Sie Input Remapper aus dem Anwendungsmenü starten. Das Tool bietet eine benutzerfreundliche Oberfläche zum Erstellen gerätespezifischer Presets und unterstützt auch Makros. Besonders praktisch ist die Funktion, Presets beim Login automatisch zu laden[17].

## Spezielle Anpassungen für Terminal-Anwendungen

Ein häufiges Problem bei macOS-ähnlichen Konfigurationen ist, dass im Terminal sowohl ⌘+C zum Kopieren als auch Ctrl+C zum Senden eines SIGINT-Signals verwendet werden soll. Hierfür gibt es verschiedene Ansätze:

1. **Terminal-spezifische Profile**: Viele Terminal-Emulatoren erlauben eigene Tastenkonfigurationen.

2. **wl-clipboard**: Für Clipboard-Operationen in Wayland können Sie wl-clipboard verwenden[3][8]:
   ```bash
   sudo apt install wl-clipboard
   ```
   Damit können Sie Pipelines erstellen wie:
   ```bash
   echo "Text" | wl-copy  # Kopieren in die Zwischenablage
   wl-paste  # Ausgabe der Zwischenablage
   ```

## Bekannte Einschränkungen unter Wayland

Trotz dieser Lösungsansätze gibt es einige inhärente Einschränkungen in Waylands Architektur:

1. Wayland-Compositoren senden Tastaturlayouts im XKB-Format an den Client, der für die Umwandlung von Keycodes zu Symbolen verantwortlich ist[16].

2. Ohne Modifikation des Clients ist es nicht möglich, die Beziehung zwischen Codes und Symbolen zu ändern, abgesehen von dem, was die XKB-Definition selbst bietet[16].

3. Darum nutzen viele Tools evdev anstelle von Wayland-spezifischen Methoden, da es auf niedrigerer Ebene arbeitet und mit praktisch allem kompatibel ist[16].

## Fazit

Die Umstellung von X11 auf Wayland bringt einige Herausforderungen mit sich, aber es gibt inzwischen brauchbare Lösungen für macOS-ähnliche Tastenkonfigurationen. Je nach Ihren spezifischen Anforderungen empfehle ich:

- Für eine umfassende Lösung: gnome-macos-remap-wayland
- Für systemweite, aber minimale Anpassungen: keyd mit GNOME Tweaks
- Für eine benutzerfreundliche GUI-Lösung: Input Remapper

Alle diese Lösungen werden aktiv weiterentwickelt und bieten unter Ubuntu 24.04 LTS mit Wayland eine solide Grundlage für ein macOS-ähnliches Tastaturerlebnis.

Citations:
[1] https://cis106.com/labs/lab2_m_sereires_mac/
[2] https://github.com/petrstepanov/gnome-macos-remap-wayland
[3] https://github.com/bugaevc/wl-clipboard
[4] https://www.reddit.com/r/Ubuntu/comments/vituv8/xdotool_key_doenst_work_on_wayland_whats_the/
[5] https://gist.github.com/bernhardfritz/f250ceaec666a974341b422debb6ae1a
[6] https://github.com/petrstepanov/gnome-macos-remap
[7] https://www.reddit.com/r/Ubuntu/comments/1dfdfys/remapping_keys_on_ubuntu_2404_lts_with_a_mac/
[8] https://superuser.com/questions/1189467/how-to-copy-text-to-the-clipboard-when-using-wayland
[9] https://superuser.com/questions/1032270/wayland-alternative-for-xorgs-xdotool
[10] https://forum.linuxguides.de/index.php?thread%2F9034-imac-installation%2F
[11] https://superuser.com/questions/385748/binding-superc-superv-to-copy-and-paste
[12] https://kofler.info/multipass-ubuntu-vms-unter-windows-macos-und-linux-ausfuehren/
[13] https://askubuntu.com/questions/857282/how-to-make-the-keyboard-shortcuts-like-macos-on-ubuntu
[14] https://kofler.info/fluch-und-segen-von-gnome/
[15] https://www.youtube.com/watch?v=utqpa_8SXkA
[16] https://www.reddit.com/r/wayland/comments/synjyl/waylandnative_method_to_remap_keys/
[17] https://www.linuxuprising.com/2020/12/remap-keyboard-and-mouse-buttons-on.html
[18] https://askubuntu.com/questions/1538644/how-do-i-enable-wayland-in-xubuntu-24-04
[19] https://discuss.kde.org/t/toshy-v23-06-mac-like-per-app-keyboard-shortcuts-now-supports-wayland-kde/2380
[20] https://unix.stackexchange.com/questions/342628/gnome-3-keybindings-in-source-where-are-ctrl-c-cut-copy-and-paste-defin
[21] https://unix.stackexchange.com/questions/381831/keyboard-emulation-in-wayland
[22] https://kskroyal.com/ubuntu-24-04-post-install-steps/
[23] https://linuxconfig.org/how-to-enable-disable-wayland-on-ubuntu-22-04-desktop
[24] https://www.reddit.com/r/wayland/comments/18hggfh/singlekey_global_shortcuts_for_copy_and_paste/
[25] https://stackoverflow.com/questions/48038038/xdotool-type-takes-ages-and-causes-entire-desktop-to-freeze
[26] https://www.reddit.com/r/Ubuntu/comments/1dfdfys/remapping_keys_on_ubuntu_2404_lts_with_a_mac/?tl=de
[27] https://askubuntu.com/questions/961175/copy-and-paste-doesnt-work-in-the-terminal
[28] https://askubuntu.com/questions/1332788/xdotool-does-not-work-with-gnome-keyboard-shortcuts
[29] https://www.linux-community.de/wp-content/uploads/2024/07/lu-ce_2024-07.pdf
[30] https://forum.ubuntuusers.de/topic/externer-monitor-wird-nicht-erkannt-7/
[31] https://www.reddit.com/r/linux/comments/qqtmen/using_macos_ctrl_option_and_command_keys_on_linux/?tl=de
[32] https://andersgood.de/kurz-notiert/performanter-remote-desktop-fuer-ubuntu-und-co
[33] https://unix.stackexchange.com/questions/740638/systemwide-uniform-keyboard-remapping-in-gnome-de-to-match-mac-keyboard-shortcut
[34] https://help.ubuntu.com/stable/ubuntu-help/shell-keyboard-shortcuts.html.de
[35] https://askubuntu.com/questions/368437/how-do-i-remap-my-ubuntu-keyboard-shortcuts-to-match-osx
[36] https://www.macuser.de/threads/lm22-auf-macbook-air-3-2-startet-erst-nach-langer-pause.948449/
[37] https://www.reddit.com/r/Ubuntu/comments/1cdh92x/wayland_resizing_windows_problembug/?tl=de
[38] https://github.com/bjohas/Ubuntu-keyboard-map-like-OS-X
[39] https://unix.stackexchange.com/questions/647729/mapping-the-linux-keyboard-to-match-macos-keys
[40] https://superuser.com/questions/426515/efficient-key-bindings-using-both-mac-and-linux

---
Antwort von Perplexity: pplx.ai/share