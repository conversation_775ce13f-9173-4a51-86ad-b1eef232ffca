<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_1368_44" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="12" y="15" width="104" height="101">
<path d="M109 15.4L113.894 21H113.876C115.195 22.6639 116 24.8889 116 27.3333V106.667C116 111.821 112.418 116 108 116H20C15.5817 116 12 111.821 12 106.667V27.3333C12 24.8889 12.8055 22.6639 14.1237 21H14.1084L19 15.4H109Z" fill="black"/>
</mask>
<g mask="url(#mask0_1368_44)">
<rect x="12" y="12" width="52" height="104" fill="url(#paint0_linear_1368_44)"/>
<rect width="52" height="104" transform="matrix(-1 0 0 1 116 12)" fill="url(#paint1_linear_1368_44)"/>
</g>
<path d="M110 18.88C110 15.0803 106.832 12 102.923 12C102.923 12 83.5745 13 64 13C44.4255 13 25.0769 12 25.0769 12C21.1684 12 18 15.0803 18 18.88V91.12C18 94.9197 21.1684 98 25.0769 98C25.0769 98 38.0638 99 64 99C89.9362 99 102.923 98 102.923 98C106.085 98 110 94.9197 110 91.12V18.88Z" fill="#383838"/>
<g filter="url(#filter0_d_1368_44)">
<path d="M39.3076 81.3994C39.2188 79.7383 37.9609 78.5146 35.9785 78.5146C33.7842 78.5146 32.3828 79.9775 32.3828 82.3291C32.3828 84.7217 33.7842 86.1572 35.9922 86.1572C37.9199 86.1572 39.2119 85.043 39.3145 83.3135H37.4551C37.332 84.1133 36.8125 84.5918 36.0127 84.5918C35.0215 84.5918 34.4062 83.7715 34.4062 82.3291C34.4062 80.9141 35.0215 80.0801 36.0059 80.0801C36.8262 80.0801 37.3389 80.627 37.4551 81.3994H39.3076ZM43.6416 86.1572C45.8564 86.1572 47.2715 84.7422 47.2715 82.3291C47.2715 79.9502 45.8359 78.5146 43.6416 78.5146C41.4473 78.5146 40.0117 79.957 40.0117 82.3291C40.0117 84.7354 41.4268 86.1572 43.6416 86.1572ZM43.6416 84.6328C42.6572 84.6328 42.0352 83.8057 42.0352 82.3359C42.0352 80.8799 42.6709 80.0391 43.6416 80.0391C44.6123 80.0391 45.2412 80.8799 45.2412 82.3359C45.2412 83.8057 44.6191 84.6328 43.6416 84.6328ZM48.3037 86H50.2998V81.5771C50.2998 80.7568 50.8125 80.1826 51.5713 80.1826C52.3096 80.1826 52.7539 80.6475 52.7539 81.4336V86H54.6748V81.5498C54.6748 80.7363 55.1738 80.1826 55.9326 80.1826C56.7051 80.1826 57.1289 80.6475 57.1289 81.4951V86H59.125V80.9961C59.125 79.5195 58.1611 78.5352 56.7256 78.5352C55.6523 78.5352 54.7773 79.1162 54.4492 80.0391H54.4082C54.1621 79.082 53.4238 78.5352 52.3643 78.5352C51.3662 78.5352 50.5801 79.1162 50.2725 79.998H50.2314V78.6719H48.3037V86ZM60.3896 86H62.3857V81.5771C62.3857 80.7568 62.8984 80.1826 63.6572 80.1826C64.3955 80.1826 64.8398 80.6475 64.8398 81.4336V86H66.7607V81.5498C66.7607 80.7363 67.2598 80.1826 68.0186 80.1826C68.791 80.1826 69.2148 80.6475 69.2148 81.4951V86H71.2109V80.9961C71.2109 79.5195 70.2471 78.5352 68.8115 78.5352C67.7383 78.5352 66.8633 79.1162 66.5352 80.0391H66.4941C66.248 79.082 65.5098 78.5352 64.4502 78.5352C63.4521 78.5352 62.666 79.1162 62.3584 79.998H62.3174V78.6719H60.3896V86ZM75.3057 84.6738C74.6289 84.6738 74.1777 84.332 74.1777 83.7852C74.1777 83.2656 74.6016 82.9307 75.3467 82.876L76.8916 82.7803V83.3135C76.8916 84.1064 76.1738 84.6738 75.3057 84.6738ZM74.6494 86.1162C75.5654 86.1162 76.4746 85.6582 76.8848 84.8789H76.9258V86H78.8535V80.9619C78.8535 79.4854 77.6299 78.5146 75.75 78.5146C73.8086 78.5146 72.5986 79.4922 72.5234 80.9141H74.3486C74.4443 80.374 74.916 80.0049 75.6611 80.0049C76.4131 80.0049 76.8916 80.4014 76.8916 81.085V81.5703L75.0459 81.6797C73.2207 81.7891 72.1953 82.5615 72.1953 83.8945C72.1953 85.2139 73.2549 86.1162 74.6494 86.1162ZM80.1455 86H82.1416V81.7959C82.1416 80.8457 82.709 80.1895 83.6045 80.1895C84.5 80.1895 84.9512 80.7363 84.9512 81.6934V86H86.9473V81.2764C86.9473 79.5605 86.0312 78.5352 84.4043 78.5352C83.2764 78.5352 82.5039 79.0684 82.1143 79.9775H82.0732V78.6719H80.1455V86ZM90.9326 86.1162C92.0127 86.1162 92.8604 85.5488 93.2158 84.7422H93.25V86H95.2188V76.1357H93.2227V79.9297H93.1816C92.8262 79.1162 92.0059 78.5557 90.9463 78.5557C89.1074 78.5557 87.9453 79.998 87.9453 82.3223C87.9453 84.667 89.1006 86.1162 90.9326 86.1162ZM91.6094 80.1553C92.6006 80.1553 93.2363 81.0029 93.2363 82.3359C93.2363 83.6758 92.6006 84.5098 91.6094 84.5098C90.6045 84.5098 89.9893 83.6826 89.9893 82.3359C89.9893 80.9961 90.6045 80.1553 91.6094 80.1553Z" fill="white"/>
</g>
<g filter="url(#filter1_i_1368_44)">
<mask id="mask1_1368_44" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="66" y="24" width="33" height="40">
<path d="M95.1124 24C86.7911 24 85.2301 35.8522 89.9113 35.8522C94.5918 35.8522 103.434 24 95.1124 24Z" fill="black"/>
<path d="M81.295 34.3836C83.7875 34.5358 86.5221 24.8716 81.8375 25.4795C77.1546 26.0873 78.8015 34.2314 81.295 34.3836Z" fill="black"/>
<path d="M69.872 40.3886C71.6447 39.6083 70.1003 31.976 67.086 34.0575C64.0729 36.1394 68.0993 41.1686 69.872 40.3886Z" fill="black"/>
<path d="M74.8668 36.3937C76.977 35.9635 77.0927 27.3073 73.3295 28.8601C69.5656 30.4133 72.7582 36.8246 74.8668 36.3937Z" fill="black"/>
<path d="M85.8862 55.7649C86.2606 58.6244 83.7884 60.0365 81.3673 58.1931C73.6599 52.3251 94.1287 49.3976 92.7794 41.3807C91.6595 34.7262 71.2418 36.7749 68.9168 47.1872C67.3429 54.2297 75.395 64 83.7969 64C87.9301 64 92.6976 60.2679 93.5895 55.54C94.2707 51.9347 85.5736 53.3793 85.8862 55.7649Z" fill="black"/>
</mask>
<g mask="url(#mask1_1368_44)">
<rect x="64.714" y="41.0023" width="36.0256" height="4.60356" fill="#FEB827"/>
<rect x="64.714" y="23.1769" width="36.0256" height="17.8254" fill="#61BB46"/>
<rect x="64.714" y="45.6059" width="36.0256" height="4.60356" fill="#F6821F"/>
<rect x="64.714" y="50.2094" width="36.0256" height="4.60356" fill="#E13A3E"/>
<rect x="64.714" y="54.813" width="36.0256" height="4.60356" fill="#963D96"/>
<rect x="64.714" y="59.4166" width="36.0256" height="4.60356" fill="#009DDC"/>
</g>
</g>
<defs>
<filter id="filter0_d_1368_44" x="32.3828" y="76.1357" width="62.8359" height="12.0215" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1368_44"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1368_44" result="shape"/>
</filter>
<filter id="filter1_i_1368_44" x="66" y="24" width="32.5437" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1368_44"/>
</filter>
<linearGradient id="paint0_linear_1368_44" x1="12" y1="97.5" x2="64.8432" y2="113.154" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="0.129346" stop-color="#505050"/>
<stop offset="0.258158"/>
</linearGradient>
<linearGradient id="paint1_linear_1368_44" x1="-3.56538e-07" y1="85.5" x2="52.8432" y2="101.154" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="0.129346" stop-color="#505050"/>
<stop offset="0.258158"/>
</linearGradient>
</defs>
</svg>