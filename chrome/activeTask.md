# Anpassung der Scrollgeschwindigkeit des Apple Magic Trackpad unter Ubuntu 24.04.2 LTS

Die Steuerung der Scrollgeschwindigkeit eines Apple Magic Trackpad unter Ubuntu kann frustrierend sein, da die Standardeinstellungen in GNOME keine direkte Option zur Anpassung der Scrollgeschwindigkeit bieten. In diesem Bericht stelle ich verschiedene Methoden vor, mit denen Sie die Scrollgeschwindigkeit Ihres Apple Magic Trackpad unter Ubuntu 24.04.2 LTS anpassen können.

## Hauptlösung: Anpassung der virtuellen Touchpad-Größe mit libinput-tools

Die effektivste Methode für Ubuntu 24.04 mit Wayland ist die Verwendung von libinput-tools, um eine virtuelle Touchpad-Größe zu setzen. Dies funktioniert, weil die Scrollgeschwindigkeit indirekt mit der erkannten Größe des Touchpads zusammenhängt.

### Vorgehensweise:

1. **Installation des benötigten Pakets**:
   Öffnen Sie ein Terminal (Strg+Alt+T) und installieren Sie libinput-tools:
   ```
   sudo apt install libinput-tools
   ```

2. **Bestimmen der tatsächlichen Touchpad-Größe**:
   Führen Sie den folgenden Befehl aus:
   ```
   sudo libinput measure touchpad-size 100x100
   ```
   Das Ergebnis zeigt die tatsächliche Breite und Höhe Ihres Touchpads in Millimetern an. Notieren Sie diese Werte[2].

3. **Festlegen einer virtuellen Touchpad-Größe**:
   - Für langsameres Scrollen setzen Sie eine größere virtuelle Größe
   - Für schnelleres Scrollen setzen Sie eine kleinere virtuelle Größe

   Beispiel für 1,5-fache Scrollgeschwindigkeit (wenn Ihr Touchpad 114,6 mm × 48,4 mm groß ist):
   ```
   sudo libinput measure touchpad-size 172x73
   ```

   Beispiel für 0,8-fache Scrollgeschwindigkeit:
   ```
   sudo libinput measure touchpad-size 92x39
   ```

   **Wichtig**: Passen Sie die Werte entsprechend der tatsächlichen Größe Ihres Touchpads an. Die Berechnung ist einfach: originale Größe × gewünschter Faktor[2].

4. **Befolgen Sie die Anweisungen im Terminal**:
   Bewegen Sie einen Finger entlang aller Ränder des Touchpads, bis die erkannte Größe mit Ihrer angegebenen Größe übereinstimmt[2].

## Alternative Methode 1: xf86-input-mtrack (nur für Xorg)

Wenn Sie bereit sind, von Wayland zu Xorg zu wechseln, bietet der mtrack-Treiber umfangreichere Anpassungsmöglichkeiten für das Apple Magic Trackpad.

1. **Wechsel zu Xorg**:
   Melden Sie sich ab und wählen Sie "Ubuntu auf Xorg" in der Anmeldebildschirm-Sitzungsauswahl.

2. **Installation des Treibers**:
   ```
   sudo apt install xserver-xorg-input-mtrack
   ```

3. **Konfiguration erstellen**:
   Erstellen Sie eine Konfigurationsdatei unter `/usr/share/X11/xorg.conf.d/50-mtrack.conf` mit folgendem Inhalt (angepasst für Apple Magic Trackpad):
   ```
   Section "InputClass"
       MatchIsTouchpad "on"
       Identifier "Touchpads"
       Driver "mtrack"
       Option "Sensitivity" "0.55"
       Option "FingerHigh" "12"
       Option "FingerLow" "1"
       Option "IgnoreThumb" "true"
       Option "IgnorePalm" "true"
       Option "TapButton1" "0"
       Option "TapButton2" "0"
       Option "TapButton3" "0"
       Option "TapButton4" "0"
       Option "ClickFinger1" "1"
       Option "ClickFinger2" "3"
       Option "ClickFinger3" "3"
       Option "ButtonMoveEmulate" "false"
       Option "ButtonIntegrated" "true"
       Option "ClickTime" "25"
       Option "BottomEdge" "25"
       Option "SwipeLeftButton" "8"
       Option "SwipeRightButton" "9"
       Option "SwipeUpButton" "0"
       Option "SwipeDownButton" "0"
       Option "ScrollDistance" "75"    # Dieser Wert beeinflusst die Scrollgeschwindigkeit
   EndSection
   ```

   Passen Sie den Wert für "ScrollDistance" an, um die Scrollgeschwindigkeit zu ändern - kleinere Werte führen zu schnellerem Scrollen[5][13].

4. **Neustarten des X-Servers**:
   Melden Sie sich ab und wieder an, oder starten Sie den Computer neu.

## Alternative Methode 2: Anpassen der Firefox-Scrollgeschwindigkeit

Wenn Ihr Hauptproblem die Scrollgeschwindigkeit in Firefox ist, können Sie diese direkt im Browser anpassen:

1. Geben Sie `about:config` in die Adressleiste ein und bestätigen Sie die Warnung.
2. Suchen Sie nach `mousewheel.default.delta_multiplier_y`.
3. Ändern Sie den Wert (Standard ist 100) - höhere Werte für schnelleres Scrollen, niedrigere für langsameres Scrollen[1].

## Alternative Methode 3: imwheel (nur für Xorg)

Diese Methode funktioniert gut für Mäuse und kann auch mit dem Magic Trackpad unter Xorg verwendet werden:

1. **Installation von imwheel**:
   ```
   sudo apt install imwheel
   ```

2. **Konfiguration erstellen**:
   Erstellen Sie eine Datei `~/.imwheelrc` mit folgendem Inhalt:
   ```
   ".*"
   None, Up, Button4, 3
   None, Down, Button5, 3
   ```

   Die Zahl am Ende jeder Zeile bestimmt den Scrollfaktor - passen Sie sie an Ihre Bedürfnisse an[15].

3. **imwheel starten**:
   ```
   imwheel
   ```

## Alternative Methode 4: Benutzerdefinierte libinput-Quirks

Sie können auch benutzerdefinierte Quirks für Ihr Apple Magic Trackpad erstellen:

1. Erstellen Sie die Datei `/usr/share/libinput/local-overrides.quirks`:
   ```
   [Apple Magic Trackpad v2]
   MatchBus=bluetooth
   MatchVendor=0x004C
   MatchProduct=0x0265
   AttrTouchSizeRange=20:10
   AttrPressureRange=3:0
   ```

2. Diese Einstellungen beeinflussen hauptsächlich die Druckempfindlichkeit und können das Scrollerlebnis verbessern[9].

## Schlussfolgerung

Die Anpassung der Scrollgeschwindigkeit unter Ubuntu 24.04 ist nicht so einfach wie unter macOS, aber mit den beschriebenen Methoden können Sie ein angenehmeres Scrollerlebnis mit Ihrem Apple Magic Trackpad erreichen. Die erste Methode mit libinput-tools ist für die meisten Benutzer am zugänglichsten, da sie unter Wayland funktioniert, das in Ubuntu 24.04 standardmäßig verwendet wird.

Für eine umfassendere Kontrolle über Gesten und Scrollverhalten bietet der Wechsel zu Xorg mit dem mtrack-Treiber die meisten Anpassungsmöglichkeiten, erfordert jedoch mehr Konfigurationsarbeit und den Verzicht auf Wayland-spezifische Funktionen.

Beachten Sie, dass diese Einstellungen nach System-Updates möglicherweise zurückgesetzt werden müssen, insbesondere bei Kernel-Updates, die die libinput-Bibliothek betreffen.

Citations:
[1] https://combo.cc/posts/fixing-trackpad-scrolling-speed-on-firefox-linux/
[2] https://ubuntuhandbook.org/index.php/2023/05/adjust-touchpad-scrolling-ubuntu/
[3] https://www.reddit.com/r/Ubuntu/comments/ahgbdg/apple_magic_trackpad_2_configuration_on_kubuntu/
[4] https://askubuntu.com/questions/254847/how-do-i-adjust-the-pointer-speed-and-scrolling-speed-of-a-trackpad
[5] https://chris-reilly.org/blog/ubuntu-macbook-trackpad/
[6] https://superuser.com/questions/226300/apple-magic-mouse-on-ubuntu-slow-scrolling
[7] https://www.reddit.com/r/Ubuntu/comments/x9defo/is_there_anyway_to_slow_down_trackpad_scroll/
[8] https://www.reddit.com/r/Ubuntu/comments/1fkl7g4/2404_2410_gnome_trackpad_scrolling_speed/
[9] https://askubuntu.com/questions/1283762/custom-libinput-quirk-for-apple-magic-trackpad-2
[10] https://forum.parallels.com/threads/scrolling-in-linux-ubuntu-is-either-too-slow-or-too-fast.359412/
[11] https://askubuntu.com/questions/807940/how-to-enable-multi-touch-gestured-with-apple-magic-trackpad-on-ubuntu-16-04
[12] https://discussions.apple.com/thread/253744199
[13] https://int3ractive.com/blog/2018/make-the-best-of-MacBook-touchpad-on-Ubuntu/
[14] https://www.reddit.com/r/Ubuntu/comments/1gmj45o/apple_magic_trackpad_2024_usbc_version_not/
[15] https://superuser.com/questions/1730904/how-to-change-mouse-scrolling-speed-in-ubuntu
[16] https://askubuntu.com/questions/1413750/how-to-change-2-finger-touchpad-scroll-speed-on-ubuntu-22-04
[17] https://howchoo.com/linux/the-perfect-almost-touchpad-settings-on-linux-2/
[18] https://www.netzsofa.net/2020/01/05/apple-magicmouse-scroll_speed-und-ubuntu/
[19] https://community.frame.work/t/responded-ubuntu-22-04-trackpad-scroll-speed/40477/21
[20] https://www.sharpwriting.net/project/libinput-on-old-macbooks/
[21] https://forums.linuxmint.com/viewtopic.php?t=430692
[22] https://community.frame.work/t/responded-ubuntu-22-04-trackpad-scroll-speed/40477
[23] https://askubuntu.com/questions/tagged/scrolling?tab=Active
[24] https://community.frame.work/t/looking-for-help-adjusting-trackpad-scroll-speed-on-ubuntu/7080?page=2
[25] https://github.com/BlueDragonX/xf86-input-mtrack/issues/105
[26] https://discourse.ubuntu.com/t/requesting-to-add-scrolling-speed-for-both-mouse-touchpad-on-ubuntu-25-04-and-later/53035
[27] https://askubuntu.com/questions/1440249/macbook-trackpad-scroll-speed
[28] https://unix.stackexchange.com/questions/425913/apple-magictrackpad2-on-ubuntu
[29] https://github.com/mwyborski/Linux-Magic-Trackpad-2-Driver/issues/38
[30] https://forum.parallels.com/threads/scrolling-in-linux-ubuntu-is-either-too-slow-or-too-fast.359412/page-2

---
Antwort von Perplexity: pplx.ai/share