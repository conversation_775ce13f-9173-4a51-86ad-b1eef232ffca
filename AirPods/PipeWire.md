Um Apple AirPods mit einem Ubuntu Linux Desktop zu verbinden, gibt es verschiedene Methoden und Werkzeuge, wobei die grundlegende Verbindung über Standard-Bluetooth erfolgt. Es gibt keine offiziellen Apple-Tools für Linux.

## Standard-Bluetooth-Verbindung

Die einfachste Methode ist die normale Bluetooth-Kopplung[4]:
1.  Öffnen Sie das AirPods-Case, lassen Sie die AirPods darin[1][2].
2.  Halten Sie die Taste auf der Rückseite des Cases gedrückt, bis die LED weiß blinkt, um den Pairing-Modus zu aktivieren[1][2][4].
3.  <PERSON>ffnen Sie die Bluetooth-Einstellungen auf Ihrem Ubuntu-Desktop.
4.  Suchen Sie nach neuen Geräten und wählen Sie Ihre AirPods aus der Liste aus, um sie zu koppeln[1][2][4].

## Fehlerbehebung und Verbesserung der Verbindung

Falls Probleme bei der Verbindung oder mit der Audioqualität auftreten, sind folgende Schritte verbreitet:

**Anpassung der Bluetooth-Konfiguration**
Ein häufiger Lösungsansatz besteht darin, den Controller-Modus in der Bluetooth-Konfigurationsdatei zu ändern[1][2][5][7]:
1.  Öffnen Sie ein Terminal.
2.  Bearbeiten Sie die Datei `/etc/bluetooth/main.conf` mit Root-Rechten, z.B. mit `sudo nano /etc/bluetooth/main.conf`[1][2][6].
3.  Suchen Sie die Zeile `#ControllerMode = dual` (oder ähnlich).
4.  Entfernen Sie das `#`-Zeichen am Anfang der Zeile und ändern Sie den Wert von `dual` auf `bredr`[1][2][6][7]. Die Zeile sollte lauten: `ControllerMode = bredr`. BR/EDR steht für "Bluetooth Basic Rate/Enhanced Data Rate", auch bekannt als klassisches Bluetooth[7].
5.  Speichern Sie die Datei und schließen Sie den Editor (In Nano: Strg+X, dann 'y', dann Enter)[1][2].
6.  Starten Sie den Bluetooth-Dienst neu mit `sudo systemctl restart bluetooth`[5][7] oder `sudo /etc/init.d/bluetooth restart`[1][2].
7.  Versuchen Sie erneut, die AirPods zu koppeln[1][2][5].

**Wechsel zu PipeWire (Empfohlen für neuere Ubuntu-Versionen)**
Für eine verbesserte Audioqualität, Stabilität und potenziell funktionierende Mikrofoneingabe, besonders unter Ubuntu 22.04 und neuer, wird oft der Wechsel vom standardmäßigen PulseAudio-Audioserver zu PipeWire empfohlen[6][7].
1.  PipeWire ist in neueren Ubuntu-Versionen oft vorinstalliert, aber nicht unbedingt als Standard-Audioserver aktiv[6]. Überprüfen Sie den Status mit `systemctl --user status pipewire pipewire-session-manager`[6].
2.  Installieren Sie benötigte Pakete: `sudo apt install pipewire-audio-client-libraries libspa-0.2-bluetooth libspa-0.2-jack`[6].
3.  Installieren Sie den empfohlenen Session-Manager WirePlumber und entfernen Sie den alten: `sudo apt install wireplumber pipewire-media-session-`[6].
4.  Kopieren Sie Konfigurationsdateien:
    ```bash
    sudo cp /usr/share/doc/pipewire/examples/alsa.conf.d/99-pipewire-default.conf /etc/alsa/conf.d/
    sudo cp /usr/share/doc/pipewire/examples/ld.so.conf.d/pipewire-jack-*.conf /etc/ld.so.conf.d/
    sudo ldconfig
    ```

5.  Entfernen Sie das PulseAudio Bluetooth-Modul: `sudo apt remove pulseaudio-module-bluetooth`[6].
6.  (Optional, falls weiterhin Probleme bestehen) Bearbeiten Sie `/etc/bluetooth/main.conf` wie oben beschrieben, um `ControllerMode = bredr` zu setzen[6].
7.  Aktivieren Sie den WirePlumber-Dienst: `systemctl --user --now enable wireplumber.service`[6].
8.  Starten Sie Ihr System neu[6]. Danach sollten sich die AirPods über die normalen Einstellungen verbinden lassen und besser funktionieren[6][7].

## Benötigte Software und Werkzeuge

*   **bluez:** Der Kern-Bluetooth-Stack für Linux. Normalerweise standardmäßig installiert, kann aber mit `sudo apt-get install bluez*` sichergestellt werden[5].
*   **Bluetooth-Manager:** Die meisten Ubuntu-Desktop-Umgebungen enthalten einen grafischen Bluetooth-Manager in den Systemeinstellungen[4]. Alternativ kann Blueman installiert werden (`sudo apt-get install blueman`)[5][4]. Für die Kommandozeile gibt es `bluetoothctl`[4].
*   **PipeWire/WirePlumber:** Wie oben beschrieben, für verbesserte Audioverarbeitung[6][7].

## Zusätzliche Software von Drittanbietern

*   **AirPodsDesktop:** Ein Open-Source-Projekt auf GitHub, das darauf abzielt, die Benutzererfahrung mit AirPods auf Desktop-Systemen (Windows und Linux) zu verbessern. Es bietet Funktionen wie Batterieanzeige, automatische Ohrerkennung und einen Modus mit geringer Audiolatenz. Das Projekt ist jedoch noch in Entwicklung ("Work In Progress")[8].

## Einschränkungen

Obwohl die grundlegende Audioausgabe und oft auch die Mikrofoneingabe (insbesondere mit PipeWire) funktionieren, sind erweiterte Funktionen der AirPods, wie z.B. die Konfiguration der Geräuschunterdrückung oder 3D-Audio, unter Linux möglicherweise nicht verfügbar oder konfigurierbar, da dies oft eine Verbindung mit einem Apple-Gerät erfordert[3][4].

Citations:
[1] https://gist.github.com/aidos-dev/b49078c1d8c6bb1621e4ac199d18213b
[2] https://gist.github.com/aidos-dev/b49078c1d8c6bb1621e4ac199d18213b?permalink_comment_id=4636484
[3] https://www.youtube.com/watch?v=JX8kSWkP2xc
[4] https://www.gutefrage.net/frage/wie-verbinde-ich-meine-air-pods-pro-2-mit-linux
[5] https://korkutozcan.com/pair-airpods-with-linux-ubuntu/
[6] https://jkfran.com/pipewire-airpods-ubuntu-jammy-jellyfish-22.04/
[7] https://www.youtube.com/watch?v=3TJDgIveiI8
[8] https://github.com/SpriteOvO/AirPodsDesktop
[9] https://www.computerbase.de/forum/threads/apple-airpods-2th-mit-ubuntu.1899523/
[10] https://www.reddit.com/r/linuxmint/comments/vqvkdm/airpods_and_linux_mint/
[11] https://wiki.ubuntuusers.de/Bluetooth/Einrichtung/
[12] https://www.youtube.com/watch?v=3TJDgIveiI8
[13] https://mil.ad/blog/2024/airpods-on-arch.html
[14] https://www.c-jump.com/?c=321194912
[15] https://askubuntu.com/questions/922860/pairing-apple-airpods-as-headset
[16] https://www.linkedin.com/pulse/how-connect-air-pods-ubuntu-deepak-kumar
[17] https://www.reddit.com/r/Ubuntu/comments/1c2xea7/connecting_airpods_pro_to_ubuntu/?tl=de
[18] https://support.apple.com/de-de/108786
[19] https://askubuntu.com/questions/1240679/how-to-pairsetupconnect-airpod-pro-on-ubuntu-20-04
[20] https://www.reddit.com/r/linuxquestions/comments/d36a74/is_it_possible_to_get_airpods_working_with_linux/?tl=de
[21] https://www.reddit.com/r/linuxquestions/comments/d36a74/is_it_possible_to_get_airpods_working_with_linux/
[22] https://askubuntu.com/questions/1408647/unable-to-pair-airpods-pro-with-ubuntu-22-04
[23] https://forums.linuxmint.com/viewtopic.php?t=399249
[24] https://cri.dev/posts/2021-01-04-Pair-AirPods-with-Linux-Ubuntu/
[25] https://www.reddit.com/r/Ubuntu/comments/113unt1/ubuntu_wont_connect_to_bluetooth_earbuds/
[26] https://unix.stackexchange.com/questions/750785/bluetooth-issues-with-airpods-pro-2-on-debian-12
[27] https://discourse.ubuntu.com/t/bluetooth-earphones-have-to-be-paired-after-every-reboot/50338
[28] https://magicpods.app
[29] https://www.ionos.de/digitalguide/server/knowhow/bluetooth-verbindung-herstellen/
[30] https://apps.apple.com/us/app/ish-shell/id1436902243
[31] https://www.chip.de/news/Update-fuer-Apples-AirPods-Kopfgesten-und-mehr-sind-da_185468561.html
[32] https://www.golem.de/news/apple-android-und-linux-tastatureingaben-lassen-sich-per-bluetooth-einschleusen-2312-180117.html
[33] https://praxistipps.chip.de/itunes-auf-ubuntu-installieren-so-gehts_34872
[34] https://www.apple.com/airpods-pro/
[35] https://www.macuser.de/threads/tastatur-und-maus-unter-linux.932605/
[36] https://forum.linuxguides.de/index.php?thread%2F7741-bluetooth-mit-apple-wireless-keyboard-a1314%2F
[37] https://discussions.apple.com/thread/251159387
[38] https://gist.github.com/aidos-dev/b49078c1d8c6bb1621e4ac199d18213b?permalink_comment_id=4790262
[39] https://discussions.apple.com/thread/254396923
[40] https://www.gutefrage.net/frage/wie-verbinde-ich-meine-air-pods-pro-2-mit-linux
[41] https://meroupatate.github.io/posts/airpods/
[42] https://www.youtube.com/watch?v=Jn-hurRuXu0
[43] https://www.ifun.de/jetzt-auch-bluetooth-linux-unterstuetzung-fuer-das-magic-keyboard-126360/
[44] https://support.apple.com/de-de/106340

---
Antwort von Perplexity: pplx.ai/share