# Probleme mit AirPods als Aufnahmegerät bei PipeWire

Das von Ihnen beobachtete Verhalten ist tatsächlich zu erwarten, da das "High Fidelity-Wiedergabe (A2DP-Senke)" Profil ausschließlich für Audioausgabe und nicht für Mikrofoneingang ausgelegt ist[4][6]. Um das Mikrofon nutzen zu können, müssen Sie auf ein Headset-Profil umschalten.

## Profil-Umschaltung für Mikrofonzugriff

Der A2DP-Modus, den Sie aktuell verwenden, bietet beste Audioqualität für die Wiedergabe, unterstützt aber keine Mikrofonfunktion[3][7]. Für die Mikrofonfunktionalität müssen Sie:

1. In den Soundeinstellungen zu Ihren AirPods navigieren
2. Das Profil von "High Fidelity-Wiedergabe (A2DP-Senke)" auf "Headset Head Unit (HSP/HFP)" umschalten[4][6]

In den Soundeinstellungen sollten Ihre AirPods als zwei separate Geräte erscheinen:
- **Headphone/Kopfhörer** (nur Audio-Ausgabe, A2DP)
- **Handsfree/Headset** (Audio-Ausgabe und Mikrofon, HSP/HFP)[6]

## Zusätzliche Konfigurationsschritte

Da Sie bereits PipeWire installiert haben, sollten Sie noch folgende Konfigurationen überprüfen:

1. **Codec-Unterstützung aktivieren**:
   Bearbeiten Sie die Datei `/etc/pipewire/media-session.d/bluez-monitor.conf` oder `/usr/share/pipewire/media-session.d/bluez-monitor.conf` (je nach Installation) und stellen Sie sicher, dass folgende Zeilen aktiviert (nicht auskommentiert) und auf `true` gesetzt sind[7]:
   ```
   bluez5.msbc-support = true
   bluez5.sbc-xq-support = true
   ```

2. **Bluetooth Controller-Modus**:
   Überprüfen Sie, ob in `/etc/bluetooth/main.conf` die Zeile `ControllerMode = bredr` eingestellt ist[1][8]

3. **WirePlumber aktivieren** (falls noch nicht geschehen):
   ```
   systemctl --user --now enable wireplumber.service
   ```
   
4. **System neustarten** nach diesen Änderungen

## Automatische Profilumschaltung

PipeWire mit WirePlumber sollte theoretisch automatisch das Profil wechseln, wenn eine Anwendung das Mikrofon benötigt[4]. Dies funktioniert jedoch nicht immer zuverlässig mit AirPods. Es gibt einige bekannte Probleme:

1. Die automatische Umschaltung funktioniert möglicherweise nicht mit allen Anwendungen
2. PipeWire hat ein bekanntes Problem, bei dem Headsets erst beginnen, Mikrofondaten zu senden, wenn Audiopakete in ihre Richtung gesendet werden[4]
3. Bei manchen AirPods-Modellen müssen Sie die Profilumschaltung manuell durchführen

## Begrenzungen und Qualitätshinweise

Beachten Sie, dass beim Umschalten auf das Headset-Profil (HSP/HFP) für die Mikrofonnutzung:

1. Die Audioqualität für die Wiedergabe deutlich schlechter wird (klingt komprimiert)[6][7]
2. A2DP (hohe Audioqualität) und HSP/HFP (Mikrofonnutzung) können nicht gleichzeitig verwendet werden[7]
3. Neuere Codecs wie mSBC oder SBC-XQ bieten bessere Qualität als der ältere CVSD-Codec, sind aber immer noch nicht mit A2DP-Qualität vergleichbar[7]

## Zusammenfassung

Der von Ihnen gesehene "High Fidelity-Wiedergabe (A2DP-Senke, Codec SBC-XQ)" Modus ist korrekt für hochwertige Audiowiedergabe, unterstützt aber keine Mikrofonfunktion. Für die Nutzung des Mikrofons müssen Sie auf "Headset Head Unit (HSP/HFP)" umschalten, was jedoch die Audioqualität vermindert. Dies ist eine Einschränkung der Bluetooth-Technologie und nicht spezifisch für Ubuntu oder PipeWire.

Citations:
[1] https://askubuntu.com/questions/922860/pairing-apple-airpods-as-headset
[2] https://askubuntu.com/questions/1247209/microphone-not-working-on-bluetooth-earbuds
[3] https://www.reddit.com/r/linux/comments/l9blkp/the_current_state_of_bluetooth_headsets_on_linux/?tl=de
[4] https://discussion.fedoraproject.org/t/no-audio-input-device-with-airpod-pros/96208
[5] https://www.youtube.com/watch?v=3TJDgIveiI8
[6] https://answers.launchpad.net/ubuntu/+question/705677
[7] https://www.commander1024.de/wordpress/2021/07/bluetooth-hifi-unter-linux/
[8] https://jkfran.com/pipewire-airpods-ubuntu-jammy-jellyfish-22.04/
[9] https://www.reddit.com/r/linuxquestions/comments/d36a74/is_it_possible_to_get_airpods_working_with_linux/
[10] https://github.com/sadanand-singh/reckoning.dev.comments/issues/4
[11] https://bbs.archlinux.org/viewtopic.php?id=294444
[12] https://bbs.archlinux.org/viewtopic.php?id=282119
[13] https://gist.github.com/the-spyke/2de98b22ff4f978ebf0650c90e82027e?permalink_comment_id=4320194
[14] https://www.reddit.com/r/Ubuntu/comments/1e1fbqi/no_sound_from_bluetooth_headphones_on_ubuntu_with/
[15] https://answers.launchpad.net/ubuntu/+question/705677
[16] https://discussion.fedoraproject.org/t/no-audio-input-device-with-airpod-pros/96208/2
[17] https://bbs.archlinux.org/viewtopic.php?id=290780
[18] https://gist.github.com/maestrow/f6a0169054226ae8051d42dbe48ba7d0
[19] https://discourse.ubuntu.com/t/sound-stopped-working-after-release-upgrade-to-24-04/51846
[20] https://discourse.ubuntu.com/t/bluetooth-degraded-audio-quality-resolved/27244
[21] https://askubuntu.com/questions/863930/bluetooth-headset-cant-set-a2dp-high-fidelity-playback-poor-sound-quality
[22] https://www.reddit.com/r/pipewire/comments/10zmwhn/a2dp_codec_not_working_with_airpod_pros_2/?tl=de
[23] https://bbs.archlinux.org/viewtopic.php?id=269195
[24] https://www.youtube.com/watch?v=3TJDgIveiI8
[25] https://pipewire.pages.freedesktop.org/wireplumber/daemon/configuration/bluetooth.html
[26] https://wiki.archlinux.org/title/Bluetooth_headset
[27] https://unix.stackexchange.com/questions/642381/impossible-to-set-a2dp-profile-on-bluetooth-headset-on-ubuntu-20-04
[28] https://www.reddit.com/r/Fedora/comments/p873dr/stop_pipewire_changing_profile_automatically/
[29] https://discourse.nixos.org/t/pipewire-bluetooth-audio-msbc-sbc-codec-not-selectable/17605
[30] https://askubuntu.com/questions/1004712/how-to-keep-the-audio-profile-at-a2dp-while-using-a-mic-with-bluetooth-headset
[31] https://wiki.archlinux.org/title/PipeWire
[32] https://forum.manjaro.org/t/switched-to-pipewire-no-a2dp-profile-for-headphones/72502
[33] https://www.reddit.com/r/Fedora/comments/1ascsre/pipewire_audio_input_device_suddenly_missing/
[34] https://unix.stackexchange.com/questions/750785/bluetooth-issues-with-airpods-pro-2-on-debian-12
[35] https://bbs.archlinux.org/viewtopic.php?id=297896
[36] https://archive.fosdem.org/2023/schedule/event/bt_pipewire/attachments/slides/5419/export/events/attachments/bt_pipewire/slides/5419/FOSDEM23_Bluetooth_in_PipeWire_and_WirePlumber.pdf
[37] https://gitlab.freedesktop.org/pipewire/pipewire/-/issues/3502
[38] https://www.reddit.com/r/pipewire/comments/10zmwhn/a2dp_codec_not_working_with_airpod_pros_2/
[39] https://developer.toradex.com/linux-bsp/application-development/multimedia/bluetooth-audio-linux/

---
Antwort von Perplexity: pplx.ai/share