Skip to main content

Manjaro Linux Forum
WIFI problem after latest update
Support
Network
broadcom

Log In
​
WIFI problem after latest update
Support
Network
broadcom

624
views

3
links



read
9 min
Aug 2024
Aug 2024

boborbt

2
Aug 2024
Hi there, after the latest update the wifi always fails to authenticate (it worked fine before that, and it works properly if I boot from the manjaro live distro). The card is up and running, I can see all routers around me, but the connection fails unless I disable authentication. If I keep the security of the network on, the system keeps me asking for the password and the icon in the top bar shows three dots inside the wifi symbol.

I’m working on an iMac, here is the output of inxi --admin --verbosity=8 -Fxxxz:

System:
  Kernel: 6.6.44-1-MANJARO arch: x86_64 bits: 64 compiler: gcc v: 14.1.1
    clocksource: tsc avail: hpet,acpi_pm
    parameters: BOOT_IMAGE=/boot/vmlinuz-6.6-x86_64
    root=UUID=c104ab9f-2f28-409e-83d3-fa06b5d6dbc0 rw splash apparmor=1
    security=apparmor udev.log_priority=3
  Desktop: GNOME v: 46.3.1 tk: GTK v: 3.24.43 wm: gnome-shell
    tools: gsd-screensaver-proxy dm: GDM v: 46.2 Distro: Manjaro
    base: Arch Linux
Machine:
  Type: Laptop System: Apple product: iMac18,3 v: 1.0
    serial: <superuser required> Chassis: type: 9 v: Mac-BE088AF8C5EB4FA2
    serial: <superuser required>
  Mobo: Apple model: Mac-BE088AF8C5EB4FA2 v: iMac18,3
    serial: <superuser required> uuid: <superuser required> UEFI: Apple
    v: 522.0.0.0.0 date: 08/17/2023
Battery:
  Message: No system battery data found. Is one present?
Memory:
  System RAM: total: 40 GiB available: 39.08 GiB used: 2.73 GiB (7.0%)
  Message: For most reliable report, use superuser + dmidecode.
  Array-1: capacity: 64 GiB note: est. slots: 4 modules: 4 EC: None
    max-module-size: 16 GiB note: est.
  Device-1: DIMM0 type: DDR4 detail: synchronous size: 4 GiB
    speed: 2400 MT/s volts: curr: 1 width (bits): data: 64 total: 64
    manufacturer: 0x802C part-no: 0x344154463531323634485A2D3247334232202020
    serial: <filter>
  Device-2: DIMM1 type: DDR4 detail: synchronous size: 16 GiB
    speed: 2400 MT/s volts: curr: 1 width (bits): data: 64 total: 64
    manufacturer: 0x859B part-no: 0x435431364734534644383234412E433136464444
    serial: <filter>
  Device-3: DIMM0 type: DDR4 detail: synchronous size: 4 GiB
    speed: 2400 MT/s volts: curr: 1 width (bits): data: 64 total: 64
    manufacturer: 0x802C part-no: 0x344154463531323634485A2D3247334232202020
    serial: <filter>
  Device-4: DIMM1 type: DDR4 detail: synchronous size: 16 GiB
    speed: 2400 MT/s volts: curr: 1 width (bits): data: 64 total: 64
    manufacturer: 0x859B part-no: 0x435431364734534644383234412E433136464444
    serial: <filter>
PCI Slots:
  Permissions: Unable to run dmidecode. Root privileges required.
CPU:
  Info: model: Intel Core i5-7500 bits: 64 type: MCP arch: Kaby Lake
    gen: core 7 level: v3 note: check built: 2018 process: Intel 14nm family: 6
    model-id: 0x9E (158) stepping: 9 microcode: 0xF8
  Topology: cpus: 1x cores: 4 smt: <unsupported> cache: L1: 256 KiB
    desc: d-4x32 KiB; i-4x32 KiB L2: 1024 KiB desc: 4x256 KiB L3: 6 MiB
    desc: 1x6 MiB
  Speed (MHz): avg: 800 min/max: 800/3800 scaling: driver: intel_pstate
    governor: powersave cores: 1: 800 2: 800 3: 800 4: 800 bogomips: 27208
  Flags: 3dnowprefetch abm acpi adx aes aperfmperf apic arat
    arch_capabilities arch_perfmon art avx avx2 bmi1 bmi2 bts clflush
    clflushopt cmov constant_tsc cpuid cpuid_fault cx16 cx8 de ds_cpl dtes64
    dtherm dts epb ept ept_ad erms est f16c flexpriority flush_l1d fma fpu
    fsgsbase fxsr ht hwp hwp_act_window hwp_epp hwp_notify ibpb ibrs ida
    intel_pt invpcid lahf_lm lm mca mce md_clear mmx monitor movbe mpx msr
    mtrr nonstop_tsc nopl nx pae pat pbe pcid pclmulqdq pdcm pdpe1gb pebs pge
    pln pni popcnt pse pse36 pti pts rdrand rdseed rdtscp rep_good sdbg sep
    smap smep smx ss ssbd sse sse2 sse4_1 sse4_2 ssse3 stibp syscall tm tm2
    tpr_shadow tsc tsc_adjust tsc_deadline_timer vme vmx vnmi vpid x2apic
    xgetbv1 xsave xsavec xsaveopt xsaves xtopology xtpr
  Vulnerabilities:
  Type: gather_data_sampling mitigation: Microcode
  Type: itlb_multihit status: KVM: VMX disabled
  Type: l1tf mitigation: PTE Inversion; VMX: conditional cache flushes, SMT
    disabled
  Type: mds mitigation: Clear CPU buffers; SMT disabled
  Type: meltdown mitigation: PTI
  Type: mmio_stale_data mitigation: Clear CPU buffers; SMT disabled
  Type: reg_file_data_sampling status: Not affected
  Type: retbleed mitigation: IBRS
  Type: spec_rstack_overflow status: Not affected
  Type: spec_store_bypass mitigation: Speculative Store Bypass disabled via
    prctl
  Type: spectre_v1 mitigation: usercopy/swapgs barriers and __user pointer
    sanitization
  Type: spectre_v2 mitigation: IBRS; IBPB: conditional; STIBP: disabled;
    RSB filling; PBRSB-eIBRS: Not affected; BHI: Not affected
  Type: srbds mitigation: Microcode
  Type: tsx_async_abort mitigation: TSX disabled
Graphics:
  Device-1: AMD Ellesmere [Radeon RX 470/480/570/570X/580/580X/590]
    vendor: Apple driver: amdgpu v: kernel arch: GCN-4 code: Arctic Islands
    process: GF 14nm built: 2016-20 pcie: gen: 3 speed: 8 GT/s lanes: 16
    ports: active: eDP-1 empty: DP-1,DP-2,DP-3 bus-ID: 01:00.0
    chip-ID: 1002:67df class-ID: 0300 temp: 53.0 C
  Device-2: Apple FaceTime HD Camera (Built-in) driver: uvcvideo type: USB
    rev: 2.0 speed: 480 Mb/s lanes: 1 mode: 2.0 bus-ID: 1-5:3 chip-ID: 05ac:8511
    class-ID: 0e02 serial: <filter>
  Display: wayland server: X.org v: ********* with: Xwayland v: 24.1.1
    compositor: gnome-shell driver: gpu: amdgpu display-ID: 0
  Monitor-1: eDP-1 model: Apple iMac serial: <filter> built: 2016
    res: 3840x2160 dpi: 163 gamma: 1.2 chroma: red: x: 0.682 y: 0.322 green:
    x: 0.255 y: 0.698 blue: x: 0.149 y: 0.055 white: x: 0.310 y: 0.329
    size: 597x336mm (23.5x13.23") diag: 685mm (27") ratio: 16:9
    modes: 3840x2160, 3200x1800, 2560x1440, 1920x1200, 1920x1080, 1600x1200,
    1680x1050, 1280x1024, 1440x900, 1280x800, 1280x720, 1024x768, 800x600,
    640x480
  API: EGL v: 1.5 hw: drv: amd radeonsi platforms: device: 0 drv: radeonsi
    device: 1 drv: swrast gbm: drv: kms_swrast surfaceless: drv: radeonsi
    wayland: drv: radeonsi x11: drv: radeonsi
  API: OpenGL v: 4.6 compat-v: 4.5 vendor: amd mesa v: 24.1.5-manjaro1.1
    glx-v: 1.4 direct-render: yes renderer: AMD Radeon RX 470 Graphics
    (radeonsi polaris10 LLVM 18.1.8 DRM 3.54 6.6.44-1-MANJARO)
    device-ID: 1002:67df memory: 3.91 GiB unified: no display-ID: :0.0
Audio:
  Device-1: Intel 100 Series/C230 Series Family HD Audio driver: snd_hda_intel
    v: kernel alternate: snd_soc_avs bus-ID: 00:1f.3 chip-ID: 8086:a170
    class-ID: 0403
  Device-2: AMD Ellesmere HDMI Audio [Radeon RX 470/480 / 570/580/590]
    driver: snd_hda_intel v: kernel pcie: gen: 3 speed: 8 GT/s lanes: 16
    bus-ID: 01:00.1 chip-ID: 1002:aaf0 class-ID: 0403
  API: ALSA v: k6.6.44-1-MANJARO status: kernel-api with: aoss
    type: oss-emulator tools: alsactl,alsamixer,amixer
  Server-1: JACK v: 1.9.22 status: off tools: N/A
  Server-2: PipeWire v: 1.2.2 status: off tools: pw-cli
  Server-3: PulseAudio v: 17.0 status: active with: pulseaudio-alsa
    type: plugin tools: pacat,pactl
Network:
  Device-1: Broadcom BCM43602 802.11ac Wireless LAN SoC vendor: Apple
    driver: brcmfmac v: kernel pcie: gen: 1 speed: 2.5 GT/s lanes: 1
    bus-ID: 03:00.0 chip-ID: 14e4:43ba class-ID: 0280
  IF: wlp3s0 state: up mac: <filter>
  IP v4: <filter> type: dynamic noprefixroute scope: global
    broadcast: <filter>
  IP v6: <filter> type: noprefixroute scope: link
  Device-2: Broadcom NetXtreme BCM57766 Gigabit Ethernet PCIe driver: tg3
    v: kernel pcie: gen: 1 speed: 2.5 GT/s lanes: 1 port: N/A bus-ID: 04:00.0
    chip-ID: 14e4:1686 class-ID: 0200
  IF: enp4s0f0 state: down mac: <filter>
  Info: services: NetworkManager, systemd-timesyncd, wpa_supplicant
  WAN IP: <filter>
Bluetooth:
  Device-1: Apple Bluetooth USB Host Controller
    driver: btusb,hid-generic,usbhid type: USB rev: 2.0 speed: 12 Mb/s lanes: 1
    mode: 1.1 bus-ID: 1-6:4 chip-ID: 05ac:8296 class-ID: fe01
  Report: rfkill ID: hci0 rfk-id: 0 state: up address: see --recommends
Logical:
  Message: No logical block device data found.
RAID:
  Message: No RAID data found.
Drives:
  Local Storage: total: 1016.47 GiB used: 66.1 GiB (6.5%)
  SMART Message: Required tool smartctl not installed. Check --recommends
  ID-1: /dev/nvme0n1 maj-min: 259:0 vendor: Apple model: SSD SM0032L
    size: 26.08 GiB block-size: physical: 4096 B logical: 4096 B
    speed: 15.8 Gb/s lanes: 2 tech: SSD serial: <filter> fw-rev: DXZ77A0Q
    temp: 35.9 C scheme: GPT
  ID-2: /dev/sda maj-min: 8:0 vendor: Apple model: HDD ST1000DM003
    size: 931.51 GiB block-size: physical: 4096 B logical: 512 B speed: 6.0 Gb/s
    tech: HDD rpm: 7200 serial: <filter> fw-rev: AQ04 scheme: GPT
  ID-3: /dev/sdb maj-min: 8:16 model: USB Flash size: 58.88 GiB block-size:
    physical: 512 B logical: 512 B type: USB rev: 3.1 spd: 5 Gb/s lanes: 1
    mode: 3.2 gen-1x1 tech: SSD serial: <filter> fw-rev: 1100 scheme: MBR
  Message: No optical or floppy data found.
Partition:
  ID-1: / raw-size: 25.75 GiB size: 25.18 GiB (97.77%) used: 14.74 GiB (58.5%)
    fs: ext4 dev: /dev/nvme0n1p2 maj-min: 259:2 label: N/A
    uuid: c104ab9f-2f28-409e-83d3-fa06b5d6dbc0
  ID-2: /boot/efi raw-size: 300 MiB size: 299.3 MiB (99.76%)
    used: 3.3 MiB (1.1%) fs: vfat dev: /dev/nvme0n1p1 maj-min: 259:1 label: N/A
    uuid: C7D3-7D7E
  ID-3: /mnt/bigdisk raw-size: 730.3 GiB size: 717.78 GiB (98.28%)
    used: 40.04 GiB (5.6%) fs: ext4 dev: /dev/sda2 maj-min: 8:2 label: data
    uuid: 2b1a0cbb-018c-4423-84f1-be1b1a0e0d24
  ID-4: /usr raw-size: 201.21 GiB size: 196.99 GiB (97.90%)
    used: 11.32 GiB (5.7%) fs: ext4 dev: /dev/sda1 maj-min: 8:1 label: sysusr
    uuid: b703ee0f-6b1a-4a11-b2c5-2c84c04d5740
Swap:
  Alert: No swap data was found.
Unmounted:
  ID-1: /dev/sdb1 maj-min: 8:17 size: 3.33 GiB fs: iso9660
  ID-2: /dev/sdb2 maj-min: 8:18 size: 4 MiB fs: vfat label: MISO_EFI
    uuid: 76DC-D123
USB:
  Hub-1: 1-0:1 info: hi-speed hub with single TT ports: 16 rev: 2.0
    speed: 480 Mb/s (57.2 MiB/s) lanes: 1 mode: 2.0 chip-ID: 1d6b:0002
    class-ID: 0900
  Device-1: 1-3:2 info: Logitech M90/M100 Optical Mouse type: mouse
    driver: hid-generic,usbhid interfaces: 1 rev: 2.0
    speed: 1.5 Mb/s (183 KiB/s) lanes: 1 mode: 1.0 power: 98mA
    chip-ID: 046d:c05a class-ID: 0301
  Device-2: 1-5:3 info: Apple FaceTime HD Camera (Built-in) type: video
    driver: uvcvideo interfaces: 3 rev: 2.0 speed: 480 Mb/s (57.2 MiB/s)
    lanes: 1 mode: 2.0 power: 500mA chip-ID: 05ac:8511 class-ID: 0e02
    serial: <filter>
  Device-3: 1-6:4 info: Apple Bluetooth USB Host Controller
    type: keyboard,mouse,bluetooth driver: btusb,hid-generic,usbhid
    interfaces: 6 rev: 2.0 speed: 12 Mb/s (1.4 MiB/s) lanes: 1 mode: 1.1
    chip-ID: 05ac:8296 class-ID: fe01
  Device-4: 1-10:5 info: SONiX AMAZON MD005 Wired Keyboard
    type: keyboard,HID driver: hid-generic,usbhid interfaces: 2 rev: 2.0
    speed: 1.5 Mb/s (183 KiB/s) lanes: 1 mode: 1.0 power: 100mA
    chip-ID: 413d:2114 class-ID: 0300
  Hub-2: 2-0:1 info: super-speed hub ports: 10 rev: 3.0
    speed: 5 Gb/s (596.0 MiB/s) lanes: 1 mode: 3.2 gen-1x1 chip-ID: 1d6b:0003
    class-ID: 0900
  Hub-3: 3-0:1 info: hi-speed hub with single TT ports: 2 rev: 2.0
    speed: 480 Mb/s (57.2 MiB/s) lanes: 1 mode: 2.0 chip-ID: 1d6b:0002
    class-ID: 0900
  Hub-4: 4-0:1 info: super-speed hub ports: 2 rev: 3.1
    speed: 10 Gb/s (1.16 GiB/s) lanes: 1 mode: 3.2 gen-2x1 chip-ID: 1d6b:0003
    class-ID: 0900
  Device-1: 4-2:2 info: Silicon Motion - Taiwan (formerly Feiya ) Flash
    Drive type: mass storage driver: usb-storage interfaces: 1 rev: 3.1
    speed: 5 Gb/s (596.0 MiB/s) lanes: 1 mode: 3.2 gen-1x1 power: 504mA
    chip-ID: 090c:1000 class-ID: 0806
Sensors:
  System Temperatures: cpu: 55.0 C mobo: N/A gpu: amdgpu temp: 53.0 C
  Fan Speeds (rpm): N/A
Repos:
  Packages: pm: pacman pkgs: 1557 libs: 459 tools: gnome-software,pamac
    pm: flatpak pkgs: 0
  Active pacman repo servers in: /etc/pacman.d/mirrorlist
    1: https://mirror.raiolanetworks.com/manjaro/stable/$repo/$arch
    2: https://mirror.futureweb.be/manjaro/stable/$repo/$arch
    3: https://mirror.zetup.net/manjaro/stable/$repo/$arch
    4: https://ftp.lysator.liu.se/pub/manjaro/stable/$repo/$arch
    5: https://southfront.mm.fcix.net/manjaro/stable/$repo/$arch
    6: https://muug.ca/mirror/manjaro/stable/$repo/$arch
    7: https://mirrors.ucr.ac.cr/manjaro/stable/$repo/$arch
    8: https://mirror.phoepsilonix.love/manjaro/stable/$repo/$arch
Processes:
  CPU top: 5 of 251
  1: cpu: 10.9% command: firefox pid: 2043 mem: 675.8 MiB (1.6%)
  2: cpu: 5.8% command: firefox pid: 2329 mem: 255.9 MiB (0.6%)
  3: cpu: 4.1% command: gnome-shell pid: 1372 mem: 318.9 MiB (0.7%)
  4: cpu: 2.9% command: firefox pid: 4272 mem: 182.5 MiB (0.4%)
  5: cpu: 0.7% command: firefox pid: 4133 mem: 164.7 MiB (0.4%)
  Memory top: 5 of 251
  1: mem: 675.8 MiB (1.6%) command: firefox pid: 2043 cpu: 10.9%
  2: mem: 318.9 MiB (0.7%) command: gnome-shell pid: 1372 cpu: 4.1%
  3: mem: 255.9 MiB (0.6%) command: firefox pid: 2329 cpu: 5.8%
  4: mem: 197.7 MiB (0.4%) command: firefox pid: 2424 cpu: 0.3%
  5: mem: 182.5 MiB (0.4%) command: firefox pid: 4272 cpu: 2.9%
Info:
  Processes: 251 Power: uptime: 17m states: freeze,mem,disk suspend: deep
    avail: s2idle wakeups: 0 hibernate: platform avail: shutdown, reboot,
    suspend, test_resume image: 15.61 GiB services: gsd-power,
    power-profiles-daemon, upowerd Init: systemd v: 256 default: graphical
    tool: systemctl
  Compilers: clang: 18.1.8 gcc: 14.1.1 Shell: Zsh v: 5.9
    running-in: gnome-terminal inxi: 3.3.35
and here there are a few lines from journalctl | wifi:

device (wlp3s0): Activation: (wifi) connection 'bbnet2' has security, and secrets exist.  No new secrets needed.
ago 17 11:50:08 esposito-imac183 NetworkManager[621]: <warn>  [1723888208.1662] device (wlp3s0): Activation: (wifi) association took too long
ago 17 11:50:08 esposito-imac183 NetworkManager[621]: <warn>  [1723888208.1666] device (wlp3s0): Activation: (wifi) asking for new secrets
ago 17 11:50:08 esposito-imac183 NetworkManager[621]: <info>  [1723888208.1677] device (wlp3s0): Activation: (wifi) connection 'bbnet2' has security, and secrets exist.  No new secrets needed.
ago 17 15:16:36 esposito-imac183 NetworkManager[605]: <info>  [1723900596.0256] Loaded device plugin: NMWifiFactory (/usr/lib/NetworkManager/1.48.6-1/libnm-device-plugin-wifi.so)
ago 17 15:16:41 esposito-imac183 NetworkManager[605]: <info>  [1723900601.1657] device (wlp3s0): Activation: (wifi) access point 'bbnet2' has security, but secrets are required.
Any idea about how to solve the problem? Thanks in advance!

Finally solved it. We were looking in the wrong direction. The driver was indeed working, but there is some problem with wpa_supplicant during authentication. I found the solution thanks to this post: T410 - Can't connect to wifi | WPA2 + WPA3 (mixed mode) - #22 by RedPanda Here is the passage con…

Moderator
 Jump to post
Device-1: Broadcom BCM43602 802.11ac Wireless LAN SoC vendor: Apple
    driver: brcmfmac v: kernel pcie: gen: 1 speed: 2.5 GT/s lanes: 1
    bus-ID: 03:00.0 chip-ID: 14e4:43ba class-ID: 0280
broadcom driver?

mhwd  --install pci network-broadcom-wl

megavolt
Moderator
Aug 2024
Device-1: Broadcom BCM43602 802.11ac Wireless LAN SoC vendor: Apple
    driver: brcmfmac v: kernel pcie: gen: 1 speed: 2.5 GT/s lanes: 1
    bus-ID: 03:00.0 chip-ID: 14e4:43ba class-ID: 0280
broadcom driver?

mhwd  --install pci network-broadcom-wl
then reboot to apply.


Teo
Aug 2024
There are several mac wifi topics, the most popular solutions are either the above suggested broadcom-wl package or there was some firmware package bm or bcm43. Test and tell us which one works on that mac.
https://aur.archlinux.org/packages/b43-firmware 8


boborbt

1
Aug 2024
mhwd --install pci network-broadcom-wl

Hi, thanks for your help! I’ve tried the command, but it spits out:

Warning: no matching device for config 'network-broadcom-wl' found!
> Installing network-broadcom-wl...
Sourcing /etc/mhwd-x86_64.conf
Has lib32 support: true
Sourcing /var/lib/mhwd/db/pci/network_drivers/broadcom-wl/MHWDCONFIG
Processing classid: 0200
Processing classid: 0280
Processing classid: 0282
:: Sincronizzazione dei database in corso...
download di core in corso...
download di extra in corso...
download di multilib in corso...
errore: impossibile scaricare il pacchetto 'core.db' da mirror.raiolanetworks.com : Connection time-out
errore: il seguente pacchetto non è stato trovato: linux65-broadcom-wl
Error: pacman failed!
Error: script failed!
By the way, I know for a fact that the brcmfmac driver works for this mac since it is the one used by the manjaro live distro where it works without a glitch.

As I was mentioning, the driver seems to be up and running, but then it fails to authenticate. May be I need some associated firmware to be installed along the driver?

Please also note that I am currently connected to the wifi and the driver works fine, but only because I’ve disabled authentication in my router. It really seems something that has to do with the authentication part (I really don’t know if that is handled by the driver or some other package).


boborbt

1
Aug 2024
mhwd --install pci network-broadcom-wl

Hi, thanks for your help! I’ve tried the command, but it spits out:

Warning: no matching device for config 'network-broadcom-wl' found!
> Installing network-broadcom-wl...
Sourcing /etc/mhwd-x86_64.conf
Has lib32 support: true
Sourcing /var/lib/mhwd/db/pci/network_drivers/broadcom-wl/MHWDCONFIG
Processing classid: 0200
Processing classid: 0280
Processing classid: 0282
:: Sincronizzazione dei database in corso...
download di core in corso...
download di extra in corso...
download di multilib in corso...
errore: impossibile scaricare il pacchetto 'core.db' da mirror.raiolanetworks.com : Connection time-out
errore: il seguente pacchetto non è stato trovato: linux65-broadcom-wl
Error: pacman failed!
Error: script failed!
By the way, I know for a fact that the brcmfmac driver works for this mac since it is the one used by the manjaro live distro where it works without a glitch.

As I was mentioning, the driver seems to be up and running, but then it fails to authenticate. May be I need some associated firmware to be installed along the driver?

Please also note that I am currently connected to the wifi and the driver works fine, but only because I’ve disabled authentication in my router. It really seems something that has to do with the authentication part (I really don’t know if that is handled by the driver or some other package).


megavolt
Moderator
Aug 2024
By the way, I know for a fact that the brcmfmac driver works for this mac since it is the one used by the manjaro live distro where it works without a glitch.

Well, then try an older kernel version.

mhwd-kernel --list
sudo mhwd-kernel --install linux61
Reboot and choose it at the grub boot menu.

Warning: no matching device for config 'network-broadcom-wl' found!

Is your Mac so old that even the proprietary driver doesn’t support this chipset? :thinking:

errore: impossibile scaricare il pacchetto 'core.db' da mirror.raiolanetworks.com : Connection time-out

Unstable internet connection? That’s why it doesn’t work.


boborbt
Aug 2024
Hi again and thanks for the continued support.

Tried with linux61, same behaviour :frowning:

In the meanwhile, I found this by dmesg | grep brcm:

[   12.927922] usbcore: registered new interface driver brcmfmac
[   13.036001] brcmfmac: brcmf_fw_alloc_request: using brcm/brcmfmac43602-pcie for chip BCM43602/1
[   13.174533] brcmfmac 0000:03:00.0: Direct firmware load for brcm/brcmfmac43602-pcie.Apple Inc.-iMac18,3.bin failed with error -2
[   13.205117] brcmfmac 0000:03:00.0: Direct firmware load for brcm/brcmfmac43602-pcie.txt failed with error -2
[   13.212024] brcmfmac 0000:03:00.0: Direct firmware load for brcm/brcmfmac43602-pcie.clm_blob failed with error -2
[   13.212218] brcmfmac 0000:03:00.0: Direct firmware load for brcm/brcmfmac43602-pcie.txcap_blob failed with error -2
[   13.747327] brcmfmac: brcmf_c_process_clm_blob: no clm_blob available (err=-2), device may have limited channels available
[   13.750092] brcmfmac: brcmf_c_process_txcap_blob: no txcap_blob available (err=-2)
[   13.761409] brcmfmac: brcmf_c_preinit_dcmds: Firmware: BCM43602/1 wl0: Nov 10 2015 06:38:10 version *********** (r598657) FWID 01-ea662a8c
[   13.831562] brcmfmac 0000:03:00.0 wlp3s0: renamed from wlan0
[   26.953506] ieee80211 phy0: brcmf_inetaddr_changed: fail to get arp ip table err:-52
[   56.907199] ieee80211 phy0: brcmf_inetaddr_changed: fail to get arp ip table err:-52
[  125.527590] ieee80211 phy0: brcmf_inetaddr_changed: fail to get arp ip table err:-52
which seems to suggest that there is some problem loading the firmware. No idea how to fix that though.

Also, the problem with ‘core.db’ was indeed temporary, it went away repeating the command. However, the main problem (the missing linux65-broadcom-wl package) persists, which id odd for two reasons:

pacman indeed cannot find it, and I cannot see in the AUR repo either (maybe I did something wrong during the search?)
the error was the same even when I switched to linux61. This is strange because I assume that linux65-broadcom-wl is tailored for linux65.
For what it concerns the flaky wifi connection: apperently the wifi signal is strong, I can download steadily at 45Mbps. Also, I have been able to upgrade to the unstable branch and then go back to stable branch without a hiccup in less than half an hour. The problem with core.db I assume was a fluke.

Unstable internet connection? That’s why it doesn’t work.

I don’t know, but I restate that the manjaro live distro does indeed work (and, as far as I can understand from the output of the inxi command, it is using the same brcmfmac driver I’m using right now).


megavolt
Moderator
Aug 2024
However, the main problem (the missing linux65-broadcom-wl package) persists, which id odd for two reasons:

pacman indeed cannot find it, and I cannot see in the AUR repo either (maybe I did something wrong during the search?)
the error was the same even when I switched to linux61. This is strange because I assume that linux65-broadcom-wl is tailored for linux65.
or 3. linux65 doesn’t exist anymore in the repo and therefore no wl module for this kernel. linux65 needs to be removed on your system.

[   13.174533] brcmfmac 0000:03:00.0: Direct firmware load for brcm/brcmfmac43602-pcie.Apple Inc.-iMac18,3.bin failed with error -2
[   13.205117] brcmfmac 0000:03:00.0: Direct firmware load for brcm/brcmfmac43602-pcie.txt failed with error -2
[   13.212024] brcmfmac 0000:03:00.0: Direct firmware load for brcm/brcmfmac43602-pcie.clm_blob failed with error -2
[   13.212218] brcmfmac 0000:03:00.0: Direct firmware load for brcm/brcmfmac43602-pcie.txcap_blob failed with error -2
That is strange… i can only find these:

$ LANG=C pamac search -f brcmfmac43602-pcie
/usr/lib/firmware/brcm/brcmfmac43602-pcie.ap.bin is owned by linux-firmware
/usr/lib/firmware/brcm/brcmfmac43602-pcie.bin is owned by linux-firmware
Could try this command on the live session?

find /usr/lib/firmware/brcm/ -name "*brcmfmac43602-pcie*" -exec sha1sum {} \;
Then compare the checksums with the same command on the local installation.

boborbt

1
Aug 2024
or 3. linux65 doesn’t exist anymore in the repo and therefore no wl module for this kernel. linux65 needs to be removed on your system.

Ok. I’ll look into it. Thanks.

Could try this command on the live session?

find /usr/lib/firmware/brcm/ -name "*brcmfmac43602-pcie*" -exec sha1sum {} \;
Then compare the checksums with the same command on the local installation.
[/quote]

Sure. The checksums are indeed different, but maybe they are only compressed versions of the same files (I’m guessing just looking at the extension of the reported files).

Here is the results:

# Output taken on the problematic installation
405aa0d8b9dd4dbca86639802f5592e94b795995  /usr/lib/firmware/brcm/brcmfmac43602-pcie.ap.bin
f88cfa6a3b86e055d76679b1f645886b3d42af4e  /usr/lib/firmware/brcm/brcmfmac43602-pcie.bin

# Output taken on the manjaro live distro
3120b1d638434746e54e23efe5c7227146c2ca4b  /usr/lib/firmware/brcm/brcmfmac43602-pcie.ap.bin.zst
7fc544c19b99940357aa09bde62c7c1210dc5e14  /usr/lib/firmware/brcm/brcmfmac43602-pcie.bin.zst
I almost forgot: the dmesg on the live distro reports the following:

[   11.387363] usbcore: registered new interface driver brcmfmac
[   11.495654] brcmfmac: brcmf_fw_alloc_request: using brcm/brcmfmac43602-pcie for chip BCM43602/1
[   11.565045] brcmfmac 0000:03:00.0: Direct firmware load for brcm/brcmfmac43602-pcie.Apple Inc.-iMac18,3.bin failed with error -2
[   11.653530] brcmfmac 0000:03:00.0: Direct firmware load for brcm/brcmfmac43602-pcie.txt failed with error -2
[   11.655406] brcmfmac 0000:03:00.0: Direct firmware load for brcm/brcmfmac43602-pcie.clm_blob failed with error -2
[   11.655556] brcmfmac 0000:03:00.0: Direct firmware load for brcm/brcmfmac43602-pcie.txcap_blob failed with error -2
[   14.459132] brcmfmac: brcmf_c_process_clm_blob: no clm_blob available (err=-2), device may have limited channels available
[   14.459135] brcmfmac: brcmf_c_process_txcap_blob: no txcap_blob available (err=-2)
[   14.459932] brcmfmac: brcmf_c_preinit_dcmds: Firmware: BCM43602/1 wl0: Nov 10 2015 06:38:10 version *********** (r598657) FWID 01-ea662a8c
[   14.538973] brcmfmac 0000:03:00.0 wlp3s0: renamed from wlan0
[   85.613298] ieee80211 phy0: brcmf_inetaddr_changed: fail to get arp ip table err:-52
which is much similar to what I reported earlier on the main system. Maybe that was a red herring?


boborbt
Aug 2024
or 3. linux65 doesn’t exist anymore in the repo and therefore no wl module for this kernel. linux65 needs to be removed on your system.

Ok. I’ll look into it. Thanks.

Update: you were right, the problem was that linux65 was installed. I removed it and then mhwd --install pci network-broadcom-wl succeeded.

Unfortunately this worsened the problem: after rebooting the wifi icon in the top-bar was not present anymore and the system settings panel for the network reported that no wifi adapter were to be found.

I uninstalled it and now eveything is back to normal (i.e., wifi works only without authentication). :frowning:

I’m loosing hope to solve the problem and wonder if wouldn’t it be better to reinstall the whole system (which is not a piece of cake: the imac has an hybrid drive and so it has a very peculiar partition scheme, where /usr is mounted on a separate partition, which was not trivial to do).


Aug 2024
Probably related to this problem: https://wiki.archlinux.org/title/Broadcom_wireless#No_5GHz_for_BCM4360_(14e4:43a0)_/BCM43602(14e4:43ba)_devices 14

Other than that, no idea. The dmesg is the same on live and local. wl driver doesn’t work. :man_shrugging:


boborbt
Aug 2024
Finally solved it. We were looking in the wrong direction. The driver was indeed working, but there is some problem with wpa_supplicant during authentication.

I found the solution thanks to this post: T410 - Can't connect to wifi | WPA2 + WPA3 (mixed mode) - #22 by RedPanda 41

Here is the passage containing the solution:

It seems like there is a problem with the wpa_supplicant package regarding connecting to WPA2 + WPA3 (mixed mode) wifi’s. As soon as i installed the packages iwd and networkmanager-iwd-overlay (AUR), removing wpa_supplicant and then rebooting i was able to connect to my wifi without a problem.

