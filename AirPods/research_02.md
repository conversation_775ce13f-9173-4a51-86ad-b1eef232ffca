# Analyse der HSP/HFP-Probleme mit AirPods Pro und Broadcom BCM4360 unter Ubuntu

Die Kombination aus Broadcom BCM4360 Bluetooth-Controller und AirPods Pro führt unter Linux zu bekannten Herausforderungen bei der Nutzung des HSP/HFP-Profils. Die folgenden Erkenntnisse basieren auf der Hardware-Konfiguration und den recherchierten Lösungsansätzen:

## 1. Hardware-Spezifische Einschränkungen
Der Broadcom BCM4360-Chip weist folgende Besonderheiten auf:
- **Apple-spezifische Implementierung**: Trotz Broadcom-Grunddesign enthält die Firmware Apple-spezifische Erweiterungen[5][12]
- **Dual-Use-Architektur**: Derselbe Chip verwaltet sowohl WLAN als auch Bluetooth, was Ressourcenkonflikte verursachen kann[12]
- **Firmware-Abhängigkeit**: Benötigt proprietäre Firmware-Dateien (`brcmfmac43602-pcie.bin`), die nicht in allen Linux-Distributionen enthalten sind[5][12]

## 2. Kernel- und Treiberkompatibilität
Aktuelle Lösungsansätze für Kernel 6.x:
- **Kernel-Parameter**: `btusb.enable_autosuspend=0` behebt Verbindungsabbrüche[8]
- **Modulblacklisting**: `echo "blacklist btusb" | sudo tee /etc/modprobe.d/btusb-blacklist.conf` bei persistierenden Timeouts[8]
- **Kernel-Downgrade**: Bei Versionen >6.13 empfiehlt sich ein Rollback auf 6.12-LTS[8]

```bash
# Kernel-Version prüfen
uname -r
# Verfügbare Kernel anzeigen
apt list --installed | grep linux-image
```

## 3. PipeWire-Konfigurationsoptimierung
Für AirPods Pro notwendige Einstellungen in `/etc/pipewire/media-session.d/bluez-monitor.conf`:
```ini
bluez5.msbc-support = true
bluez5.sbc-xq-support = true
bluez5.auto-connect = hfp_hf
bluez5.hfphsp-backend = ofono
```

**Wichtiger Hinweis**: Das gleichzeitige Aktivieren von mSBC und AAC kann zur Profil-Deaktivierung führen[14]. Empfohlene Workflow-Optimierung:
1. A2DP für Medienwiedergabe
2. Automatischer Wechsel zu HSP/HFP bei Aktivierung des Mikrofons
```bash
# Manuelle Profilumschaltung erzwingen
pactl set-card-profile bluez_card.XX_XX_XX_XX_XX_XX headset-head-unit
```

## 4. Broadcom-Firmware-Update
Für BCM4360 notwendige Schritte:
1. Firmware-Datei von [Plugable](https://plugable.com/2014/06/23/plugable-usb-bluetooth-adapter-solving-hfphsp-profile-issues-on-linux) herunterladen
2. Existierende Firmware sichern:
```bash
sudo cp /lib/firmware/brcm/brcmfmac43602-pcie.bin ~/brcmfmac43602-pcie.bin.bak
```
3. Neue Firmware installieren:
```bash
sudo wget -O /lib/firmware/brcm/brcmfmac43602-pcie.bin [URL_ZUR_FIRMWARE]
```
4. Modul neu laden:
```bash
sudo modprobe -r brcmfmac && sudo modprobe brcmfmac
```

## 5. Bluetooth-Stack-Optimierungen
Erweiterte Einstellungen in `/etc/bluetooth/main.conf`:
```ini
ControllerMode = bredr
FastConnectable = true
ReconnectAttempts = 7
AutoEnable = true
Disable=Socket
```

**Debugging-Tipp**: Echtzeit-Monitoring der Bluetooth-Protokolle:
```bash
sudo btmon | tee bluetooth_debug.log
```

## 6. Qualitätsverbesserungen im HSP/HFP-Modus
Trotz der technischen Limitierungen lassen sich folgende Verbesserungen erreichen:
1. **Codec-Forcierung**:
```bash
# mSBC mit 16 kHz Sampling-Rate
pactl set-port-latency-offset bluez_input.XX_XX_XX_XX_XX_XX 25000
```
2. **Rauschunterdrückung** mittels `noise-suppression-for-voice`:
```bash
pip install noise-suppression-for-voice
```
3. **Equalizer-Einstellungen** für verbesserten Frequenzgang:
```ini
# In /etc/pulse/equalizer.conf
channel=both
filter="0.0:0.8;0.1:0.7;0.2:0.6;0.3:0.5;0.4:0.4"
```

## 7. Alternative Lösungsansätze
Für persistierende Probleme:
- **USB-Bluetooth-Dongle**: Verwendung eines externen Adapters mit CSR-Chipset
- **Kernel-Patching**: Manuelle Anwendung von [Broadcom-Patches](https://github.com/linux-surface/linux-surface/tree/master/patches/5.19)
- **QEMU-Passthrough**: Virtualisierung von macOS zur exklusiven Bluetooth-Nutzung

```bash
# Beispiel für USB-Dongle-Konfiguration
lsusb -v -d 0a12:0001 | grep -i bcdDevice
```

## 8. Systemweite Diagnostik
Umfassendes Debugging-Skript:
```bash
#!/bin/bash
{
    date
    echo "### Kernel Version ###"
    uname -a
    echo "### Bluetooth Stack ###"
    bluetoothctl --version
    systemctl status bluetooth
    echo "### PipeWire Status ###"
    pipewire --version
    systemctl --user status pipewire
    echo "### Hardware Info ###"
    lspci -nnk | grep -iA2 net
    lsusb
    echo "### Firmware Checks ###"
    md5sum /lib/firmware/brcm/brcmfmac43602-pcie.bin
    echo "### Runtime Config ###"
    dmesg | grep -i blue
    journalctl -u bluetooth --since "5 minutes ago"
} > system_debug_report.txt
```

Diese umfassenden Maßnahmen berücksichtigen die spezifische Hardware-Konfiguration und bieten strukturierte Lösungswege für die HSP/HFP-Problematik. Die Implementierung erfordert jedoch fortgeschrittene Linux-Kenntnisse und sollte schrittweise erfolgen[1][5][8][14].

Citations:
[1] https://gitlab.freedesktop.org/pipewire/pipewire/-/issues/2689
[2] https://unix.stackexchange.com/questions/642381/impossible-to-set-a2dp-profile-on-bluetooth-headset-on-ubuntu-20-04
[3] https://www.youtube.com/watch?v=3TJDgIveiI8
[4] https://unix.stackexchange.com/questions/672584/how-to-make-a-hsp-hfp-bluetooth-profile-work-with-pulseaudio
[5] https://unix.stackexchange.com/questions/175810/how-to-install-broadcom-bcm4360-on-debian-on-macbook-pro
[6] https://support.apple.com/de-de/106340
[7] https://askubuntu.com/questions/922860/pairing-apple-airpods-as-headset
[8] https://discussion.fedoraproject.org/t/kernel-6-13-bluetooth-headphnes-a2dp-hsp-hfp-profile-switch-is-broken/146222
[9] https://www.reddit.com/r/linuxquestions/comments/myyib1/hsphfp_low_quality_audio_on_bluetooth_headphones/
[10] https://gist.github.com/aidos-dev/b49078c1d8c6bb1621e4ac199d18213b
[11] https://answers.microsoft.com/en-us/windows/forum/all/bluetooth-headset-connecting-to-music-profile-not/dc4d07dd-4387-48e5-85ef-6a283fa3f359
[12] https://www.reddit.com/r/linuxmint/comments/y5tae8/macbook_air_broadcom_bcm4360_kernel_adjustment_on/
[13] https://discussion.fedoraproject.org/t/airpods-pro-gen-1-not-working-under-hsp-hfp-codec/134415
[14] https://gitlab.freedesktop.org/pipewire/pipewire/-/issues/859
[15] https://askubuntu.com/questions/1104723/hsp-hfp-doesnt-work-on-ubuntu-18-04-1-lts-with-bluetooth-headphones
[16] https://www.reddit.com/r/archlinux/comments/1iuxka0/hfphsp_bluetooth_profile_with_apple_airpods/?tl=de
[17] https://www.reddit.com/r/linuxquestions/comments/d36a74/is_it_possible_to_get_airpods_working_with_linux/?tl=de
[18] https://jkfran.com/pipewire-airpods-ubuntu-jammy-jellyfish-22.04/
[19] https://osxlatitude.com/forums/topic/2925-bluetooth-firmware-uploader/
[20] https://bugs.launchpad.net/ubuntu/+source/pulseaudio/+bug/405294/+index?comments=all
[21] https://www.reddit.com/r/linux4noobs/comments/o7ik25/arch_bluetooth_headphones_cant_use_a2dp_profiles/
[22] https://askubuntu.com/questions/1260753/no-wifi-networks-listed-ubuntu-20-04-broadcom-bcm4360
[23] https://askubuntu.com/questions/636566/bluetooth-hsp-hfp-not-working-but-a2dp-is
[24] https://forum.manjaro.org/t/howto-post-screenshots-and-links/16378
[25] https://askubuntu.com/questions/777273/bcm43602-bluetooth-not-working-sees-device-mac-but-wont-pair
[26] https://www.reddit.com/r/Ubuntu/comments/hp460y/headset_mode_is_now_working_with_airpods_pro/
[27] https://forums.linuxmint.com/viewtopic.php?t=260636
[28] https://discussion.fedoraproject.org/t/airpods-pro-gen-1-not-working-under-hsp-hfp-codec/134415
[29] https://www.reddit.com/r/linuxquestions/comments/d36a74/is_it_possible_to_get_airpods_working_with_linux/
[30] https://reckoning.dev/blog/airpods-pro-ubuntu/
[31] https://www.tonymacx86.com/threads/wifi-fenvi-broadcom-bcm4360-on-ventura-13-6-1.328002/page-3
[32] https://bbs.archlinux.org/viewtopic.php?id=261297
[33] https://gitlab.freedesktop.org/pipewire/pipewire/-/issues/859
[34] https://gist.github.com/torresashjian/e97d954c7f1554b6a017f07d69a66374
[35] https://www.reddit.com/r/archlinux/comments/15ls3ul/which_driver_is_needed_for_broadcom_bcm4360/?tl=de
[36] https://forum.garudalinux.org/t/broadcom-bcm4360-wifi-not-working-after-initial-install/6666
[37] https://bbs.archlinux.org/viewtopic.php?id=249038
[38] https://apple.stackexchange.com/questions/390085/do-apple-airpods-support-hsp
[39] https://gist.github.com/aidos-dev/b49078c1d8c6bb1621e4ac199d18213b
[40] https://bbs.archlinux.org/viewtopic.php?id=262051
[41] https://askubuntu.com/questions/1382301/problem-with-wifi-drivers-ubuntu-20-04-lts-on-bcm4360-802-11ac-14e443a0-rev
[42] https://unix.stackexchange.com/questions/750785/bluetooth-issues-with-airpods-pro-2-on-debian-12
[43] https://askubuntu.com/questions/1328334/bluetooth-headset-hsp-hfp-sound-not-working-but-microphone-is
[44] https://blog.fernvenue.com/archives/airpods-pro-2-on-debian/
[45] https://discussion.fedoraproject.org/t/kernel-6-13-bluetooth-headphnes-a2dp-hsp-hfp-profile-switch-is-broken/146222
[46] https://bbs.archlinux.org/viewtopic.php?id=290780
[47] https://access.redhat.com/discussions/7059446
[48] https://gitlab.freedesktop.org/pipewire/pipewire/-/issues/2689
[49] https://debianforum.de/forum/viewtopic.php?t=146369
[50] https://gitlab.freedesktop.org/pipewire/pipewire/-/issues/2104
[51] https://www.youtube.com/watch?v=3TJDgIveiI8

---
Antwort von Perplexity: pplx.ai/share