
Pages:1
#12025-01-12 09:54:36
bifflodude
Member
Registered: 2025-01-12
Posts: 2
I'm having trouble getting my WiFi to work after an installation of Arch Linux and I'm out of ideas on how to fix it.

First I tried using 'iw' within the Live ISO environment to connect and got a message: Operation failed

    Searched for the error and came across this thread: Bugzilla thread: Bug 193121. The solution was to put a firmware related file into the "/lib/firmware/brcm/" directory
    I put the "brcmfmac43602-pcie.txt" file into the live ISO Env. and it worked ... while in the Live ISO. I copied over the "brcmfmac43602-pcie.txt" into the new install filesystem and rebooted
    Try to connect, and it fails.

I later tried a few variations of the "brcmfmac43602-pcie.txt" file from the Bugzilla thread, various github guides, etc.
    None of them seem to work.

Reading the journalctl I noticed a missing "brcm/brcmfmac43602-pcie.Apple\ Inc.-MacBookPro13,3.bin" file (I later discovered that these 'errors' aren't critical, it's just searching for firmware, it gets the firmware from the existing .ap.bin.zst, .bin.zst, and .txt files)
    I thought I needed to suppress the warning so I created the symlink:

/lib/firmware/brcm/brcmfmac43602-pcie.Apple\ Inc.-MacBookPro13,3.bin" -> /lib/firmware/brcm/brcmfmac43602.bin.zst
    and reloaded the brcmfmac brcmfmac_wcc modules using modprobe. Checking the journalctl, this resulted in the firmware crashing or 'haulting', so i removed the brcmfmac module and removed the symlink
    I then loaded brcmfmac and ran nmcli to connect. And it connected! This is the only way it would connect, but the issue persisted after a restart.

I did further testing on how this firmware halt related to changing the "brcmfmac43602-pcie.txt" file. I found out that the only txt file that worked was the one suggested in Bugzilla thread: Bug 193121
   
The only reliable way to get a connection after a restart was to:
       

Ensure the txt file from the Bugzilla thread is in /lib/firmware/brcm/

rmmod brcmfmac_wcc brcmfmac
Create the symlink

/lib/firmware/brcm/brcmfmac43602.bin.zst -> /lib/firmware/brcm/brcmfmac43602-pcie.Apple\ Inc.MacBookPro.-13,3.bin
(I think this can be any file, its just to crash the firmware or trigger some sort of reset)

modprobe brcmfmac
firmware crashes...

rmmod brcmfmac
rm "/lib/firmware/brcm/brcmfmac43602-pcie.Apple\ Inc.-MacBookPro13,3.bin"
modprobe brcmfmac
nmcli con up <wifi connection name>
and it instantly connects

The resulting connection isn't that great though. Moving away more than ~30 feet from the router degrades the signal to almost nothing.

I've also tried to turn off randomized mac addresses for connection in the NetworkManager config. This didn't help.

I also checked that my WiFi is indeed 2Ghz, so I don't think No 5GHz for BCM4360 (14e4:43a0) / BCM43602 (14e4:43ba) devices applies.

I know this is a common problem that others have figured out a solution for, but it seems I'm more unlucky. Is there any hope of getting this to work as intended, or should I just use a script to crash the firmware and reload the driver whenever I need WiFi.

journalctl -b

Any help would be appreciated.

Last edited by bifflodude (2025-01-12 12:05:27)

Offline

#22025-01-12 10:57:49
Head_on_a_Stick
Member

From: The Wirral
Registered: 2014-02-20
Posts: 8,842
Website
See https://bbs.archlinux.org/viewtopic.php?id=299821 and especially seth's link in post #3.

Perhaps also try the wl driver instead.

Jin, Jîyan, Azadî

Offline

#32025-01-12 12:04:09
bifflodude
Member
Registered: 2025-01-12
Posts: 2
I'm speechless, this post basically solved the issue for me. Speed is a bit slow, but its something, and most important, its stable, I can now at least tinker with the parameters to see if I get better speeds. Thank you Head_on_a_Stick and seth. This is the furthest I've gotten with this issue, so I'm marking this as solved, thank you!