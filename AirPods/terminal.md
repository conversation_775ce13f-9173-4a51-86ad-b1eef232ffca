        Bondable: Enabled (0x01)
@ MGMT Event: Command Complete (0x0001) plen 7                    {0x0001} [hci0] 29.445072
      Set Bondable (0x0009) plen 4
        Status: Success (0x00)
        Current settings: 0x000000d1
          Powered
          Bondable
          Secure Simple Pairing
          BR/EDR
bluetoothd[1272]: @ MGMT Command: Set IO Capab.. (0x0018) plen 1  {0x0001} [hci0] 29.445090
        Capability: DisplayYesNo (0x01)
@ MGMT Event: Command Complete (0x0001) plen 3                    {0x0001} [hci0] 29.445091
      Set IO Capability (0x0018) plen 0
        Status: Success (0x00)
bluetoothd[1272]: @ MGMT Command: Set Connecta.. (0x0007) plen 1  {0x0001} [hci0] 29.446480
        Connectable: Enabled (0x01)
< HCI Command: Write Scan Enable (0x03|0x001a) plen 1                   #1 [hci0] 29.446523
        Scan enable: Page Scan (0x02)
> HCI Event: Command Complete (0x0e) plen 4                             #2 [hci0] 29.447616
      Write Scan Enable (0x03|0x001a) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 7                    {0x0001} [hci0] 29.447663
      Set Connectable (0x0007) plen 4
        Status: Success (0x00)
        Current settings: 0x000000d3
          Powered
          Connectable
          Bondable
          Secure Simple Pairing
          BR/EDR
bluetoothd[1272]: @ MGMT Command: Set Discover.. (0x0006) plen 3  {0x0001} [hci0] 29.447725
        Discoverable: General (0x01)
        Timeout: 0
< HCI Command: Write Current IAC LAP (0x03|0x003a) plen 4               #3 [hci0] 29.447768
        Number of IAC: 1
        Access code: 0x9e8b33 (General Inquiry)
> HCI Event: Command Complete (0x0e) plen 4                             #4 [hci0] 29.448646
      Write Current IAC LAP (0x03|0x003a) ncmd 1
        Status: Success (0x00)
< HCI Command: Write Scan Enable (0x03|0x001a) plen 1                   #5 [hci0] 29.448761
        Scan enable: Inquiry Scan + Page Scan (0x03)
> HCI Event: Command Complete (0x0e) plen 4                             #6 [hci0] 29.449643
      Write Scan Enable (0x03|0x001a) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 7                    {0x0001} [hci0] 29.449726
      Set Discoverable (0x0006) plen 4
        Status: Success (0x00)
        Current settings: 0x000000db
          Powered
          Connectable
          Discoverable
          Bondable
          Secure Simple Pairing
          BR/EDR
bluetoothd[1272]: @ MGMT Command: Start Discov.. (0x0023) plen 1  {0x0001} [hci0] 29.449754
        Address type: 0x01
          BR/EDR
< HCI Command: Inquiry (0x01|0x0001) plen 5                             #7 [hci0] 29.449791
        Access code: 0x9e8b33 (General Inquiry)
        Length: 10.24s (0x08)
        Num responses: 0
> HCI Event: Command Status (0x0f) plen 4                               #8 [hci0] 29.450616
      Inquiry (0x01|0x0001) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 4                    {0x0001} [hci0] 29.450653
      Start Discovery (0x0023) plen 1
        Status: Success (0x00)
        Address type: 0x01
          BR/EDR
@ MGMT Event: Discovering (0x0013) plen 2                         {0x0001} [hci0] 29.450656
        Address type: 0x01
          BR/EDR
        Discovery: Enabled (0x01)
> HCI Event: Inquiry Complete (0x01) plen 1                             #9 [hci0] 39.693444
        Status: Success (0x00)
@ MGMT Event: Discovering (0x0013) plen 2                         {0x0001} [hci0] 39.693488
        Address type: 0x01
          BR/EDR
        Discovery: Disabled (0x00)
bluetoothd[1272]: @ MGMT Command: Start Discov.. (0x0023) plen 1  {0x0001} [hci0] 44.604477
        Address type: 0x01
          BR/EDR
< HCI Command: Inquiry (0x01|0x0001) plen 5                            #10 [hci0] 44.604556
        Access code: 0x9e8b33 (General Inquiry)
        Length: 10.24s (0x08)
        Num responses: 0
> HCI Event: Command Status (0x0f) plen 4                              #11 [hci0] 44.605348
      Inquiry (0x01|0x0001) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 4                    {0x0001} [hci0] 44.605390
      Start Discovery (0x0023) plen 1
        Status: Success (0x00)
        Address type: 0x01
          BR/EDR
@ MGMT Event: Discovering (0x0013) plen 2                         {0x0001} [hci0] 44.605392
        Address type: 0x01
          BR/EDR
        Discovery: Enabled (0x01)
< ACL Data TX: Handle 12 flags 0x00 dlen 12                            #12 [hci0] 48.865945
      L2CAP: Disconnection Request (0x06) ident 15 len 4
        Destination CID: 3336
        Source CID: 68
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 7           #13 [hci0] 48.887504
      Channel: 2823 len 3 [PSM 0 mode Basic (0x00)] {chan 65535}
        80 08 04                                         ...             
bluetoothd[1272]: @ MGMT Command: Remove Device (0x0034) plen 7   {0x0001} [hci0] 48.887520
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
@ MGMT Event: Command Complete (0x0001) plen 10                   {0x0001} [hci0] 48.887547
      Remove Device (0x0034) plen 7
        Status: Success (0x00)
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
< HCI Command: Disconnect (0x01|0x0006) plen 3                         #14 [hci0] 48.888107
        Handle: 6
        Reason: Remote User Terminated Connection (0x13)
< ACL Data TX: Handle 12 flags 0x00 dlen 8                             #15 [hci0] 48.888913
      Channel: 2309 len 4 [PSM 0 mode Basic (0x00)] {chan 65535}
        3b 53 01 e7                                      ;S..            
> HCI Event: Command Status (0x0f) plen 4                              #16 [hci0] 48.889291
      Disconnect (0x01|0x0006) ncmd 1
        Status: Success (0x00)
bluetoothd[1272]: = profiles/audio/avdtp.c:avdtp_start() avdtp_start: rejecti..   48.911042
bluetoothd[1272]: = profiles/audio/a2dp.c:a2dp_resume() avdtp_start failed        48.911075
> HCI Event: Disconnect Complete (0x05) plen 4                         #17 [hci0] 49.194429
        Status: Success (0x00)
        Handle: 6
        Reason: Connection Terminated By Local Host (0x16)
> HCI Event: Max Slots Change (0x1b) plen 3                            #18 [hci0] 49.197447
        Handle: 12
        Max slots: 3
> HCI Event: Max Slots Change (0x1b) plen 3                            #19 [hci0] 49.265321
        Handle: 12
        Max slots: 5
> ACL Data RX: Handle 12 flags 0x02 dlen 12                            #20 [hci0] 49.336413
      L2CAP: Disconnection Response (0x07) ident 15 len 4
        Destination CID: 3336
        Source CID: 68
> HCI Event: Number of Completed Packets (0x13) plen 5                 #21 [hci0] 49.337409
        Num handles: 1
        Handle: 12
        Count: 2
> ACL Data RX: Handle 12 flags 0x02 dlen 6                             #22 [hci0] 49.418428
      Channel: 67 len 2 [PSM 0 mode Basic (0x00)] {chan 65535}
        82 08                                            ..              
> ACL Data RX: Handle 12 flags 0x02 dlen 8                             #23 [hci0] 49.418430
      Channel: 65 len 4 [PSM 0 mode Basic (0x00)] {chan 65535}
        3b 73 01 cd                                      ;s..            
< ACL Data TX: Handle 12 flags 0x00 dlen 8                             #24 [hci0] 49.418548
      Channel: 2309 len 4 [PSM 0 mode Basic (0x00)] {chan 65535}
        03 53 01 fd                                      .S..            
< ACL Data TX: Handle 12 flags 0x00 dlen 12                            #25 [hci0] 49.418556
      L2CAP: Disconnection Request (0x06) ident 16 len 4
        Destination CID: 3078
        Source CID: 66
< ACL Data TX: Handle 12 flags 0x00 dlen 12                            #26 [hci0] 49.418627
      L2CAP: Disconnection Request (0x06) ident 17 len 4
        Destination CID: 2823
        Source CID: 67
> HCI Event: Number of Completed Packets (0x13) plen 5                 #27 [hci0] 49.497415
        Num handles: 1
        Handle: 12
        Count: 2
> ACL Data RX: Handle 12 flags 0x02 dlen 8                             #28 [hci0] 49.499425
      Channel: 65 len 4 [PSM 0 mode Basic (0x00)] {chan 65535}
        03 73 01 d7                                      .s..            
< ACL Data TX: Handle 12 flags 0x00 dlen 12                            #29 [hci0] 49.499542
      L2CAP: Disconnection Request (0x06) ident 18 len 4
        Destination CID: 2309
        Source CID: 65
> ACL Data RX: Handle 12 flags 0x02 dlen 12                            #30 [hci0] 49.576417
      L2CAP: Disconnection Response (0x07) ident 16 len 4
        Destination CID: 3078
        Source CID: 66
> HCI Event: Number of Completed Packets (0x13) plen 5                 #31 [hci0] 49.577414
        Num handles: 1
        Handle: 12
        Count: 2
> ACL Data RX: Handle 12 flags 0x02 dlen 12                            #32 [hci0] 49.579319
      L2CAP: Disconnection Response (0x07) ident 17 len 4
        Destination CID: 2823
        Source CID: 67
> ACL Data RX: Handle 12 flags 0x02 dlen 12                            #33 [hci0] 49.658417
      L2CAP: Disconnection Response (0x07) ident 18 len 4
        Destination CID: 2309
        Source CID: 65
> HCI Event: Number of Completed Packets (0x13) plen 5                 #34 [hci0] 49.768412
        Num handles: 1
        Handle: 12
        Count: 1
bluetoothd[1272]: @ MGMT Command: Disconnect (0x0014) plen 7      {0x0001} [hci0] 51.601463
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
< HCI Command: Disconnect (0x01|0x0006) plen 3                         #35 [hci0] 51.601549
        Handle: 12
        Reason: Remote User Terminated Connection (0x13)
> HCI Event: Command Status (0x0f) plen 4                              #36 [hci0] 51.602423
      Disconnect (0x01|0x0006) ncmd 1
        Status: Success (0x00)
> HCI Event: Vendor (0xff) plen 4                                      #37 [hci0] 51.731265
        49 0c 00 c8                                      I...            
> HCI Event: Disconnect Complete (0x05) plen 4                         #38 [hci0] 51.732261
        Status: Success (0x00)
        Handle: 12
        Reason: Connection Terminated By Local Host (0x16)
@ MGMT Event: Device Disconnected (0x000c) plen 8                 {0x0001} [hci0] 51.732319
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Reason: Connection terminated by local host (0x02)
@ MGMT Event: Command Complete (0x0001) plen 10                   {0x0001} [hci0] 51.749408
      Disconnect (0x0014) plen 7
        Status: Success (0x00)
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
bluetoothd[1272]: @ MGMT Command: Unpair Device (0x001b) plen 8   {0x0001} [hci0] 51.749466
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Disconnect: Enabled (0x01)
@ MGMT Event: Command Complete (0x0001) plen 10                   {0x0001} [hci0] 51.749468
      Unpair Device (0x001b) plen 7
        Status: Success (0x00)
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
> HCI Event: Inquiry Complete (0x01) plen 1                            #39 [hci0] 54.849362
        Status: Success (0x00)
@ MGMT Event: Discovering (0x0013) plen 2                         {0x0001} [hci0] 54.849377
        Address type: 0x01
          BR/EDR
        Discovery: Disabled (0x00)
bluetoothd[1272]: @ MGMT Command: Start Discov.. (0x0023) plen 1  {0x0001} [hci0] 60.604333
        Address type: 0x01
          BR/EDR
< HCI Command: Inquiry (0x01|0x0001) plen 5                            #40 [hci0] 60.604424
        Access code: 0x9e8b33 (General Inquiry)
        Length: 10.24s (0x08)
        Num responses: 0
> HCI Event: Command Status (0x0f) plen 4                              #41 [hci0] 60.605237
      Inquiry (0x01|0x0001) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 4                    {0x0001} [hci0] 60.605357
      Start Discovery (0x0023) plen 1
        Status: Success (0x00)
        Address type: 0x01
          BR/EDR
@ MGMT Event: Discovering (0x0013) plen 2                         {0x0001} [hci0] 60.605359
        Address type: 0x01
          BR/EDR
        Discovery: Enabled (0x01)
> HCI Event: Inquiry Complete (0x01) plen 1                            #42 [hci0] 70.849252
        Status: Success (0x00)
@ MGMT Event: Discovering (0x0013) plen 2                         {0x0001} [hci0] 70.849293
        Address type: 0x01
          BR/EDR
        Discovery: Disabled (0x00)
bluetoothd[1272]: @ MGMT Command: Start Discov.. (0x0023) plen 1  {0x0001} [hci0] 76.604484
        Address type: 0x01
          BR/EDR
< HCI Command: Inquiry (0x01|0x0001) plen 5                            #43 [hci0] 76.604562
        Access code: 0x9e8b33 (General Inquiry)
        Length: 10.24s (0x08)
        Num responses: 0
> HCI Event: Command Status (0x0f) plen 4                              #44 [hci0] 76.605225
      Inquiry (0x01|0x0001) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 4                    {0x0001} [hci0] 76.605304
      Start Discovery (0x0023) plen 1
        Status: Success (0x00)
        Address type: 0x01
          BR/EDR
@ MGMT Event: Discovering (0x0013) plen 2                         {0x0001} [hci0] 76.605334
        Address type: 0x01
          BR/EDR
        Discovery: Enabled (0x01)
> HCI Event: Extended Inquiry Result (0x2f) plen 255                   #45 [hci0] 76.835223
        Num responses: 1
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page scan repetition mode: R1 (0x01)
        Page period mode: P0 (0x00)
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
        Clock offset: 0x4a02
        RSSI: -63 dBm (0xc1)
        Device ID: Bluetooth SIG assigned (0x0001)
          Vendor: Apple, Inc. (76)
          Product: 0x200e
          Version: 1.5.0 (0x0150)
        Name (complete): AirPods Pro
@ MGMT Event: Device Found (0x0012) plen 42                       {0x0001} [hci0] 76.835260
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        RSSI: -63 dBm (0xc1)
        Flags: 0x00000000
        Data length: 28
        Device ID: Bluetooth SIG assigned (0x0001)
          Vendor: Apple, Inc. (76)
          Product: 0x200e
          Version: 1.5.0 (0x0150)
        Name (complete): AirPods Pro
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
> HCI Event: Inquiry Complete (0x01) plen 1                            #46 [hci0] 86.849127
        Status: Success (0x00)
@ MGMT Event: Discovering (0x0013) plen 2                         {0x0001} [hci0] 86.849167
        Address type: 0x01
          BR/EDR
        Discovery: Disabled (0x00)
bluetoothd[1272]: @ MGMT Command: Start Discov.. (0x0023) plen 1  {0x0001} [hci0] 91.604368
        Address type: 0x01
          BR/EDR
< HCI Command: Inquiry (0x01|0x0001) plen 5                            #47 [hci0] 91.604459
        Access code: 0x9e8b33 (General Inquiry)
        Length: 10.24s (0x08)
        Num responses: 0
> HCI Event: Command Status (0x0f) plen 4                              #48 [hci0] 91.605090
      Inquiry (0x01|0x0001) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 4                    {0x0001} [hci0] 91.605166
      Start Discovery (0x0023) plen 1
        Status: Success (0x00)
        Address type: 0x01
          BR/EDR
@ MGMT Event: Discovering (0x0013) plen 2                         {0x0001} [hci0] 91.605170
        Address type: 0x01
          BR/EDR
        Discovery: Enabled (0x01)
> HCI Event: Extended Inquiry Result (0x2f) plen 255                   #49 [hci0] 92.048187
        Num responses: 1
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page scan repetition mode: R1 (0x01)
        Page period mode: P0 (0x00)
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
        Clock offset: 0x4a03
        RSSI: -67 dBm (0xbd)
@ MGMT Event: Device Found (0x0012) plen 19                       {0x0001} [hci0] 92.048224
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        RSSI: -67 dBm (0xbd)
        Flags: 0x00000001
          Confirm Name
        Data length: 5
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
> HCI Event: Extended Inquiry Result (0x2f) plen 255                   #50 [hci0] 92.439094
        Num responses: 1
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page scan repetition mode: R1 (0x01)
        Page period mode: P0 (0x00)
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
        Clock offset: 0x4a02
        RSSI: -67 dBm (0xbd)
        Device ID: Bluetooth SIG assigned (0x0001)
          Vendor: Apple, Inc. (76)
          Product: 0x200e
          Version: 1.5.0 (0x0150)
        Name (complete): AirPods Pro
@ MGMT Event: Device Found (0x0012) plen 42                       {0x0001} [hci0] 92.439133
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        RSSI: -67 dBm (0xbd)
        Flags: 0x00000000
        Data length: 28
        Device ID: Bluetooth SIG assigned (0x0001)
          Vendor: Apple, Inc. (76)
          Product: 0x200e
          Version: 1.5.0 (0x0150)
        Name (complete): AirPods Pro
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
          > HCI Event: Inquiry Complete (0x01) plen 1                           #51 [hci0] 101.849176
        Status: Success (0x00)
@ MGMT Event: Discovering (0x0013) plen 2                        {0x0001} [hci0] 101.849216
        Address type: 0x01
          BR/EDR
        Discovery: Disabled (0x00)
bluetoothd[1272]: @ MGMT Command: Start Disco.. (0x0023) plen 1  {0x0001} [hci0] 106.604376
        Address type: 0x01
          BR/EDR
< HCI Command: Inquiry (0x01|0x0001) plen 5                           #52 [hci0] 106.604455
        Access code: 0x9e8b33 (General Inquiry)
        Length: 10.24s (0x08)
        Num responses: 0
> HCI Event: Command Status (0x0f) plen 4                             #53 [hci0] 106.605174
      Inquiry (0x01|0x0001) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 4                   {0x0001} [hci0] 106.605214
      Start Discovery (0x0023) plen 1
        Status: Success (0x00)
        Address type: 0x01
          BR/EDR
@ MGMT Event: Discovering (0x0013) plen 2                        {0x0001} [hci0] 106.605217
        Address type: 0x01
          BR/EDR
        Discovery: Enabled (0x01)
> HCI Event: Extended Inquiry Result (0x2f) plen 255                  #54 [hci0] 109.964187
        Num responses: 1
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page scan repetition mode: R1 (0x01)
        Page period mode: P0 (0x00)
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
        Clock offset: 0x4a02
        RSSI: -79 dBm (0xb1)
@ MGMT Event: Device Found (0x0012) plen 19                      {0x0001} [hci0] 109.964227
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        RSSI: -79 dBm (0xb1)
        Flags: 0x00000001
          Confirm Name
        Data length: 5
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
> HCI Event: Extended Inquiry Result (0x2f) plen 255                  #55 [hci0] 109.994079
        Num responses: 1
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page scan repetition mode: R1 (0x01)
        Page period mode: P0 (0x00)
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
        Clock offset: 0x4a02
        RSSI: -79 dBm (0xb1)
        Device ID: Bluetooth SIG assigned (0x0001)
          Vendor: Apple, Inc. (76)
          Product: 0x200e
          Version: 1.5.0 (0x0150)
        Name (complete): AirPods Pro
@ MGMT Event: Device Found (0x0012) plen 42                      {0x0001} [hci0] 109.994133
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        RSSI: -79 dBm (0xb1)
        Flags: 0x00000000
        Data length: 28
        Device ID: Bluetooth SIG assigned (0x0001)
          Vendor: Apple, Inc. (76)
          Product: 0x200e
          Version: 1.5.0 (0x0150)
        Name (complete): AirPods Pro
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
> HCI Event: Inquiry Complete (0x01) plen 1                           #56 [hci0] 116.849083
        Status: Success (0x00)
@ MGMT Event: Discovering (0x0013) plen 2                        {0x0001} [hci0] 116.849096
        Address type: 0x01
          BR/EDR
        Discovery: Disabled (0x00)
bluetoothd[1272]: @ MGMT Command: Start Disco.. (0x0023) plen 1  {0x0001} [hci0] 121.604324
        Address type: 0x01
          BR/EDR
< HCI Command: Inquiry (0x01|0x0001) plen 5                           #57 [hci0] 121.604366
        Access code: 0x9e8b33 (General Inquiry)
        Length: 10.24s (0x08)
        Num responses: 0
> HCI Event: Command Status (0x0f) plen 4                             #58 [hci0] 121.605075
      Inquiry (0x01|0x0001) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 4                   {0x0001} [hci0] 121.605126
      Start Discovery (0x0023) plen 1
        Status: Success (0x00)
        Address type: 0x01
          BR/EDR
@ MGMT Event: Discovering (0x0013) plen 2                        {0x0001} [hci0] 121.605128
        Address type: 0x01
          BR/EDR
        Discovery: Enabled (0x01)
> HCI Event: Extended Inquiry Result (0x2f) plen 255                  #59 [hci0] 122.765097
        Num responses: 1
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page scan repetition mode: R1 (0x01)
        Page period mode: P0 (0x00)
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
        Clock offset: 0x4a02
        RSSI: -69 dBm (0xbb)
        Device ID: Bluetooth SIG assigned (0x0001)
          Vendor: Apple, Inc. (76)
          Product: 0x200e
          Version: 1.5.0 (0x0150)
        Name (complete): AirPods Pro
@ MGMT Event: Device Found (0x0012) plen 42                      {0x0001} [hci0] 122.765137
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        RSSI: -69 dBm (0xbb)
        Flags: 0x00000000
        Data length: 28
        Device ID: Bluetooth SIG assigned (0x0001)
          Vendor: Apple, Inc. (76)
          Product: 0x200e
          Version: 1.5.0 (0x0150)
        Name (complete): AirPods Pro
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
bluetoothd[1272]: @ MGMT Command: Set Connect.. (0x0007) plen 1  {0x0001} [hci0] 131.107826
        Connectable: Disabled (0x00)
< HCI Command: Write Scan Enable (0x03|0x001a) plen 1                 #60 [hci0] 131.107905
        Scan enable: No Scans (0x00)
> HCI Event: Command Complete (0x0e) plen 4                           #61 [hci0] 131.109179
      Write Scan Enable (0x03|0x001a) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 7                   {0x0001} [hci0] 131.109246
      Set Connectable (0x0007) plen 4
        Status: Success (0x00)
        Current settings: 0x000000d1
          Powered
          Bondable
          Secure Simple Pairing
          BR/EDR
bluetoothd[1272]: @ MGMT Command: Stop Discov.. (0x0024) plen 1  {0x0001} [hci0] 131.111854
        Address type: 0x01
          BR/EDR
< HCI Command: Inquiry Cancel (0x01|0x0002) plen 0                    #62 [hci0] 131.111951
> HCI Event: Command Complete (0x0e) plen 4                           #63 [hci0] 131.114084
      Inquiry Cancel (0x01|0x0002) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Discovering (0x0013) plen 2                        {0x0001} [hci0] 131.114120
        Address type: 0x01
          BR/EDR
        Discovery: Disabled (0x00)
@ MGMT Event: Command Complete (0x0001) plen 4                   {0x0001} [hci0] 131.114127
      Stop Discovery (0x0024) plen 1
        Status: Success (0x00)
        Address type: 0x01
          BR/EDR
bluetoothd[1272]: @ MGMT Command: Add Device (0x0033) plen 8     {0x0001} [hci0] 131.114165
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Action: Allow incoming connection (0x01)
@ MGMT Event: Device Flags Changed (0x002a) plen 15              {0x0001} [hci0] 131.114169
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Supported Flags: 0x00000001
          Remote Wakeup
        Current Flags: 0x00000000
@ MGMT Event: Command Complete (0x0001) plen 10                  {0x0001} [hci0] 131.114170
      Add Device (0x0033) plen 7
        Status: Success (0x00)
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
bluetoothd[1272]: @ MGMT Command: Stop Discov.. (0x0024) plen 1  {0x0001} [hci0] 131.114187
        Address type: 0x01
          BR/EDR
@ MGMT Event: Command Complete (0x0001) plen 4                   {0x0001} [hci0] 131.114188
      Stop Discovery (0x0024) plen 1
        Status: Rejected (0x0b)
        Address type: 0x01
          BR/EDR
< HCI Command: Write Scan Enable (0x03|0x001a) plen 1                 #64 [hci0] 131.114190
        Scan enable: Page Scan (0x02)
bluetoothd[1272]: @ MGMT Command: Pair Device (0x0019) plen 8    {0x0001} [hci0] 131.114198
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Capability: DisplayYesNo (0x01)
> HCI Event: Command Complete (0x0e) plen 4                           #65 [hci0] 131.115086
      Write Scan Enable (0x03|0x001a) ncmd 1
        Status: Success (0x00)
< HCI Command: Create Connection (0x01|0x0005) plen 13                #66 [hci0] 131.115130
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Packet type: 0xcc18
          DM1 may be used
          DH1 may be used
          DM3 may be used
          DH3 may be used
          DM5 may be used
          DH5 may be used
        Page scan repetition mode: R1 (0x01)
        Page scan mode: Mandatory (0x00)
        Clock offset: 0xca02
        Role switch: Allow peripheral (0x01)
> HCI Event: Command Status (0x0f) plen 4                             #67 [hci0] 131.117084
      Create Connection (0x01|0x0005) ncmd 1
        Status: Success (0x00)
> HCI Event: Connect Complete (0x03) plen 11                          #68 [hci0] 131.215142
        Status: Success (0x00)
        Handle: 12
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Link type: ACL (0x01)
        Encryption: Disabled (0x00)
< HCI Command: Read Remote Supported Features (0x01|0x001b) plen 2    #69 [hci0] 131.215221
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
btmon[6677]: @ RAW Open: btmon (privileged) version 2.22                {0x0002} 131.215316
btmon[6677]: @ RAW Close: btmon                                         {0x0002} 131.215318
> HCI Event: Command Status (0x0f) plen 4                             #70 [hci0] 131.216086
      Read Remote Supported Features (0x01|0x001b) ncmd 1
        Status: Success (0x00)
< HCI Command: Write Scan Enable (0x03|0x001a) plen 1                 #71 [hci0] 131.216092
        Scan enable: No Scans (0x00)
> HCI Event: Read Remote Supported Features (0x0b) plen 11            #72 [hci0] 131.217084
        Status: Success (0x00)
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Features: 0xbf 0xfe 0x2f 0xfe 0xdb 0xff 0x7b 0x87
          3 slot packets
          5 slot packets
          Encryption
          Slot offset
          Timing accuracy
          Role switch
          Sniff mode
          Power control requests
          Channel quality driven data rate (CQDDR)
          SCO link
          HV2 packets
          HV3 packets
          u-law log synchronous data
          A-law log synchronous data
          CVSD synchronous data
          Paging parameter negotiation
          Power control
          Transparent synchronous data
          Flow control lag (middle bit)
          Enhanced Data Rate ACL 2 Mbps mode
          Enhanced Data Rate ACL 3 Mbps mode
          Enhanced inquiry scan
          Interlaced inquiry scan
          Interlaced page scan
          RSSI with inquiry results
          Extended SCO link (EV3 packets)
          EV4 packets
          EV5 packets
          AFH capable peripheral
          AFH classification peripheral
          LE Supported (Controller)
          3-slot Enhanced Data Rate ACL packets
          5-slot Enhanced Data Rate ACL packets
          Sniff subrating
          Pause encryption
          AFH capable central
          AFH classification central
          Enhanced Data Rate eSCO 2 Mbps mode
          Enhanced Data Rate eSCO 3 Mbps mode
          3-slot Enhanced Data Rate eSCO packets
          Extended Inquiry Response
          Simultaneous LE and BR/EDR (Controller)
          Secure Simple Pairing
          Encapsulated PDU
          Erroneous Data Reporting
          Non-flushable Packet Boundary Flag
          Link Supervision Timeout Changed Event
          Inquiry TX Power Level
          Enhanced Power Control
          Extended features
> HCI Event: Command Complete (0x0e) plen 4                           #73 [hci0] 131.218081
      Write Scan Enable (0x03|0x001a) ncmd 1
        Status: Success (0x00)
< HCI Command: Read Remote Extended Features (0x01|0x001c) plen 3     #74 [hci0] 131.218115
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page: 1
> HCI Event: Command Status (0x0f) plen 4                             #75 [hci0] 131.219089
      Read Remote Extended Features (0x01|0x001c) ncmd 1
        Status: Success (0x00)
> HCI Event: Read Remote Extended Features (0x23) plen 13             #76 [hci0] 131.220086
        Status: Success (0x00)
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page: 1/1
        Features: 0x07 0x00 0x00 0x00 0x00 0x00 0x00 0x00
          Secure Simple Pairing (Host Support)
          LE Supported (Host)
          Simultaneous LE and BR/EDR (Host)
< HCI Command: Remote Name Request (0x01|0x0019) plen 10              #77 [hci0] 131.220120
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page scan repetition mode: R2 (0x02)
        Page scan mode: Mandatory (0x00)
        Clock offset: 0x0000
> HCI Event: Command Status (0x0f) plen 4                             #78 [hci0] 131.221055
      Remote Name Request (0x01|0x0019) ncmd 1
        Status: Success (0x00)
> HCI Event: Max Slots Change (0x1b) plen 3                           #79 [hci0] 131.223094
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Max slots: 5
> HCI Event: Remote Name Req Complete (0x07) plen 255                 #80 [hci0] 131.250184
        Status: Success (0x00)
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Name: AirPods Pro
@ MGMT Event: Device Connected (0x000b) plen 31                  {0x0001} [hci0] 131.250220
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Flags: 0x00000008
          Connection Locally Initiated
        Data length: 18
        Name (complete): AirPods Pro
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
< HCI Command: Authentication Requested (0x01|0x0011) plen 2          #81 [hci0] 131.250224
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
> HCI Event: Command Status (0x0f) plen 4                             #82 [hci0] 131.251058
      Authentication Requested (0x01|0x0011) ncmd 1
        Status: Success (0x00)
> HCI Event: Link Key Request (0x17) plen 6                           #83 [hci0] 131.252126
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
< HCI Command: Link Key Request Negative Reply (0x01|0x000c) plen 6   #84 [hci0] 131.252164
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
> HCI Event: Command Complete (0x0e) plen 10                          #85 [hci0] 131.253177
      Link Key Request Negative Reply (0x01|0x000c) ncmd 1
        Status: Success (0x00)
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
> HCI Event: IO Capability Request (0x31) plen 6                      #86 [hci0] 131.254111
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
< HCI Command: IO Capability Request Reply (0x01|0x002b) plen 9       #87 [hci0] 131.254146
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        IO capability: DisplayYesNo (0x01)
        OOB data: Authentication data not present (0x00)
        Authentication: Dedicated Bonding - MITM required (0x03)
> HCI Event: Command Complete (0x0e) plen 10                          #88 [hci0] 131.255107
      IO Capability Request Reply (0x01|0x002b) ncmd 1
        Status: Success (0x00)
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
> HCI Event: IO Capability Response (0x32) plen 9                     #89 [hci0] 131.260085
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        IO capability: NoInputNoOutput (0x03)
        OOB data: Authentication data not present (0x00)
        Authentication: Dedicated Bonding - MITM not required (0x02)
> HCI Event: User Confirmation Request (0x33) plen 10                 #90 [hci0] 131.333181
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Passkey: 310395
< HCI Command: User Confirmation Request Reply (0x01|0x002c) plen 6   #91 [hci0] 131.333219
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
> HCI Event: Command Complete (0x0e) plen 10                          #92 [hci0] 131.339082
      User Confirmation Request Reply (0x01|0x002c) ncmd 1
        Status: Success (0x00)
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
> HCI Event: Simple Pairing Complete (0x36) plen 7                    #93 [hci0] 131.356073
        Status: Success (0x00)
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
> HCI Event: Link Key Notification (0x18) plen 23                     #94 [hci0] 131.369183
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Link key: 7a64ec98d997df6fe626ef7436a35880
        Key type: Unauthenticated Combination key from P-192 (0x04)
@ MGMT Event: New Link Key (0x0009) plen 26                      {0x0001} [hci0] 131.369221
        Store hint: Yes (0x01)
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Key type: Unauthenticated Combination key from P-192 (0x04)
        Link key: 7a64ec98d997df6fe626ef7436a35880
        PIN length: 0
> HCI Event: Auth Complete (0x06) plen 3                              #95 [hci0] 131.370071
        Status: Success (0x00)
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
< HCI Command: Set Connection Encryption (0x01|0x0013) plen 3         #96 [hci0] 131.370198
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Encryption: Enabled (0x01)
> HCI Event: Command Status (0x0f) plen 4                             #97 [hci0] 131.371055
      Set Connection Encryption (0x01|0x0013) ncmd 1
        Status: Success (0x00)
> HCI Event: Encryption Change (0x08) plen 4                          #98 [hci0] 131.388182
        Status: Success (0x00)
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Encryption: Enabled with E0 (0x01)
< HCI Command: Read Encryption Key Size (0x05|0x0008) plen 2          #99 [hci0] 131.388221
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
> HCI Event: Command Complete (0x0e) plen 7                          #100 [hci0] 131.389057
      Read Encryption Key Size (0x05|0x0008) ncmd 1
        Status: Success (0x00)
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Key size: 16
@ MGMT Event: Command Complete (0x0001) plen 10                  {0x0001} [hci0] 131.389098
      Pair Device (0x0019) plen 7
        Status: Success (0x00)
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
< ACL Data TX: Handle 12 flags 0x00 dlen 10                          #101 [hci0] 131.389101
      L2CAP: Information Request (0x0a) ident 1 len 2
        Type: Extended features supported (0x0002)
> ACL Data RX: Handle 12 flags 0x02 dlen 16                          #102 [hci0] 131.396353
      L2CAP: Information Response (0x0b) ident 1 len 8
        Type: Extended features supported (0x0002)
        Result: Success (0x0000)
        Features: 0x00000280
          Fixed Channels
          Unicast Connectionless Data Reception
< ACL Data TX: Handle 12 flags 0x00 dlen 10                          #103 [hci0] 131.396395
      L2CAP: Information Request (0x0a) ident 2 len 2
        Type: Fixed channels supported (0x0003)
> HCI Event: Number of Completed Packets (0x13) plen 5               #104 [hci0] 131.399086
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #101: len 10 (8 Kb/s)
        Latency: 9 msec (9-9 msec ~9 msec)
        #103: len 10 (40 Kb/s)
        Latency: 2 msec (2-9 msec ~6 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 20                          #105 [hci0] 131.403340
      L2CAP: Information Response (0x0b) ident 2 len 12
        Type: Fixed channels supported (0x0003)
        Result: Success (0x0000)
        Channels: 0x0001000000000040
          Security Manager (LE)
          Unknown channels (0x1000000000000)
< ACL Data TX: Handle 12 flags 0x00 dlen 12                          #106 [hci0] 131.403368
      L2CAP: Connection Request (0x02) ident 3 len 4
        PSM: 1 (0x0001)
        Source CID: 64
> ACL Data RX: Handle 12 flags 0x02 dlen 10                          #107 [hci0] 131.412385
      L2CAP: Information Request (0x0a) ident 39 len 2
        Type: Extended features supported (0x0002)
> ACL Data RX: Handle 12 flags 0x02 dlen 16                          #108 [hci0] 131.412387
      L2CAP: Connection Response (0x03) ident 3 len 8
        Destination CID: 3588
        Source CID: 64
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 12 flags 0x00 dlen 16                          #109 [hci0] 131.412398
      L2CAP: Information Response (0x0b) ident 39 len 8
        Type: Extended features supported (0x0002)
        Result: Success (0x0000)
        Features: 0x000002b8
          Enhanced Retransmission Mode
          Streaming Mode
          FCS Option
          Fixed Channels
          Unicast Connectionless Data Reception
< ACL Data TX: Handle 12 flags 0x00 dlen 12                          #110 [hci0] 131.412405
      L2CAP: Configure Request (0x04) ident 4 len 4
        Destination CID: 3588
        Flags: 0x0000
> HCI Event: Number of Completed Packets (0x13) plen 5               #111 [hci0] 131.415146
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #106: len 12 (8 Kb/s)
        Latency: 11 msec (2-11 msec ~9 msec)
        #109: len 16 (64 Kb/s)
        Latency: 2 msec (2-11 msec ~5 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 10                          #112 [hci0] 131.433339
      L2CAP: Information Request (0x0a) ident 40 len 2
        Type: Fixed channels supported (0x0003)
< ACL Data TX: Handle 12 flags 0x00 dlen 20                          #113 [hci0] 131.433402
      L2CAP: Information Response (0x0b) ident 40 len 12
        Type: Fixed channels supported (0x0003)
        Result: Success (0x0000)
        Channels: 0x0000000000000006
          L2CAP Signaling (BR/EDR)
          Connectionless reception
> ACL Data RX: Handle 12 flags 0x02 dlen 16                          #114 [hci0] 131.435439
      L2CAP: Configure Request (0x04) ident 41 len 8
        Destination CID: 64
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 256
> ACL Data RX: Handle 12 flags 0x02 dlen 18                          #115 [hci0] 131.435439
      L2CAP: Configure Response (0x05) ident 4 len 10
        Source CID: 64
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
< ACL Data TX: Handle 12 flags 0x00 dlen 18                          #116 [hci0] 131.435477
      L2CAP: Configure Response (0x05) ident 41 len 10
        Source CID: 3588
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 256
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 24        #117 [hci0] 131.435592
      Channel: 3588 len 20 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Request (0x06) tid 0 len 15
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              L2CAP (0x0100)
        Max record count: 65535
        Attribute list: [len 7]
          Sequence (6) with 5 bytes [8 extra bits] len 7
            Unsigned Integer (1) with 4 bytes [0 extra bits] len 5
              0x0000ffff
        Continuation state: 0
> HCI Event: Number of Completed Packets (0x13) plen 5               #118 [hci0] 131.437057
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #110: len 12 (4 Kb/s)
        Latency: 24 msec (2-24 msec ~15 msec)
        #113: len 20 (53 Kb/s)
        Latency: 3 msec (2-24 msec ~9 msec)
> HCI Event: Number of Completed Packets (0x13) plen 5               #119 [hci0] 131.443094
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #116: len 18 (20 Kb/s)
        Latency: 7 msec (2-24 msec ~8 msec)
        #117: len 24 (27 Kb/s)
        Latency: 7 msec (2-24 msec ~8 msec)
        Channel: 3588 [PSM 1 mode Basic (0x00)] {chan 0}
        Channel Latency: 7 msec (7-7 msec ~7 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 258                         #120 [hci0] 131.457354
      Channel: 64 len 254 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Response (0x07) tid 0 len 249
        Attribute bytes: 244
        Continuation state: 2
        00 f1                                            ..              
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 26        #121 [hci0] 131.457489
      Channel: 3588 len 22 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Request (0x06) tid 1 len 17
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              L2CAP (0x0100)
        Max record count: 65535
        Attribute list: [len 7]
          Sequence (6) with 5 bytes [8 extra bits] len 7
            Unsigned Integer (1) with 4 bytes [0 extra bits] len 5
              0x0000ffff
        Continuation state: 2
        00 f1                                            ..              
> ACL Data RX: Handle 12 flags 0x02 dlen 258                         #122 [hci0] 131.494451
      Channel: 64 len 254 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Response (0x07) tid 1 len 249
        Attribute bytes: 244
        Continuation state: 2
        01 e5                                            ..              
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 26        #123 [hci0] 131.494580
      Channel: 3588 len 22 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Request (0x06) tid 2 len 17
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              L2CAP (0x0100)
        Max record count: 65535
        Attribute list: [len 7]
          Sequence (6) with 5 bytes [8 extra bits] len 7
            Unsigned Integer (1) with 4 bytes [0 extra bits] len 5
              0x0000ffff
        Continuation state: 2
        01 e5                                            ..              
> HCI Event: Number of Completed Packets (0x13) plen 5               #124 [hci0] 131.498092
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #121: len 26 (5 Kb/s)
        Latency: 40 msec (2-40 msec ~24 msec)
        Channel: 3588 [PSM 1 mode Basic (0x00)] {chan 0}
        Channel Latency: 40 msec (7-40 msec ~24 msec)
        #123: len 26 (69 Kb/s)
        Latency: 3 msec (2-40 msec ~13 msec)
        Channel: 3588 [PSM 1 mode Basic (0x00)] {chan 0}
        Channel Latency: 3 msec (3-40 msec ~13 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 260                         #125 [hci0] 131.545423
      Channel: 64 len 256 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Response (0x07) tid 2 len 251
        Attribute bytes: 246
        Continuation state: 2
        02 db                                            ..              
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 26        #126 [hci0] 131.545542
      Channel: 3588 len 22 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Request (0x06) tid 3 len 17
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              L2CAP (0x0100)
        Max record count: 65535
        Attribute list: [len 7]
          Sequence (6) with 5 bytes [8 extra bits] len 7
            Unsigned Integer (1) with 4 bytes [0 extra bits] len 5
              0x0000ffff
        Continuation state: 2
        02 db                                            ..              
> ACL Data RX: Handle 12 flags 0x02 dlen 161                         #127 [hci0] 131.582422
      Channel: 64 len 157 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Response (0x07) tid 3 len 152
        Attribute bytes: 149
        Continuation state: 0
        Combined attribute bytes: 883
          Attribute list: [len 147] {position 0}
            Attribute: Service Record Handle (0x0000) [len 2]
              0x4f492950
            Attribute: Service Class ID List (0x0001) [len 2]
              UUID (3) with 16 bytes [0 extra bits] len 17
                74ec2172-0bad-4d01-8f77-997b2be0722a
            Attribute: Service Record State (0x0002) [len 2]
              0x00000000
            Attribute: Protocol Descriptor List (0x0004) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  L2CAP (0x0100)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x1001
            Attribute: Browse Group List (0x0005) [len 2]
              UUID (3) with 2 bytes [0 extra bits] len 3
                Public Browse Root (0x1002)
            Attribute: Language Base Attribute ID List (0x0006) [len 2]
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x656e
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0100
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6672
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0110
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6465
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0120
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6a61
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0130
            Attribute: Service Availability (0x0008) [len 2]
              0xff
            Attribute: Bluetooth Profile Descriptor List (0x0009) [len 2]
              Sequence (6) with 20 bytes [8 extra bits] len 22
                UUID (3) with 16 bytes [0 extra bits] len 17
                  4b6f7c74-07f4-49de-b0b9-ab43 4728f29
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0100
            Attribute: Unknown (0x0100) [len 2]
              AAP Server [len 10]
          Attribute list: [len 131] {position 1}
            Attribute: Service Record Handle (0x0000) [len 2]
              0x4f49111e
            Attribute: Service Class ID List (0x0001) [len 2]
              UUID (3) with 2 bytes [0 extra bits] len 3
                Handsfree (0x111e)
              UUID (3) with 2 bytes [0 extra bits] len 3
                Generic Audio (0x1203)
            Attribute: Service Record State (0x0002) [len 2]
              0x00000000
            Attribute: Protocol Descriptor List (0x0004) [len 2]
              Sequence (6) with 3 bytes [8 extra bits] len 5
                UUID (3) with 2 bytes [0 extra bits] len 3
                  L2CAP (0x0100)
              Sequence (6) with 5 bytes [8 extra bits] len 7
                UUID (3) with 2 bytes [0 extra bits] len 3
                  RFCOMM (0x0003)
                Unsigned Integer (1) with 1 byte [0 extra bits] len 2
                  0x07
            Attribute: Browse Group List (0x0005) [len 2]
              UUID (3) with 2 bytes [0 extra bits] len 3
                Public Browse Root (0x1002)
            Attribute: Language Base Attribute ID List (0x0006) [len 2]
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x656e
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0100
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6672
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0110
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6465
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0120
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6a61
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0130
            Attribute: Service Availability (0x0008) [len 2]
              0xff
            Attribute: Bluetooth Profile Descriptor List (0x0009) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  Handsfree (0x111e)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0106
            Attribute: Unknown (0x0100) [len 2]
              Handsfree [len 9]
            Attribute: Unknown (0x0311) [len 2]
              0x003b
          Attribute list: [len 135] {position 2}
            Attribute: Service Record Handle (0x0000) [len 2]
              0x4f49110c
            Attribute: Service Class ID List (0x0001) [len 2]
              UUID (3) with 2 bytes [0 extra bits] len 3
                A/V Remote Control Target (0x110c)
            Attribute: Service Record State (0x0002) [len 2]
              0x00000000
            Attribute: Protocol Descriptor List (0x0004) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  L2CAP (0x0100)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0017
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  AVCTP (0x0017)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0104
            Attribute: Browse Group List (0x0005) [len 2]
              UUID (3) with 2 bytes [0 extra bits] len 3
                Public Browse Root (0x1002)
            Attribute: Language Base Attribute ID List (0x0006) [len 2]
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x656e
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0100
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6672
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0110
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6465
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0120
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6a61
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0130
            Attribute: Service Availability (0x0008) [len 2]
              0xff
            Attribute: Bluetooth Profile Descriptor List (0x0009) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  A/V Remote Control (0x110e)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0105
            Attribute: Unknown (0x0100) [len 2]
              AVRCP Target [len 12]
            Attribute: Unknown (0x0311) [len 2]
              0x0002
          Attribute list: [len 142] {position 3}
            Attribute: Service Record Handle (0x0000) [len 2]
              0x4f49110e
            Attribute: Service Class ID List (0x0001) [len 2]
              UUID (3) with 2 bytes [0 extra bits] len 3
                A/V Remote Control (0x110e)
              UUID (3) with 2 bytes [0 extra bits] len 3
                A/V Remote Control Controller (0x110f)
            Attribute: Service Record State (0x0002) [len 2]
              0x00000000
            Attribute: Protocol Descriptor List (0x0004) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  L2CAP (0x0100)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0017
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  AVCTP (0x0017)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0104
            Attribute: Browse Group List (0x0005) [len 2]
              UUID (3) with 2 bytes [0 extra bits] len 3
                Public Browse Root (0x1002)
            Attribute: Language Base Attribute ID List (0x0006) [len 2]
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x656e
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0100
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6672
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0110
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6465
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0120
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6a61
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0130
            Attribute: Service Availability (0x0008) [len 2]
              0xff
            Attribute: Bluetooth Profile Descriptor List (0x0009) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  A/V Remote Control (0x110e)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0105
            Attribute: Unknown (0x0100) [len 2]
              AVRCP Controller [len 16]
            Attribute: Unknown (0x0311) [len 2]
              0x0001
          Attribute list: [len 133] {position 4}
            Attribute: Service Record Handle (0x0000) [len 2]
              0x4f49110b
            Attribute: Service Class ID List (0x0001) [len 2]
              UUID (3) with 2 bytes [0 extra bits] len 3
                Audio Sink (0x110b)
            Attribute: Service Record State (0x0002) [len 2]
              0x00000000
            Attribute: Protocol Descriptor List (0x0004) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  L2CAP (0x0100)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0019
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  AVDTP (0x0019)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0103
            Attribute: Browse Group List (0x0005) [len 2]
              UUID (3) with 2 bytes [0 extra bits] len 3
                Public Browse Root (0x1002)
            Attribute: Language Base Attribute ID List (0x0006) [len 2]
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x656e
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0100
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6672
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0110
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6465
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0120
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6a61
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0130
            Attribute: Service Availability (0x0008) [len 2]
              0xff
            Attribute: Bluetooth Profile Descriptor List (0x0009) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  Advanced Audio Distribution (0x110d)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0103
            Attribute: Unknown (0x0100) [len 2]
              Audio Sink [len 10]
            Attribute: Unknown (0x0311) [len 2]
              0x0001
          Attribute list: [len 180] {position 5}
            Attribute: Service Record Handle (0x0000) [len 2]
              0x00000000
            Attribute: Service Class ID List (0x0001) [len 2]
              UUID (3) with 2 bytes [0 extra bits] len 3
                Service Discovery Server Service Class (0x1000)
            Attribute: Service Record State (0x0002) [len 2]
              0x00000000
            Attribute: Protocol Descriptor List (0x0004) [len 2]
              Sequence (6) with 3 bytes [8 extra bits] len 5
                UUID (3) with 2 bytes [0 extra bits] len 3
                  L2CAP (0x0100)
              Sequence (6) with 3 bytes [8 extra bits] len 5
                UUID (3) with 2 bytes [0 extra bits] len 3
                  SDP (0x0001)
            Attribute: Language Base Attribute ID List (0x0006) [len 2]
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x656e
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0100
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6672
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0110
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6465
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0120
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6a61
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0130
            Attribute: Service Availability (0x0008) [len 2]
              0xff
            Attribute: Unknown (0x0200) [len 2]
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0100
            Attribute: Unknown (0x0201) [len 2]
              0x00000006
            Attribute: Unknown (0x0300) [len 2]
              String (4) with 3 bytes [8 extra bits] len 5
                4.0 [len 3]
              String (4) with 49 bytes [8 extra bits] len 51
                Copyright (c) 2007 Apple Inc. All rights reserved [len 49]
            Attribute: Unknown (0x0308) [len 2]
              www.apple.com [len 13]
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 24        #128 [hci0] 131.585014
      Channel: 3588 len 20 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Request (0x06) tid 4 len 15
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              PnP Information (0x1200)
        Max record count: 65535
        Attribute list: [len 7]
          Sequence (6) with 5 bytes [8 extra bits] len 7
            Unsigned Integer (1) with 4 bytes [0 extra bits] len 5
              0x0000ffff
        Continuation state: 0
> HCI Event: Number of Completed Packets (0x13) plen 5               #129 [hci0] 131.588060
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #126: len 26 (4 Kb/s)
        Latency: 42 msec (2-42 msec ~28 msec)
        Channel: 3588 [PSM 1 mode Basic (0x00)] {chan 0}
        Channel Latency: 42 msec (3-42 msec ~28 msec)
        #128: len 24 (64 Kb/s)
        Latency: 3 msec (2-42 msec ~15 msec)
        Channel: 3588 [PSM 1 mode Basic (0x00)] {chan 0}
        Channel Latency: 3 msec (3-42 msec ~15 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 175                         #130 [hci0] 131.596289
      Channel: 64 len 171 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Response (0x07) tid 4 len 166
        Attribute bytes: 163
          Attribute list: [len 159] {position 0}
            Attribute: Service Record Handle (0x0000) [len 2]
              0x4f491200
            Attribute: Service Class ID List (0x0001) [len 2]
              UUID (3) with 2 bytes [0 extra bits] len 3
                PnP Information (0x1200)
            Attribute: Service Record State (0x0002) [len 2]
              0x00000000
            Attribute: Browse Group List (0x0005) [len 2]
              UUID (3) with 2 bytes [0 extra bits] len 3
                Public Browse Root (0x1002)
            Attribute: Language Base Attribute ID List (0x0006) [len 2]
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x656e
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0100
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6672
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0110
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6465
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0120
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6a61
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0130
            Attribute: Service Availability (0x0008) [len 2]
              0xff
            Attribute: Unknown (0x0101) [len 2]
              PnP Information [len 15]
            Attribute: Unknown (0x0200) [len 2]
              0x0102
            Attribute: Unknown (0x0201) [len 2]
              0x004c
            Attribute: Unknown (0x0202) [len 2]
              0x200e
            Attribute: Unknown (0x0203) [len 2]
              0xd415
            Attribute: Unknown (0x0204) [len 2]
              true
            Attribute: Unknown (0x0205) [len 2]
              0x0001
            Attribute: Unknown (0xa000) [len 2]
              0x7ffb30ff
            Attribute: Unknown (0xa001) [len 2]
              0x000000000004740b
            Attribute: Unknown (0xafff) [len 2]
              0x0002
        Continuation state: 0
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 24        #131 [hci0] 131.601329
      Channel: 3588 len 20 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Request (0x06) tid 5 len 15
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              Handsfree (0x111e)
        Max record count: 65535
        Attribute list: [len 7]
          Sequence (6) with 5 bytes [8 extra bits] len 7
            Unsigned Integer (1) with 4 bytes [0 extra bits] len 5
              0x0000ffff
        Continuation state: 0
> ACL Data RX: Handle 12 flags 0x02 dlen 147                         #132 [hci0] 131.638288
      Channel: 64 len 143 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Response (0x07) tid 5 len 138
        Attribute bytes: 135
          Attribute list: [len 131] {position 0}
            Attribute: Service Record Handle (0x0000) [len 2]
              0x4f49111e
            Attribute: Service Class ID List (0x0001) [len 2]
              UUID (3) with 2 bytes [0 extra bits] len 3
                Handsfree (0x111e)
              UUID (3) with 2 bytes [0 extra bits] len 3
                Generic Audio (0x1203)
            Attribute: Service Record State (0x0002) [len 2]
              0x00000000
            Attribute: Protocol Descriptor List (0x0004) [len 2]
              Sequence (6) with 3 bytes [8 extra bits] len 5
                UUID (3) with 2 bytes [0 extra bits] len 3
                  L2CAP (0x0100)
              Sequence (6) with 5 bytes [8 extra bits] len 7
                UUID (3) with 2 bytes [0 extra bits] len 3
                  RFCOMM (0x0003)
                Unsigned Integer (1) with 1 byte [0 extra bits] len 2
                  0x07
            Attribute: Browse Group List (0x0005) [len 2]
              UUID (3) with 2 bytes [0 extra bits] len 3
                Public Browse Root (0x1002)
            Attribute: Language Base Attribute ID List (0x0006) [len 2]
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x656e
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0100
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6672
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0110
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6465
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0120
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x6a61
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x006a
              Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                0x0130
            Attribute: Service Availability (0x0008) [len 2]
              0xff
            Attribute: Bluetooth Profile Descriptor List (0x0009) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  Handsfree (0x111e)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0106
            Attribute: Unknown (0x0100) [len 2]
              Handsfree [len 9]
            Attribute: Unknown (0x0311) [len 2]
              0x003b
        Continuation state: 0
< ACL Data TX: Handle 12 flags 0x00 dlen 12                          #133 [hci0] 131.638487
      L2CAP: Connection Request (0x02) ident 5 len 4
        PSM: 3 (0x0003)
        Source CID: 65
> HCI Event: Number of Completed Packets (0x13) plen 5               #134 [hci0] 131.654090
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #131: len 24 (3 Kb/s)
        Latency: 52 msec (2-52 msec ~34 msec)
        Channel: 3588 [PSM 1 mode Basic (0x00)] {chan 0}
        Channel Latency: 52 msec (3-52 msec ~34 msec)
        #133: len 12 (6 Kb/s)
        Latency: 15 msec (2-52 msec ~24 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 16                          #135 [hci0] 131.659288
      L2CAP: Connection Response (0x03) ident 5 len 8
        Destination CID: 3845
        Source CID: 65
        Result: Connection pending (0x0001)
        Status: Authentication pending (0x0001)
> ACL Data RX: Handle 12 flags 0x02 dlen 16                          #136 [hci0] 131.659290
      L2CAP: Connection Response (0x03) ident 5 len 8
        Destination CID: 3845
        Source CID: 65
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 12 flags 0x00 dlen 16                          #137 [hci0] 131.659321
      L2CAP: Configure Request (0x04) ident 6 len 8
        Destination CID: 3845
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1021
> ACL Data RX: Handle 12 flags 0x02 dlen 18                          #138 [hci0] 131.668288
      L2CAP: Configure Response (0x05) ident 6 len 10
        Source CID: 65
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1021
> ACL Data RX: Handle 12 flags 0x02 dlen 12                          #139 [hci0] 131.668289
      L2CAP: Configure Request (0x04) ident 42 len 4
        Destination CID: 65
        Flags: 0x0000
< ACL Data TX: Handle 12 flags 0x00 dlen 18                          #140 [hci0] 131.668332
      L2CAP: Configure Response (0x05) ident 42 len 10
        Source CID: 3845
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
< ACL Data TX: Handle 12 flags 0x00 dlen 8                           #141 [hci0] 131.668432
      Channel: 3845 len 4 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Set Async Balance Mode (SABM) (0x2f)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0x3f poll/final 1
         Length: 0
         FCS: 0x1c
> HCI Event: Number of Completed Packets (0x13) plen 5               #142 [hci0] 131.672098
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #137: len 16 (10 Kb/s)
        Latency: 12 msec (2-52 msec ~18 msec)
        #140: len 18 (48 Kb/s)
        Latency: 3 msec (2-52 msec ~11 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 8                           #143 [hci0] 131.676472
      Channel: 65 len 4 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Ack (UA) (0x63)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0x73 poll/final 1
         Length: 0
         FCS: 0xd7
< ACL Data TX: Handle 12 flags 0x00 dlen 18                          #144 [hci0] 131.676589
      Channel: 3845 len 14 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0x70
         MCC Message type: DLC Parameter Negotiation CMD (0x20)
           Length: 8
           dlci 14 frame_type 0 credit_flow 15 pri 7
           ack_timer 0 frame_size 667 max_retrans 0 credits 7
> HCI Event: Number of Completed Packets (0x13) plen 5               #145 [hci0] 131.679105
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #141: len 8 (6 Kb/s)
        Latency: 10 msec (2-52 msec ~10 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 10 msec (10-10 msec ~10 msec)
        #144: len 18 (72 Kb/s)
        Latency: 2 msec (2-52 msec ~6 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 2 msec (2-10 msec ~6 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 18                          #146 [hci0] 131.682333
      Channel: 65 len 14 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x01 cr 0 dlci 0x00
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0xaa
         MCC Message type: DLC Parameter Negotiation RSP (0x20)
           Length: 8
           dlci 14 frame_type 0 credit_flow 14 pri 7
           ack_timer 0 frame_size 667 max_retrans 0 credits 0
< ACL Data TX: Handle 12 flags 0x00 dlen 8                           #147 [hci0] 131.682418
      Channel: 3845 len 4 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Set Async Balance Mode (SABM) (0x2f)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0x3f poll/final 1
         Length: 0
         FCS: 0x06
> ACL Data RX: Handle 12 flags 0x02 dlen 12                          #148 [hci0] 131.691289
      L2CAP: Connection Request (0x02) ident 43 len 4
        PSM: 1 (0x0001)
        Source CID: 4102
< ACL Data TX: Handle 12 flags 0x00 dlen 16                          #149 [hci0] 131.691330
      L2CAP: Connection Response (0x03) ident 43 len 8
        Destination CID: 66
        Source CID: 4102
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 12 flags 0x00 dlen 12                          #150 [hci0] 131.691337
      L2CAP: Configure Request (0x04) ident 7 len 4
        Destination CID: 4102
        Flags: 0x0000
> HCI Event: Number of Completed Packets (0x13) plen 5               #151 [hci0] 131.694086
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #147: len 8 (5 Kb/s)
        Latency: 11 msec (2-52 msec ~9 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 11 msec (2-11 msec ~9 msec)
        #149: len 16 (64 Kb/s)
        Latency: 2 msec (2-52 msec ~5 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 16                          #152 [hci0] 131.699429
      L2CAP: Configure Request (0x04) ident 44 len 8
        Destination CID: 66
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 128
> ACL Data RX: Handle 12 flags 0x02 dlen 18                          #153 [hci0] 131.699430
      L2CAP: Configure Response (0x05) ident 7 len 10
        Source CID: 66
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
< ACL Data TX: Handle 12 flags 0x00 dlen 18                          #154 [hci0] 131.699462
      L2CAP: Configure Response (0x05) ident 44 len 10
        Source CID: 4102
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 128
> HCI Event: Number of Completed Packets (0x13) plen 5               #155 [hci0] 131.713182
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #150: len 12 (4 Kb/s)
        Latency: 21 msec (2-52 msec ~13 msec)
        #154: len 18 (11 Kb/s)
        Latency: 13 msec (2-52 msec ~13 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 40                          #156 [hci0] 131.717445
      Channel: 66 len 36 [PSM 1 mode Basic (0x00)] {chan 2}
      SDP: Service Search Attribute Request (0x06) tid 1 len 31
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              PnP Information (0x1200)
        Max record count: 64
        Attribute list: [len 23]
          Sequence (6) with 21 bytes [8 extra bits] len 23
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0201
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0202
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0203
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0205
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0xa000
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0xa001
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0xafff
        Continuation state: 0
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 40        #157 [hci0] 131.717590
      Channel: 4102 len 36 [PSM 1 mode Basic (0x00)] {chan 2}
      SDP: Service Search Attribute Response (0x07) tid 1 len 31
        Attribute bytes: 28
          Attribute list: [len 24] {position 0}
            Attribute: Unknown (0x0201) [len 2]
              0x1d6b
            Attribute: Unknown (0x0202) [len 2]
              0x0246
            Attribute: Unknown (0x0203) [len 2]
              0x0548
            Attribute: Unknown (0x0205) [len 2]
              0x0002
        Continuation state: 0
> ACL Data RX: Handle 12 flags 0x02 dlen 22                          #158 [hci0] 131.725289
      Channel: 66 len 18 [PSM 1 mode Basic (0x00)] {chan 2}
      SDP: Service Search Attribute Request (0x06) tid 2 len 13
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              Handsfree Audio Gateway (0x111f)
        Max record count: 64
        Attribute list: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0004
        Continuation state: 0
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 33        #159 [hci0] 131.725441
      Channel: 4102 len 29 [PSM 1 mode Basic (0x00)] {chan 2}
      SDP: Service Search Attribute Response (0x07) tid 2 len 24
        Attribute bytes: 21
          Attribute list: [len 17] {position 0}
            Attribute: Protocol Descriptor List (0x0004) [len 2]
              Sequence (6) with 3 bytes [8 extra bits] len 5
                UUID (3) with 2 bytes [0 extra bits] len 3
                  L2CAP (0x0100)
              Sequence (6) with 5 bytes [8 extra bits] len 7
                UUID (3) with 2 bytes [0 extra bits] len 3
                  RFCOMM (0x0003)
                Unsigned Integer (1) with 1 byte [0 extra bits] len 2
                  0x0d
        Continuation state: 0
> HCI Event: Number of Completed Packets (0x13) plen 5               #160 [hci0] 131.729087
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #157: len 40 (29 Kb/s)
        Latency: 11 msec (2-52 msec ~12 msec)
        Channel: 4102 [PSM 1 mode Basic (0x00)] {chan 2}
        Channel Latency: 11 msec (11-11 msec ~11 msec)
        #159: len 33 (88 Kb/s)
        Latency: 3 msec (2-52 msec ~8 msec)
        Channel: 4102 [PSM 1 mode Basic (0x00)] {chan 2}
        Channel Latency: 3 msec (3-11 msec ~7 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 22                          #161 [hci0] 131.735292
      Channel: 66 len 18 [PSM 1 mode Basic (0x00)] {chan 2}
      SDP: Service Search Attribute Request (0x06) tid 3 len 13
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              Audio Source (0x110a)
        Max record count: 64
        Attribute list: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0004
        Continuation state: 0
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 37        #162 [hci0] 131.735428
      Channel: 4102 len 33 [PSM 1 mode Basic (0x00)] {chan 2}
      SDP: Service Search Attribute Response (0x07) tid 3 len 28
        Attribute bytes: 25
          Attribute list: [len 21] {position 0}
            Attribute: Protocol Descriptor List (0x0004) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  L2CAP (0x0100)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0019
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  AVDTP (0x0019)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0103
        Continuation state: 0
> ACL Data RX: Handle 12 flags 0x02 dlen 22                          #163 [hci0] 131.745338
      Channel: 66 len 18 [PSM 1 mode Basic (0x00)] {chan 2}
      SDP: Service Search Attribute Request (0x06) tid 4 len 13
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              A/V Remote Control Target (0x110c)
        Max record count: 64
        Attribute list: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0009
        Continuation state: 0
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 29        #164 [hci0] 131.745516
      Channel: 4102 len 25 [PSM 1 mode Basic (0x00)] {chan 2}
      SDP: Service Search Attribute Response (0x07) tid 4 len 20
        Attribute bytes: 17
          Attribute list: [len 13] {position 0}
            Attribute: Bluetooth Profile Descriptor List (0x0009) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  A/V Remote Control (0x110e)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0105
        Continuation state: 0
> HCI Event: Number of Completed Packets (0x13) plen 5               #165 [hci0] 131.749087
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #162: len 37 (22 Kb/s)
        Latency: 13 msec (2-52 msec ~10 msec)
        Channel: 4102 [PSM 1 mode Basic (0x00)] {chan 2}
        Channel Latency: 13 msec (3-13 msec ~10 msec)
        #164: len 29 (77 Kb/s)
        Latency: 3 msec (2-52 msec ~7 msec)
        Channel: 4102 [PSM 1 mode Basic (0x00)] {chan 2}
        Channel Latency: 3 msec (3-13 msec ~7 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 8                           #166 [hci0] 131.768333
      Channel: 65 len 4 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Ack (UA) (0x63)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0x73 poll/final 1
         Length: 0
         FCS: 0xcd
< ACL Data TX: Handle 12 flags 0x00 dlen 12                          #167 [hci0] 131.768415
      Channel: 3845 len 8 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0xef poll/final 0
         Length: 4
         FCS: 0x70
         MCC Message type: Modem Status Command CMD (0x38)
           Length: 2
           dlci 14 
           fc 0 rtc 1 rtr 1 ic 0 dv 1
< ACL Data TX: Handle 12 flags 0x00 dlen 12                          #168 [hci0] 131.768899
      L2CAP: Connection Request (0x02) ident 8 len 4
        PSM: 25 (0x0019)
        Source CID: 67
> ACL Data RX: Handle 12 flags 0x02 dlen 12                          #169 [hci0] 131.770351
      Channel: 65 len 8 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x01 cr 0 dlci 0x00
         Control: 0xef poll/final 0
         Length: 4
         FCS: 0xaa
         MCC Message type: Modem Status Command CMD (0x38)
           Length: 2
           dlci 14 
           fc 0 rtc 1 rtr 1 ic 0 dv 1
< ACL Data TX: Handle 12 flags 0x00 dlen 12                          #170 [hci0] 131.770470
      Channel: 3845 len 8 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0xef poll/final 0
         Length: 4
         FCS: 0x70
         MCC Message type: Modem Status Command RSP (0x38)
           Length: 2
           dlci 14 
           fc 0 rtc 1 rtr 1 ic 0 dv 1
> HCI Event: Number of Completed Packets (0x13) plen 5               #171 [hci0] 131.773057
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #167: len 12 (24 Kb/s)
        Latency: 4 msec (2-52 msec ~5 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 4 msec (2-11 msec ~6 msec)
        #168: len 12 (24 Kb/s)
        Latency: 4 msec (2-52 msec ~5 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 12                          #172 [hci0] 131.777340
      Channel: 65 len 8 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x01 cr 0 dlci 0x00
         Control: 0xef poll/final 0
         Length: 4
         FCS: 0xaa
         MCC Message type: Modem Status Command RSP (0x38)
           Length: 2
           dlci 14 
           fc 0 rtc 1 rtr 1 ic 0 dv 1
< ACL Data TX: Handle 12 flags 0x00 dlen 9                           #173 [hci0] 131.777464
      Channel: 3845 len 5 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0xff poll/final 1
         Length: 0
         FCS: 0xb9
         Credits: 33
        b9                                               .               
> ACL Data RX: Handle 12 flags 0x02 dlen 16                          #174 [hci0] 131.779340
      L2CAP: Connection Response (0x03) ident 8 len 8
        Destination CID: 4359
        Source CID: 67
        Result: Connection pending (0x0001)
        Status: Authentication pending (0x0001)
> HCI Event: Number of Completed Packets (0x13) plen 5               #175 [hci0] 131.780178
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #170: len 12 (10 Kb/s)
        Latency: 9 msec (2-52 msec ~7 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 9 msec (2-11 msec ~8 msec)
        #173: len 9 (36 Kb/s)
        Latency: 2 msec (2-52 msec ~5 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 2 msec (2-11 msec ~5 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 16                          #176 [hci0] 131.783288
      L2CAP: Connection Response (0x03) ident 8 len 8
        Destination CID: 4359
        Source CID: 67
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
> ACL Data RX: Handle 12 flags 0x02 dlen 9                           #177 [hci0] 131.783289
      Channel: 65 len 5 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x39 cr 0 dlci 0x0e
         Control: 0xff poll/final 1
         Length: 0
         FCS: 0x63
         Credits: 255
        63                                               c               
< ACL Data TX: Handle 12 flags 0x00 dlen 12                          #178 [hci0] 131.783302
      L2CAP: Configure Request (0x04) ident 9 len 4
        Destination CID: 4359
        Flags: 0x0000
> ACL Data RX: Handle 12 flags 0x02 dlen 20                          #179 [hci0] 131.788289
      Channel: 65 len 16 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x39 cr 0 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 12
         FCS: 0x7f
        41 54 2b 42 52 53 46 3d 36 36 37 0d 7f           AT+BRSF=667..   
> ACL Data RX: Handle 12 flags 0x02 dlen 20                          #180 [hci0] 131.788290
      L2CAP: Configure Request (0x04) ident 45 len 12
        Destination CID: 67
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
        Option: Flush Timeout (0x02) [mandatory]
          Flush timeout: 30
< ACL Data TX: Handle 12 flags 0x00 dlen 18                          #181 [hci0] 131.788337
      L2CAP: Configure Response (0x05) ident 45 len 10
        Source CID: 4359
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
< ACL Data TX: Handle 12 flags 0x00 dlen 23                          #182 [hci0] 131.788588
      Channel: 3845 len 19 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 15
         FCS: 0xa5
        0d 0a 2b 42 52 53 46 3a 20 31 36 33 32 0d 0a a5  ..+BRSF: 1632...
< ACL Data TX: Handle 12 flags 0x00 dlen 14                          #183 [hci0] 131.788595
      Channel: 3845 len 10 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0xa5
        0d 0a 4f 4b 0d 0a a5                             ..OK...         
> HCI Event: Number of Completed Packets (0x13) plen 5               #184 [hci0] 131.792181
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #178: len 12 (12 Kb/s)
        Latency: 8 msec (2-52 msec ~6 msec)
        #181: len 18 (48 Kb/s)
        Latency: 3 msec (2-52 msec ~5 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 18                          #185 [hci0] 131.793349
      L2CAP: Configure Response (0x05) ident 9 len 10
        Source CID: 67
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 6         #186 [hci0] 131.793527
      Channel: 4359 len 2 [PSM 25 mode Basic (0x00)] {chan 3}
      AVDTP: Discover (0x01) Command (0x00) type 0x00 label 9 nosp 0
> HCI Event: Number of Completed Packets (0x13) plen 5               #187 [hci0] 131.794057
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #182: len 23 (36 Kb/s)
        Latency: 5 msec (2-52 msec ~5 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 5 msec (2-11 msec ~5 msec)
        #183: len 14 (22 Kb/s)
        Latency: 5 msec (2-52 msec ~5 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 5 msec (2-11 msec ~5 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 12                          #188 [hci0] 131.800325
      L2CAP: Disconnection Request (0x06) ident 46 len 4
        Destination CID: 66
        Source CID: 4102
< ACL Data TX: Handle 12 flags 0x00 dlen 12                          #189 [hci0] 131.800367
      L2CAP: Disconnection Response (0x07) ident 46 len 4
        Destination CID: 66
        Source CID: 4102
> HCI Event: Number of Completed Packets (0x13) plen 5               #190 [hci0] 131.803087
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #186: len 6 (5 Kb/s)
        Latency: 9 msec (2-52 msec ~7 msec)
        Channel: 4359 [PSM 25 mode Basic (0x00)] {chan 3}
        Channel Latency: 9 msec (9-9 msec ~9 msec)
        #189: len 12 (48 Kb/s)
        Latency: 2 msec (2-52 msec ~5 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 27                          #191 [hci0] 131.804348
      Channel: 65 len 23 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x39 cr 0 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 19
         FCS: 0x7f
        41 54 2b 42 41 43 3d 31 2c 32 2c 31 32 38 2c 32  AT+BAC=1,2,128,2
        35 36 0d 7f                                      56..            
> ACL Data RX: Handle 12 flags 0x02 dlen 12                          #192 [hci0] 131.804349
      Channel: 67 len 8 [PSM 25 mode Basic (0x00)] {chan 3}
      AVDTP: Discover (0x01) Response Accept (0x02) type 0x00 label 9 nosp 0
        ACP SEID: 1
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
        ACP SEID: 2
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
        ACP SEID: 3
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
< ACL Data TX: Handle 12 flags 0x00 dlen 14                          #193 [hci0] 131.804510
      Channel: 3845 len 10 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0xa5
        0d 0a 4f 4b 0d 0a a5                             ..OK...         
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 20        #194 [hci0] 131.807001
      Channel: 4359 len 16 [PSM 25 mode Basic (0x00)] {chan 3}
      AVDTP: Set Configuration (0x03) Command (0x00) type 0x00 label 10 nosp 0
        ACP SEID: 1
        INT SEID: 7
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: SBC (0x00)
            Frequency: 48000 (0x10)
            Channel Mode: Joint Stereo (0x01)
            Block Length: 16 (0x10)
            Subbands: 8 (0x04)
            Allocation Method: Loudness (0x01)
            Minimum Bitpool: 2
            Maximum Bitpool: 53
        Service Category: Delay Reporting (0x08)
> HCI Event: Number of Completed Packets (0x13) plen 5               #195 [hci0] 131.810060
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #193: len 14 (22 Kb/s)
        Latency: 5 msec (2-52 msec ~5 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 5 msec (2-11 msec ~5 msec)
        #194: len 20 (53 Kb/s)
        Latency: 3 msec (2-52 msec ~4 msec)
        Channel: 4359 [PSM 25 mode Basic (0x00)] {chan 3}
        Channel Latency: 3 msec (3-9 msec ~6 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 18                          #196 [hci0] 131.812333
      Channel: 65 len 14 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x39 cr 0 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0x7f
        41 54 2b 43 49 4e 44 3d 3f 0d 7f                 AT+CIND=?..     
< ACL Data TX: Handle 12 flags 0x00 dlen 140                         #197 [hci0] 131.812532
      Channel: 3845 len 136 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 131
         FCS: 0xa5
        0d 0a 2b 43 49 4e 44 3a 28 22 73 65 72 76 69 63  ..+CIND:("servic
        65 22 2c 28 30 2d 31 29 29 2c 28 22 63 61 6c 6c  e",(0-1)),("call
        22 2c 28 30 2d 31 29 29 2c 28 22 63 61 6c 6c 73  ",(0-1)),("calls
        65 74 75 70 22 2c 28 30 2d 33 29 29 2c 28 22 63  etup",(0-3)),("c
        61 6c 6c 68 65 6c 64 22 2c 28 30 2d 32 29 29 2c  allheld",(0-2)),
        28 22 73 69 67 6e 61 6c 22 2c 28 30 2d 35 29 29  ("signal",(0-5))
        2c 28 22 72 6f 61 6d 22 2c 28 30 2d 31 29 29 2c  ,("roam",(0-1)),
        28 22 62 61 74 74 63 68 67 22 2c 28 30 2d 35 29  ("battchg",(0-5)
        29 0d 0a a5                                      )...            
< ACL Data TX: Handle 12 flags 0x00 dlen 14                          #198 [hci0] 131.812541
      Channel: 3845 len 10 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0xa5
        0d 0a 4f 4b 0d 0a a5                             ..OK...         
> ACL Data RX: Handle 12 flags 0x02 dlen 6                           #199 [hci0] 131.816287
      Channel: 67 len 2 [PSM 25 mode Basic (0x00)] {chan 3}
      AVDTP: Set Configuration (0x03) Response Accept (0x02) type 0x00 label 10 nosp 0
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 7         #200 [hci0] 131.817363
      Channel: 4359 len 3 [PSM 25 mode Basic (0x00)] {chan 3}
      AVDTP: Open (0x06) Command (0x00) type 0x00 label 11 nosp 0
        ACP SEID: 1
> HCI Event: Number of Completed Packets (0x13) plen 5               #201 [hci0] 131.818056
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #197: len 140 (224 Kb/s)
        Latency: 5 msec (2-52 msec ~4 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 5 msec (2-11 msec ~5 msec)
        #198: len 14 (22 Kb/s)
        Latency: 5 msec (2-52 msec ~5 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 5 msec (2-11 msec ~5 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 9                           #202 [hci0] 131.818398
      Channel: 67 len 5 [PSM 25 mode Basic (0x00)] {chan 3}
      AVDTP: Delay Report (0x0d) Command (0x00) type 0x00 label 4 nosp 0
        ACP SEID: 7
        Delay: 150.0ms
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 6         #203 [hci0] 131.818542
      Channel: 4359 len 2 [PSM 25 mode Basic (0x00)] {chan 3}
      AVDTP: Delay Report (0x0d) Response Accept (0x02) type 0x00 label 4 nosp 0
> HCI Event: Number of Completed Packets (0x13) plen 5               #204 [hci0] 131.822084
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #200: len 7 (14 Kb/s)
        Latency: 4 msec (2-52 msec ~4 msec)
        Channel: 4359 [PSM 25 mode Basic (0x00)] {chan 3}
        Channel Latency: 4 msec (3-9 msec ~5 msec)
        #203: len 6 (16 Kb/s)
        Latency: 3 msec (2-52 msec ~4 msec)
        Channel: 4359 [PSM 25 mode Basic (0x00)] {chan 3}
        Channel Latency: 3 msec (3-9 msec ~4 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 17                          #205 [hci0] 131.823289
      Channel: 65 len 13 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x39 cr 0 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 9
         FCS: 0x7f
        41 54 2b 43 49 4e 44 3f 0d 7f                    AT+CIND?..      
< ACL Data TX: Handle 12 flags 0x00 dlen 32                          #206 [hci0] 131.823436
      Channel: 3845 len 28 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 24
         FCS: 0xa5
        0d 0a 2b 43 49 4e 44 3a 20 30 2c 30 2c 30 2c 30  ..+CIND: 0,0,0,0
        2c 30 2c 30 2c 30 0d 0a a5                       ,0,0,0...       
< ACL Data TX: Handle 12 flags 0x00 dlen 14                          #207 [hci0] 131.823443
      Channel: 3845 len 10 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0xa5
        0d 0a 4f 4b 0d 0a a5                             ..OK...         
> ACL Data RX: Handle 12 flags 0x02 dlen 6                           #208 [hci0] 131.827290
      Channel: 67 len 2 [PSM 25 mode Basic (0x00)] {chan 3}
      AVDTP: Open (0x06) Response Accept (0x02) type 0x00 label 11 nosp 0
> ACL Data RX: Handle 12 flags 0x02 dlen 7                           #209 [hci0] 131.827291
      Channel: 67 len 3 [PSM 25 mode Basic (0x00)] {chan 3}
      AVDTP: Abort (0x0a) Command (0x00) type 0x00 label 5 nosp 0
        ACP SEID: 7
< ACL Data TX: Handle 12 flags 0x00 dlen 12                          #210 [hci0] 131.827462
      L2CAP: Connection Request (0x02) ident 10 len 4
        PSM: 25 (0x0019)
        Source CID: 66
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 6         #211 [hci0] 131.827471
      Channel: 4359 len 2 [PSM 25 mode Basic (0x00)] {chan 3}
      AVDTP: Abort (0x0a) Response Accept (0x02) type 0x00 label 5 nosp 0
> HCI Event: Number of Completed Packets (0x13) plen 5               #212 [hci0] 131.828057
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #206: len 32 (64 Kb/s)
        Latency: 4 msec (2-52 msec ~4 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 4 msec (2-11 msec ~5 msec)
        #207: len 14 (28 Kb/s)
        Latency: 4 msec (2-52 msec ~4 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 4 msec (2-11 msec ~4 msec)
bluetoothd[1272]: @ MGMT Command: Set Connect.. (0x0007) plen 1  {0x0001} [hci0] 131.828126
        Connectable: Enabled (0x01)
< HCI Command: Write Scan Enable (0x03|0x001a) plen 1                #213 [hci0] 131.828137
        Scan enable: Page Scan (0x02)
> HCI Event: Command Complete (0x0e) plen 4                          #214 [hci0] 131.829054
      Write Scan Enable (0x03|0x001a) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 7                   {0x0001} [hci0] 131.829063
      Set Connectable (0x0007) plen 4
        Status: Success (0x00)
        Current settings: 0x000000d3
          Powered
          Connectable
          Bondable
          Secure Simple Pairing
          BR/EDR
bluetoothd[1272]: @ MGMT Command: Set Discove.. (0x0006) plen 3  {0x0001} [hci0] 131.829116
        Discoverable: General (0x01)
        Timeout: 0
< HCI Command: Write Current IAC LAP (0x03|0x003a) plen 4            #215 [hci0] 131.829122
        Number of IAC: 1
        Access code: 0x9e8b33 (General Inquiry)
> HCI Event: Command Complete (0x0e) plen 4                          #216 [hci0] 131.830085
      Write Current IAC LAP (0x03|0x003a) ncmd 1
        Status: Success (0x00)
< HCI Command: Write Scan Enable (0x03|0x001a) plen 1                #217 [hci0] 131.830120
        Scan enable: Inquiry Scan + Page Scan (0x03)
> HCI Event: Command Complete (0x0e) plen 4                          #218 [hci0] 131.831086
      Write Scan Enable (0x03|0x001a) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 7                   {0x0001} [hci0] 131.831126
      Set Discoverable (0x0006) plen 4
        Status: Success (0x00)
        Current settings: 0x000000db
          Powered
          Connectable
          Discoverable
          Bondable
          Secure Simple Pairing
          BR/EDR
bluetoothd[1272]: @ MGMT Command: Start Disco.. (0x0023) plen 1  {0x0001} [hci0] 131.831184
        Address type: 0x01
          BR/EDR
< HCI Command: Inquiry (0x01|0x0001) plen 5                          #219 [hci0] 131.831191
        Access code: 0x9e8b33 (General Inquiry)
        Length: 10.24s (0x08)
        Num responses: 0
> HCI Event: Command Status (0x0f) plen 4                            #220 [hci0] 131.832084
      Inquiry (0x01|0x0001) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 4                   {0x0001} [hci0] 131.832125
      Start Discovery (0x0023) plen 1
        Status: Success (0x00)
        Address type: 0x01
          BR/EDR
@ MGMT Event: Discovering (0x0013) plen 2                        {0x0001} [hci0] 131.832127
        Address type: 0x01
          BR/EDR
        Discovery: Enabled (0x01)
> ACL Data RX: Handle 12 flags 0x02 dlen 24                          #221 [hci0] 131.896338
      Channel: 65 len 20 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x39 cr 0 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 16
         FCS: 0x7f
        41 54 2b 43 4d 45 52 3d 33 2c 30 2c 30 2c 31 0d  AT+CMER=3,0,0,1.
        7f                                               .               
< ACL Data TX: Handle 12 flags 0x00 dlen 14                          #222 [hci0] 131.896542
      Channel: 3845 len 10 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0xa5
        0d 0a 4f 4b 0d 0a a5                             ..OK...         
< ACL Data TX: Handle 12 flags 0x00 dlen 19                          #223 [hci0] 131.896549
      Channel: 3845 len 15 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 11
         FCS: 0xa5
        0d 0a 2b 42 43 53 3a 20 32 0d 0a a5              ..+BCS: 2...    
> HCI Event: Number of Completed Packets (0x13) plen 5               #224 [hci0] 131.897178
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #210: len 12 (1 Kb/s)
        Latency: 69 msec (2-69 msec ~37 msec)
        #211: len 6 (0 Kb/s)
        Latency: 69 msec (2-69 msec ~53 msec)
        Channel: 4359 [PSM 25 mode Basic (0x00)] {chan 3}
        Channel Latency: 69 msec (3-69 msec ~37 msec)
> HCI Event: Number of Completed Packets (0x13) plen 5               #225 [hci0] 131.964196
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #222: len 14 (1 Kb/s)
        Latency: 67 msec (2-69 msec ~60 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 67 msec (2-67 msec ~36 msec)
        #223: len 19 (2 Kb/s)
        Latency: 67 msec (2-69 msec ~64 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 67 msec (2-67 msec ~51 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 16                          #226 [hci0] 131.965355
      L2CAP: Connection Response (0x03) ident 10 len 8
        Destination CID: 4614
        Source CID: 66
        Result: Connection pending (0x0001)
        Status: Authentication pending (0x0001)
> ACL Data RX: Handle 12 flags 0x02 dlen 12                          #227 [hci0] 131.965356
      L2CAP: Disconnection Request (0x06) ident 47 len 4
        Destination CID: 67
        Source CID: 4359
< ACL Data TX: Handle 12 flags 0x00 dlen 12                          #228 [hci0] 131.965395
      L2CAP: Disconnection Response (0x07) ident 47 len 4
        Destination CID: 67
        Source CID: 4359
> ACL Data RX: Handle 12 flags 0x02 dlen 16                          #229 [hci0] 132.032442
      L2CAP: Connection Response (0x03) ident 10 len 8
        Destination CID: 4614
        Source CID: 66
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
> ACL Data RX: Handle 12 flags 0x02 dlen 20                          #230 [hci0] 132.032444
      L2CAP: Configure Request (0x04) ident 48 len 12
        Destination CID: 66
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
        Option: Flush Timeout (0x02) [mandatory]
          Flush timeout: 30
< ACL Data TX: Handle 12 flags 0x00 dlen 14                          #231 [hci0] 132.032483
      L2CAP: Command Reject (0x01) ident 48 len 6
        Reason: Invalid CID in request (0x0002)
        Destination CID: 66
        Source CID: 0
> ACL Data RX: Handle 12 flags 0x02 dlen 18                          #232 [hci0] 132.035439
      Channel: 65 len 14 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x39 cr 0 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0x7f
        41 54 2b 56 47 53 3d 31 31 0d 7f                 AT+VGS=11..     
< ACL Data TX: Handle 12 flags 0x00 dlen 14                          #233 [hci0] 132.035637
      Channel: 3845 len 10 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0xa5
        0d 0a 4f 4b 0d 0a a5                             ..OK...         
> HCI Event: Number of Completed Packets (0x13) plen 5               #234 [hci0] 132.102064
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #228: len 12 (0 Kb/s)
        Latency: 136 msec (2-136 msec ~100 msec)
        #231: len 14 (1 Kb/s)
        Latency: 69 msec (2-136 msec ~84 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 17                          #235 [hci0] 132.170424
      Channel: 65 len 13 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x39 cr 0 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 9
         FCS: 0x7f
        41 54 2b 56 47 4d 3d 38 0d 7f                    AT+VGM=8..      
< ACL Data TX: Handle 12 flags 0x00 dlen 14                          #236 [hci0] 132.170619
      Channel: 3845 len 10 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0xa5
        0d 0a 4f 4b 0d 0a a5                             ..OK...         
> HCI Event: Number of Completed Packets (0x13) plen 5               #237 [hci0] 132.237092
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #233: len 14 (0 Kb/s)
        Latency: 201 msec (2-201 msec ~143 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 201 msec (2-201 msec ~126 msec)
        #236: len 14 (1 Kb/s)
        Latency: 66 msec (2-201 msec ~104 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 66 msec (2-201 msec ~96 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 29                          #238 [hci0] 132.306323
      Channel: 65 len 25 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x39 cr 0 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 21
         FCS: 0x7f
        41 54 2b 42 49 41 3d 30 2c 31 2c 31 2c 31 2c 30  AT+BIA=0,1,1,1,0
        2c 30 2c 30 0d 7f                                ,0,0..          
< ACL Data TX: Handle 12 flags 0x00 dlen 14                          #239 [hci0] 132.306481
      Channel: 3845 len 10 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0xa5
        0d 0a 4f 4b 0d 0a a5                             ..OK...         
> ACL Data RX: Handle 12 flags 0x02 dlen 18                          #240 [hci0] 132.442444
      Channel: 65 len 14 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x39 cr 0 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0x7f
        41 54 2b 4e 52 45 43 3d 30 0d 7f                 AT+NREC=0..     
< ACL Data TX: Handle 12 flags 0x00 dlen 17                          #241 [hci0] 132.442674
      Channel: 3845 len 13 [PSM 3 mode Basic (0x00)] {chan 1}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x3b cr 1 dlci 0x0e
         Control: 0xef poll/final 0
         Length: 9
         FCS: 0xa5
        0d 0a 45 52 52 4f 52 0d 0a a5                    ..ERROR...      
> HCI Event: Number of Completed Packets (0x13) plen 5               #242 [hci0] 132.509123
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #239: len 14 (0 Kb/s)
        Latency: 202 msec (2-202 msec ~153 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 202 msec (2-202 msec ~149 msec)
        #241: len 17 (2 Kb/s)
        Latency: 66 msec (2-202 msec ~110 msec)
        Channel: 3845 [PSM 3 mode Basic (0x00)] {chan 1}
        Channel Latency: 66 msec (2-202 msec ~108 msec)
< ACL Data TX: Handle 12 flags 0x00 dlen 12                          #243 [hci0] 133.600398
      L2CAP: Disconnection Request (0x06) ident 11 len 4
        Destination CID: 3588
        Source CID: 64
< ACL Data TX: Handle 12 flags 0x00 dlen 12                          #244 [hci0] 133.600410
      L2CAP: Connection Request (0x02) ident 12 len 4
        PSM: 25 (0x0019)
        Source CID: 66
> HCI Event: Number of Completed Packets (0x13) plen 5               #245 [hci0] 133.680179
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #243: len 12 (1 Kb/s)
        Latency: 79 msec (2-202 msec ~94 msec)
        #244: len 12 (1 Kb/s)
        Latency: 79 msec (2-202 msec ~87 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 12                          #246 [hci0] 133.681341
      L2CAP: Disconnection Response (0x07) ident 11 len 4
        Destination CID: 3588
        Source CID: 64
> ACL Data RX: Handle 12 flags 0x02 dlen 16                          #247 [hci0] 133.681342
      L2CAP: Connection Response (0x03) ident 12 len 8
        Destination CID: 4868
        Source CID: 66
        Result: Connection pending (0x0001)
        Status: Authentication pending (0x0001)
> ACL Data RX: Handle 12 flags 0x02 dlen 16                          #248 [hci0] 133.684333
      L2CAP: Connection Response (0x03) ident 12 len 8
        Destination CID: 4868
        Source CID: 66
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 12 flags 0x00 dlen 12                          #249 [hci0] 133.684363
      L2CAP: Configure Request (0x04) ident 13 len 4
        Destination CID: 4868
        Flags: 0x0000
> ACL Data RX: Handle 12 flags 0x02 dlen 20                          #250 [hci0] 133.751435
      L2CAP: Configure Request (0x04) ident 49 len 12
        Destination CID: 66
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
        Option: Flush Timeout (0x02) [mandatory]
          Flush timeout: 30
< ACL Data TX: Handle 12 flags 0x00 dlen 18                          #251 [hci0] 133.751467
      L2CAP: Configure Response (0x05) ident 49 len 10
        Source CID: 4868
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
> HCI Event: Number of Completed Packets (0x13) plen 5               #252 [hci0] 133.818177
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #249: len 12 (0 Kb/s)
        Latency: 133 msec (2-202 msec ~110 msec)
        #251: len 18 (2 Kb/s)
        Latency: 66 msec (2-202 msec ~88 msec)
> ACL Data RX: Handle 12 flags 0x02 dlen 18                          #253 [hci0] 133.819319
      L2CAP: Configure Response (0x05) ident 13 len 10
        Source CID: 66
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
bluetoothd[1272]: < ACL Data TX: Handle 12 flags 0x00 dlen 6         #254 [hci0] 133.819610
      Channel: 4868 len 2 [PSM 25 mode Basic (0x00)] {chan 2}
> HCI Event: Vendor (0xff) plen 4                                    #255 [hci0] 134.025189
        49 0c 00 c4                                      I...            
> HCI Event: Number of Completed Packets (0x13) plen 5               #256 [hci0] 134.027185
        Num handles: 1
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 1
        #254: len 6 (0 Kb/s)
        Latency: 207 msec (2-207 msec ~148 msec)
        Channel: 4868 [PSM 25 mode Basic (0x00)] {chan 2}
        Channel Latency: 207 msec (207-207 msec ~207 msec)
> HCI Event: Disconnect Complete (0x05) plen 4                       #257 [hci0] 134.028184
        Status: Success (0x00)
        Handle: 12 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Reason: Remote User Terminated Connection (0x13)
@ MGMT Event: Device Disconnected (0x000c) plen 8                {0x0001} [hci0] 134.028218
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Reason: Connection terminated by remote host (0x03)
bluetoothd[1272]: = src/profile.c:ext_io_disconnected() Unable to get io dat..   134.028424
> HCI Event: Extended Inquiry Result (0x2f) plen 255                 #258 [hci0] 137.779160
        Num responses: 1
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page scan repetition mode: R1 (0x01)
        Page period mode: P0 (0x00)
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
        Clock offset: 0x4a01
        RSSI: -73 dBm (0xb7)
        Device ID: Bluetooth SIG assigned (0x0001)
          Vendor: Apple, Inc. (76)
          Product: 0x200e
          Version: 1.5.0 (0x0150)
        Name (complete): AirPods Pro
@ MGMT Event: Device Found (0x0012) plen 42                      {0x0001} [hci0] 137.779198
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        RSSI: -73 dBm (0xb7)
        Flags: 0x00000000
        Data length: 28
        Device ID: Bluetooth SIG assigned (0x0001)
          Vendor: Apple, Inc. (76)
          Product: 0x200e
          Version: 1.5.0 (0x0150)
        Name (complete): AirPods Pro
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
> HCI Event: Inquiry Complete (0x01) plen 1                          #259 [hci0] 142.074923
        Status: Success (0x00)
@ MGMT Event: Discovering (0x0013) plen 2                        {0x0001} [hci0] 142.075038
        Address type: 0x01
          BR/EDR
        Discovery: Disabled (0x00)
          Audio (Speaker, Microphone, Headset)
> HCI Event: Inquiry Complete (0x01) plen 1                          #263 [hci0] 157.849435
        Status: Success (0x00)
@ MGMT Event: Discovering (0x0013) plen 2                        {0x0001} [hci0] 157.849474
        Address type: 0x01
          BR/EDR
        Discovery: Disabled (0x00)
bluetoothd[1272]: @ MGMT Command: Start Disco.. (0x0023) plen 1  {0x0001} [hci0] 163.604435
        Address type: 0x01
          BR/EDR
< HCI Command: Inquiry (0x01|0x0001) plen 5                          #264 [hci0] 163.604518
        Access code: 0x9e8b33 (General Inquiry)
        Length: 10.24s (0x08)
        Num responses: 0
> HCI Event: Command Status (0x0f) plen 4                            #265 [hci0] 163.605320
      Inquiry (0x01|0x0001) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 4                   {0x0001} [hci0] 163.605395
      Start Discovery (0x0023) plen 1
        Status: Success (0x00)
        Address type: 0x01
          BR/EDR
@ MGMT Event: Discovering (0x0013) plen 2                        {0x0001} [hci0] 163.605397
        Address type: 0x01
          BR/EDR
        Discovery: Enabled (0x01)
bluetoothd[1272]: @ MGMT Command: Set Connect.. (0x0007) plen 1  {0x0001} [hci0] 166.956354
        Connectable: Disabled (0x00)
< HCI Command: Inquiry Cancel (0x01|0x0002) plen 0                   #266 [hci0] 166.956354
> HCI Event: Command Complete (0x0e) plen 4                          #267 [hci0] 166.958141
      Inquiry Cancel (0x01|0x0002) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Discovering (0x0013) plen 2                        {0x0001} [hci0] 166.958175
        Address type: 0x01
          BR/EDR
        Discovery: Disabled (0x00)
< HCI Command: Create Connection (0x01|0x0005) plen 13               #268 [hci0] 166.958232
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Packet type: 0xcc18
          DM1 may be used
          DH1 may be used
          DM3 may be used
          DH3 may be used
          DM5 may be used
          DH5 may be used
        Page scan repetition mode: R2 (0x02)
        Page scan mode: Mandatory (0x00)
        Clock offset: 0x0000
        Role switch: Allow peripheral (0x01)
> HCI Event: Command Status (0x0f) plen 4                            #269 [hci0] 166.962107
      Create Connection (0x01|0x0005) ncmd 1
        Status: Success (0x00)
> HCI Event: Connect Complete (0x03) plen 11                         #270 [hci0] 168.906182
        Status: ACL Connection Already Exists (0x0b)
        Handle: 11
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Link type: ACL (0x01)
        Encryption: Disabled (0x00)
@ MGMT Event: Connect Failed (0x000d) plen 8                     {0x0001} [hci0] 168.906220
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Status: Already Connected (0x09)
bluetoothd[1272]: = src/profile.c:record_cb() Unable to get Hands-Free Voice..   168.906386
< HCI Command: Write Scan Enable (0x03|0x001a) plen 1                #271 [hci0] 168.919521
        Scan enable: Page Scan (0x02)
> HCI Event: Command Complete (0x0e) plen 4                          #272 [hci0] 168.920180
      Write Scan Enable (0x03|0x001a) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 7                   {0x0001} [hci0] 168.920260
      Set Connectable (0x0007) plen 4
        Status: Success (0x00)
        Current settings: 0x000000d1
          Powered
          Bondable
          Secure Simple Pairing
          BR/EDR
< HCI Command: Create Connection (0x01|0x0005) plen 13               #273 [hci0] 168.920267
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Packet type: 0xcc18
          DM1 may be used
          DH1 may be used
          DM3 may be used
          DH3 may be used
          DM5 may be used
          DH5 may be used
        Page scan repetition mode: R2 (0x02)
        Page scan mode: Mandatory (0x00)
        Clock offset: 0x0000
        Role switch: Allow peripheral (0x01)
> HCI Event: Command Status (0x0f) plen 4                            #274 [hci0] 168.921104
      Create Connection (0x01|0x0005) ncmd 1
        Status: ACL Connection Already Exists (0x0b)
bluetoothd[1272]: = profiles/audio/avdtp.c:avdtp_connect_cb() connect to 50:..   168.922911
bluetoothd[1272]: @ MGMT Command: Stop Discov.. (0x0024) plen 1  {0x0001} [hci0] 168.922911
        Address type: 0x01
          BR/EDR
> HCI Event: Connect Request (0x04) plen 10                          #275 [hci0] 168.927059
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
        Link type: ACL (0x01)
< HCI Command: Accept Connection Request (0x01|0x0009) plen 7        #276 [hci0] 168.941346
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Role: Central (0x00)
@ MGMT Event: Command Complete (0x0001) plen 4                   {0x0001} [hci0] 168.941392
      Stop Discovery (0x0024) plen 1
        Status: Rejected (0x0b)
        Address type: 0x01
          BR/EDR
bluetoothd[1272]: @ MGMT Command: Set Connect.. (0x0007) plen 1  {0x0001} [hci0] 168.942055
        Connectable: Enabled (0x01)
> HCI Event: Command Status (0x0f) plen 4                            #277 [hci0] 168.942056
      Accept Connection Request (0x01|0x0009) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 7                   {0x0001} [hci0] 168.942065
      Set Connectable (0x0007) plen 4
        Status: Success (0x00)
        Current settings: 0x000000d3
          Powered
          Connectable
          Bondable
          Secure Simple Pairing
          BR/EDR
bluetoothd[1272]: @ MGMT Command: Set Discove.. (0x0006) plen 3  {0x0001} [hci0] 168.942076
        Discoverable: General (0x01)
        Timeout: 0
< HCI Command: Write Current IAC LAP (0x03|0x003a) plen 4            #278 [hci0] 168.942081
        Number of IAC: 1
        Access code: 0x9e8b33 (General Inquiry)
> HCI Event: Command Complete (0x0e) plen 4                          #279 [hci0] 168.944057
      Write Current IAC LAP (0x03|0x003a) ncmd 1
        Status: Success (0x00)
< HCI Command: Write Scan Enable (0x03|0x001a) plen 1                #280 [hci0] 168.944074
        Scan enable: Inquiry Scan + Page Scan (0x03)
> HCI Event: Command Complete (0x0e) plen 4                          #281 [hci0] 168.945052
      Write Scan Enable (0x03|0x001a) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 7                   {0x0001} [hci0] 168.945091
      Set Discoverable (0x0006) plen 4
        Status: Success (0x00)
        Current settings: 0x000000db
          Powered
          Connectable
          Discoverable
          Bondable
          Secure Simple Pairing
          BR/EDR
> HCI Event: Role Change (0x12) plen 8                               #282 [hci0] 169.103081
        Status: Success (0x00)
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Role: Central (0x00)
> HCI Event: Connect Complete (0x03) plen 11                         #283 [hci0] 169.117079
        Status: Success (0x00)
        Handle: 11
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Link type: ACL (0x01)
        Encryption: Disabled (0x00)
< HCI Command: Read Remote Supported Features (0x01|0x001b) plen 2   #284 [hci0] 169.117131
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
btmon[6677]: @ RAW Open: btmon (privileged) version 2.22                {0x0002} 169.117221
btmon[6677]: @ RAW Close: btmon                                         {0x0002} 169.117224
> HCI Event: Command Status (0x0f) plen 4                            #285 [hci0] 169.118068
      Read Remote Supported Features (0x01|0x001b) ncmd 1
        Status: Success (0x00)
> HCI Event: Read Remote Supported Features (0x0b) plen 11           #286 [hci0] 169.119053
        Status: Success (0x00)
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Features: 0xbf 0xfe 0x2f 0xfe 0xdb 0xff 0x7b 0x87
          3 slot packets
          5 slot packets
          Encryption
          Slot offset
          Timing accuracy
          Role switch
          Sniff mode
          Power control requests
          Channel quality driven data rate (CQDDR)
          SCO link
          HV2 packets
          HV3 packets
          u-law log synchronous data
          A-law log synchronous data
          CVSD synchronous data
          Paging parameter negotiation
          Power control
          Transparent synchronous data
          Flow control lag (middle bit)
          Enhanced Data Rate ACL 2 Mbps mode
          Enhanced Data Rate ACL 3 Mbps mode
          Enhanced inquiry scan
          Interlaced inquiry scan
          Interlaced page scan
          RSSI with inquiry results
          Extended SCO link (EV3 packets)
          EV4 packets
          EV5 packets
          AFH capable peripheral
          AFH classification peripheral
          LE Supported (Controller)
          3-slot Enhanced Data Rate ACL packets
          5-slot Enhanced Data Rate ACL packets
          Sniff subrating
          Pause encryption
          AFH capable central
          AFH classification central
          Enhanced Data Rate eSCO 2 Mbps mode
          Enhanced Data Rate eSCO 3 Mbps mode
          3-slot Enhanced Data Rate eSCO packets
          Extended Inquiry Response
          Simultaneous LE and BR/EDR (Controller)
          Secure Simple Pairing
          Encapsulated PDU
          Erroneous Data Reporting
          Non-flushable Packet Boundary Flag
          Link Supervision Timeout Changed Event
          Inquiry TX Power Level
          Enhanced Power Control
          Extended features
< HCI Command: Read Remote Extended Features (0x01|0x001c) plen 3    #287 [hci0] 169.119064
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page: 1
> HCI Event: Command Status (0x0f) plen 4                            #288 [hci0] 169.120060
      Read Remote Extended Features (0x01|0x001c) ncmd 1
        Status: Success (0x00)
> HCI Event: Read Remote Extended Features (0x23) plen 13            #289 [hci0] 169.121056
        Status: Success (0x00)
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page: 1/1
        Features: 0x07 0x00 0x00 0x00 0x00 0x00 0x00 0x00
          Secure Simple Pairing (Host Support)
          LE Supported (Host)
          Simultaneous LE and BR/EDR (Host)
< HCI Command: Remote Name Request (0x01|0x0019) plen 10             #290 [hci0] 169.121090
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page scan repetition mode: R2 (0x02)
        Page scan mode: Mandatory (0x00)
        Clock offset: 0x0000
< ACL Data TX: Handle 11 flags 0x00 dlen 10                          #291 [hci0] 169.121099
      L2CAP: Information Request (0x0a) ident 1 len 2
        Type: Extended features supported (0x0002)
> HCI Event: Command Status (0x0f) plen 4                            #292 [hci0] 169.122050
      Remote Name Request (0x01|0x0019) ncmd 1
        Status: Success (0x00)
> HCI Event: Max Slots Change (0x1b) plen 3                          #293 [hci0] 169.123049
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Max slots: 5
> ACL Data RX: Handle 11 flags 0x02 dlen 16                          #294 [hci0] 169.149450
      L2CAP: Information Response (0x0b) ident 1 len 8
        Type: Extended features supported (0x0002)
        Result: Success (0x0000)
        Features: 0x00000280
          Fixed Channels
          Unicast Connectionless Data Reception
< ACL Data TX: Handle 11 flags 0x00 dlen 10                          #295 [hci0] 169.149489
      L2CAP: Information Request (0x0a) ident 2 len 2
        Type: Fixed channels supported (0x0003)
> ACL Data RX: Handle 11 flags 0x02 dlen 10                          #296 [hci0] 169.154451
      L2CAP: Information Request (0x0a) ident 53 len 2
        Type: Extended features supported (0x0002)
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #297 [hci0] 169.154463
      L2CAP: Information Response (0x0b) ident 53 len 8
        Type: Extended features supported (0x0002)
        Result: Success (0x0000)
        Features: 0x000002b8
          Enhanced Retransmission Mode
          Streaming Mode
          FCS Option
          Fixed Channels
          Unicast Connectionless Data Reception
> ACL Data RX: Handle 11 flags 0x02 dlen 20                          #298 [hci0] 169.158292
      L2CAP: Information Response (0x0b) ident 2 len 12
        Type: Fixed channels supported (0x0003)
        Result: Success (0x0000)
        Channels: 0x0001000000000040
          Security Manager (LE)
          Unknown channels (0x1000000000000)
> HCI Event: Remote Name Req Complete (0x07) plen 255                #299 [hci0] 169.161075
        Status: Success (0x00)
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Name: AirPods Pro
@ MGMT Event: Device Connected (0x000b) plen 31                  {0x0001} [hci0] 169.161109
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Flags: 0x00000000
        Data length: 18
        Name (complete): AirPods Pro
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
> HCI Event: Number of Completed Packets (0x13) plen 5               #300 [hci0] 169.162074
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #291: len 10 (2 Kb/s)
        Latency: 40 msec (40-40 msec ~40 msec)
        #295: len 10 (6 Kb/s)
        Latency: 12 msec (12-40 msec ~26 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 10                          #301 [hci0] 169.162288
      L2CAP: Information Request (0x0a) ident 54 len 2
        Type: Fixed channels supported (0x0003)
< ACL Data TX: Handle 11 flags 0x00 dlen 20                          #302 [hci0] 169.162299
      L2CAP: Information Response (0x0b) ident 54 len 12
        Type: Fixed channels supported (0x0003)
        Result: Success (0x0000)
        Channels: 0x0000000000000006
          L2CAP Signaling (BR/EDR)
          Connectionless reception
> HCI Event: Number of Completed Packets (0x13) plen 5               #303 [hci0] 169.166081
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #297: len 16 (11 Kb/s)
        Latency: 11 msec (11-40 msec ~19 msec)
        #302: len 20 (53 Kb/s)
        Latency: 3 msec (3-40 msec ~11 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #304 [hci0] 169.173333
      L2CAP: Connection Request (0x02) ident 55 len 4
        PSM: 1 (0x0001)
        Source CID: 5380
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #305 [hci0] 169.173374
      L2CAP: Connection Response (0x03) ident 55 len 8
        Destination CID: 64
        Source CID: 5380
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #306 [hci0] 169.173380
      L2CAP: Configure Request (0x04) ident 3 len 4
        Destination CID: 5380
        Flags: 0x0000
> HCI Event: Number of Completed Packets (0x13) plen 5               #307 [hci0] 169.177077
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #305: len 16 (42 Kb/s)
        Latency: 3 msec (3-40 msec ~7 msec)
        #306: len 12 (32 Kb/s)
        Latency: 3 msec (3-40 msec ~5 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 16                          #308 [hci0] 169.182189
      L2CAP: Configure Request (0x04) ident 56 len 8
        Destination CID: 64
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 128
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #309 [hci0] 169.182192
      L2CAP: Configure Response (0x05) ident 3 len 10
        Source CID: 64
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
< ACL Data TX: Handle 11 flags 0x00 dlen 18                          #310 [hci0] 169.182241
      L2CAP: Configure Response (0x05) ident 56 len 10
        Source CID: 5380
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 128
> ACL Data RX: Handle 11 flags 0x02 dlen 40                          #311 [hci0] 169.190289
      Channel: 64 len 36 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Request (0x06) tid 1 len 31
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              PnP Information (0x1200)
        Max record count: 64
        Attribute list: [len 23]
          Sequence (6) with 21 bytes [8 extra bits] len 23
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0201
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0202
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0203
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0205
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0xa000
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0xa001
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0xafff
        Continuation state: 0
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 40        #312 [hci0] 169.190430
      Channel: 5380 len 36 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Response (0x07) tid 1 len 31
        Attribute bytes: 28
          Attribute list: [len 24] {position 0}
            Attribute: Unknown (0x0201) [len 2]
              0x1d6b
            Attribute: Unknown (0x0202) [len 2]
              0x0246
            Attribute: Unknown (0x0203) [len 2]
              0x0548
            Attribute: Unknown (0x0205) [len 2]
              0x0002
        Continuation state: 0
> HCI Event: Number of Completed Packets (0x13) plen 5               #313 [hci0] 169.194050
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #310: len 18 (13 Kb/s)
        Latency: 11 msec (3-40 msec ~8 msec)
        #312: len 40 (106 Kb/s)
        Latency: 3 msec (3-40 msec ~6 msec)
        Channel: 5380 [PSM 1 mode Basic (0x00)] {chan 0}
        Channel Latency: 3 msec (3-3 msec ~3 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 22                          #314 [hci0] 169.198291
      Channel: 64 len 18 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Request (0x06) tid 2 len 13
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              Handsfree Audio Gateway (0x111f)
        Max record count: 64
        Attribute list: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0004
        Continuation state: 0
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 33        #315 [hci0] 169.198461
      Channel: 5380 len 29 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Response (0x07) tid 2 len 24
        Attribute bytes: 21
          Attribute list: [len 17] {position 0}
            Attribute: Protocol Descriptor List (0x0004) [len 2]
              Sequence (6) with 3 bytes [8 extra bits] len 5
                UUID (3) with 2 bytes [0 extra bits] len 3
                  L2CAP (0x0100)
              Sequence (6) with 5 bytes [8 extra bits] len 7
                UUID (3) with 2 bytes [0 extra bits] len 3
                  RFCOMM (0x0003)
                Unsigned Integer (1) with 1 byte [0 extra bits] len 2
                  0x0d
        Continuation state: 0
> ACL Data RX: Handle 11 flags 0x02 dlen 22                          #316 [hci0] 169.206339
      Channel: 64 len 18 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Request (0x06) tid 3 len 13
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              Audio Source (0x110a)
        Max record count: 64
        Attribute list: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0004
        Continuation state: 0
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 37        #317 [hci0] 169.206465
      Channel: 5380 len 33 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Response (0x07) tid 3 len 28
        Attribute bytes: 25
          Attribute list: [len 21] {position 0}
            Attribute: Protocol Descriptor List (0x0004) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  L2CAP (0x0100)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0019
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  AVDTP (0x0019)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0103
        Continuation state: 0
> HCI Event: Number of Completed Packets (0x13) plen 5               #318 [hci0] 169.210081
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #315: len 33 (24 Kb/s)
        Latency: 11 msec (3-40 msec ~8 msec)
        Channel: 5380 [PSM 1 mode Basic (0x00)] {chan 0}
        Channel Latency: 11 msec (3-11 msec ~7 msec)
        #317: len 37 (98 Kb/s)
        Latency: 3 msec (3-40 msec ~6 msec)
        Channel: 5380 [PSM 1 mode Basic (0x00)] {chan 0}
        Channel Latency: 3 msec (3-11 msec ~5 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 22                          #319 [hci0] 169.214352
      Channel: 64 len 18 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Request (0x06) tid 4 len 13
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              A/V Remote Control Target (0x110c)
        Max record count: 64
        Attribute list: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0009
        Continuation state: 0
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 29        #320 [hci0] 169.214503
      Channel: 5380 len 25 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Response (0x07) tid 4 len 20
        Attribute bytes: 17
          Attribute list: [len 13] {position 0}
            Attribute: Bluetooth Profile Descriptor List (0x0009) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  A/V Remote Control (0x110e)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0105
        Continuation state: 0
> HCI Event: Link Key Request (0x17) plen 6                          #321 [hci0] 169.256172
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
< HCI Command: Link Key Request Reply (0x01|0x000b) plen 22          #322 [hci0] 169.256211
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Link key: 7a64ec98d997df6fe626ef7436a35880
> HCI Event: Command Complete (0x0e) plen 10                         #323 [hci0] 169.259073
      Link Key Request Reply (0x01|0x000b) ncmd 1
        Status: Success (0x00)
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
> HCI Event: Encryption Change (0x08) plen 4                         #324 [hci0] 169.277049
        Status: Success (0x00)
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Encryption: Enabled with E0 (0x01)
< HCI Command: Read Encryption Key Size (0x05|0x0008) plen 2         #325 [hci0] 169.277060
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
> HCI Event: Command Complete (0x0e) plen 7                          #326 [hci0] 169.278076
      Read Encryption Key Size (0x05|0x0008) ncmd 1
        Status: Success (0x00)
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Key size: 16
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #327 [hci0] 169.282444
      L2CAP: Connection Request (0x02) ident 57 len 4
        PSM: 3 (0x0003)
        Source CID: 5637
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #328 [hci0] 169.282501
      L2CAP: Connection Response (0x03) ident 57 len 8
        Destination CID: 65
        Source CID: 5637
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #329 [hci0] 169.282506
      L2CAP: Configure Request (0x04) ident 4 len 8
        Destination CID: 5637
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1021
> HCI Event: Number of Completed Packets (0x13) plen 5               #330 [hci0] 169.286166
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #320: len 29 (3 Kb/s)
        Latency: 71 msec (3-71 msec ~38 msec)
        Channel: 5380 [PSM 1 mode Basic (0x00)] {chan 0}
        Channel Latency: 71 msec (3-71 msec ~38 msec)
        #328: len 16 (42 Kb/s)
        Latency: 3 msec (3-71 msec ~21 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #331 [hci0] 169.291413
      L2CAP: Configure Request (0x04) ident 58 len 4
        Destination CID: 65
        Flags: 0x0000
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #332 [hci0] 169.291414
      L2CAP: Configure Response (0x05) ident 4 len 10
        Source CID: 65
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1021
< ACL Data TX: Handle 11 flags 0x00 dlen 18                          #333 [hci0] 169.291462
      L2CAP: Configure Response (0x05) ident 58 len 10
        Source CID: 5637
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
> HCI Event: Number of Completed Packets (0x13) plen 5               #334 [hci0] 169.295103
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #329: len 16 (10 Kb/s)
        Latency: 12 msec (3-71 msec ~16 msec)
        #333: len 18 (48 Kb/s)
        Latency: 3 msec (3-71 msec ~10 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 8                           #335 [hci0] 169.299444
      Channel: 65 len 4 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Set Async Balance Mode (SABM) (0x2f)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0x3f poll/final 1
         Length: 0
         FCS: 0x1c
< ACL Data TX: Handle 11 flags 0x00 dlen 8                           #336 [hci0] 169.299528
      Channel: 5637 len 4 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Ack (UA) (0x63)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0x73 poll/final 1
         Length: 0
         FCS: 0xd7
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #337 [hci0] 169.307333
      Channel: 65 len 14 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0x70
         MCC Message type: DLC Parameter Negotiation CMD (0x20)
           Length: 8
           dlci 26 frame_type 0 credit_flow 15 pri 0
           ack_timer 0 frame_size 1015 max_retrans 0 credits 0
< ACL Data TX: Handle 11 flags 0x00 dlen 18                          #338 [hci0] 169.307444
      Channel: 5637 len 14 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x01 cr 0 dlci 0x00
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0xaa
         MCC Message type: DLC Parameter Negotiation RSP (0x20)
           Length: 8
           dlci 26 frame_type 0 credit_flow 14 pri 0
           ack_timer 0 frame_size 667 max_retrans 0 credits 7
> HCI Event: Number of Completed Packets (0x13) plen 5               #339 [hci0] 169.311090
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #336: len 8 (5 Kb/s)
        Latency: 11 msec (3-71 msec ~10 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 11 msec (11-11 msec ~11 msec)
        #338: len 18 (48 Kb/s)
        Latency: 3 msec (3-71 msec ~7 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 3 msec (3-11 msec ~7 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 8                           #340 [hci0] 169.314345
      Channel: 65 len 4 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Set Async Balance Mode (SABM) (0x2f)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0x3f poll/final 1
         Length: 0
         FCS: 0xe7
< ACL Data TX: Handle 11 flags 0x00 dlen 8                           #341 [hci0] 169.314583
      Channel: 5637 len 4 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Ack (UA) (0x63)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0x73 poll/final 1
         Length: 0
         FCS: 0x2c
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #342 [hci0] 169.314590
      Channel: 5637 len 8 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x01 cr 0 dlci 0x00
         Control: 0xef poll/final 0
         Length: 4
         FCS: 0xaa
         MCC Message type: Modem Status Command CMD (0x38)
           Length: 2
           dlci 26 
           fc 0 rtc 1 rtr 1 ic 0 dv 1
> HCI Event: Number of Completed Packets (0x13) plen 5               #343 [hci0] 169.320074
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #341: len 8 (12 Kb/s)
        Latency: 5 msec (3-71 msec ~6 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 5 msec (3-11 msec ~6 msec)
        #342: len 12 (19 Kb/s)
        Latency: 5 msec (3-71 msec ~5 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 5 msec (3-11 msec ~6 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #344 [hci0] 169.321452
      Channel: 65 len 8 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0xef poll/final 0
         Length: 4
         FCS: 0x70
         MCC Message type: Modem Status Command CMD (0x38)
           Length: 2
           dlci 26 
           fc 0 rtc 1 rtr 1 ic 0 dv 1
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #345 [hci0] 169.321596
      Channel: 5637 len 8 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x01 cr 0 dlci 0x00
         Control: 0xef poll/final 0
         Length: 4
         FCS: 0xaa
         MCC Message type: Modem Status Command RSP (0x38)
           Length: 2
           dlci 26 
           fc 0 rtc 1 rtr 1 ic 0 dv 1
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #346 [hci0] 169.324288
      Channel: 65 len 8 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0xef poll/final 0
         Length: 4
         FCS: 0x70
         MCC Message type: Modem Status Command RSP (0x38)
           Length: 2
           dlci 26 
           fc 0 rtc 1 rtr 1 ic 0 dv 1
< ACL Data TX: Handle 11 flags 0x00 dlen 9                           #347 [hci0] 169.324377
      Channel: 5637 len 5 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xff poll/final 1
         Length: 0
         FCS: 0x22
         Credits: 33
        22                                               "               
> HCI Event: Number of Completed Packets (0x13) plen 5               #348 [hci0] 169.327047
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #345: len 12 (19 Kb/s)
        Latency: 5 msec (3-71 msec ~5 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 5 msec (3-11 msec ~5 msec)
        #347: len 9 (36 Kb/s)
        Latency: 2 msec (2-71 msec ~4 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 2 msec (2-11 msec ~4 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 9                           #349 [hci0] 169.329345
      Channel: 65 len 5 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xff poll/final 1
         Length: 0
         FCS: 0xf8
         Credits: 255
        f8                                               .               
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #350 [hci0] 169.333288
      L2CAP: Connection Request (0x02) ident 59 len 4
        PSM: 25 (0x0019)
        Source CID: 5894
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #351 [hci0] 169.333289
      L2CAP: Connection Request (0x02) ident 60 len 4
        PSM: 23 (0x0017)
        Source CID: 6151
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #352 [hci0] 169.333366
      L2CAP: Connection Response (0x03) ident 59 len 8
        Destination CID: 66
        Source CID: 5894
        Result: Connection pending (0x0001)
        Status: Authorization pending (0x0002)
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #353 [hci0] 169.333372
      L2CAP: Connection Response (0x03) ident 60 len 8
        Destination CID: 67
        Source CID: 6151
        Result: Connection pending (0x0001)
        Status: Authorization pending (0x0002)
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #354 [hci0] 169.333493
      L2CAP: Connection Response (0x03) ident 60 len 8
        Destination CID: 67
        Source CID: 6151
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #355 [hci0] 169.333499
      L2CAP: Configure Request (0x04) ident 5 len 4
        Destination CID: 6151
        Flags: 0x0000
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #356 [hci0] 169.333501
      L2CAP: Connection Response (0x03) ident 59 len 8
        Destination CID: 66
        Source CID: 5894
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #357 [hci0] 169.333503
      L2CAP: Configure Request (0x04) ident 6 len 4
        Destination CID: 5894
        Flags: 0x0000
> ACL Data RX: Handle 11 flags 0x02 dlen 20                          #358 [hci0] 169.335443
      Channel: 65 len 16 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 12
         FCS: 0xe4
        41 54 2b 42 52 53 46 3d 36 36 37 0d e4           AT+BRSF=667..   
< ACL Data TX: Handle 11 flags 0x00 dlen 23                          #359 [hci0] 169.335730
      Channel: 5637 len 19 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 15
         FCS: 0x3e
        0d 0a 2b 42 52 53 46 3a 20 31 36 33 32 0d 0a 3e  ..+BRSF: 1632..>
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #360 [hci0] 169.335737
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
> HCI Event: Number of Completed Packets (0x13) plen 5               #361 [hci0] 169.337170
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #352: len 16 (42 Kb/s)
        Latency: 3 msec (2-71 msec ~3 msec)
        #353: len 16 (42 Kb/s)
        Latency: 3 msec (2-71 msec ~3 msec)
> HCI Event: Number of Completed Packets (0x13) plen 5               #362 [hci0] 169.340044
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #354: len 16 (21 Kb/s)
        Latency: 6 msec (2-71 msec ~5 msec)
        #355: len 12 (16 Kb/s)
        Latency: 6 msec (2-71 msec ~5 msec)
> HCI Event: Number of Completed Packets (0x13) plen 5               #363 [hci0] 169.342068
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #356: len 16 (16 Kb/s)
        Latency: 8 msec (2-71 msec ~7 msec)
        #357: len 12 (12 Kb/s)
        Latency: 8 msec (2-71 msec ~7 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 16                          #364 [hci0] 169.343289
      L2CAP: Configure Request (0x04) ident 61 len 8
        Destination CID: 67
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #365 [hci0] 169.343291
      L2CAP: Configure Response (0x05) ident 5 len 10
        Source CID: 67
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
< ACL Data TX: Handle 11 flags 0x00 dlen 18                          #366 [hci0] 169.343344
      L2CAP: Configure Response (0x05) ident 61 len 10
        Source CID: 6151
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 18        #367 [hci0] 169.343572
      Channel: 6151 len 14 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Command: type 0x00 label 0 PID 0x110e
        AV/C: Status: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: GetCapabilities pt Single len 0x0001
            CapabilityID: 0x03 (EventsID)
> HCI Event: Number of Completed Packets (0x13) plen 5               #368 [hci0] 169.345045
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #359: len 23 (20 Kb/s)
        Latency: 9 msec (2-71 msec ~8 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 9 msec (2-11 msec ~6 msec)
        #360: len 14 (12 Kb/s)
        Latency: 9 msec (2-71 msec ~8 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 9 msec (2-11 msec ~8 msec)
> HCI Event: Number of Completed Packets (0x13) plen 5               #369 [hci0] 169.347074
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #366: len 18 (48 Kb/s)
        Latency: 3 msec (2-71 msec ~6 msec)
        #367: len 18 (48 Kb/s)
        Latency: 3 msec (2-71 msec ~4 msec)
        Channel: 6151 [PSM 23 mode Basic (0x00)] {chan 5}
        Channel Latency: 3 msec (3-3 msec ~3 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 20                          #370 [hci0] 169.349303
      L2CAP: Configure Request (0x04) ident 62 len 12
        Destination CID: 66
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
        Option: Flush Timeout (0x02) [mandatory]
          Flush timeout: 30
< ACL Data TX: Handle 11 flags 0x00 dlen 18                          #371 [hci0] 169.349360
      L2CAP: Configure Response (0x05) ident 62 len 10
        Source CID: 5894
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #372 [hci0] 169.351353
      L2CAP: Configure Response (0x05) ident 6 len 10
        Source CID: 66
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
> ACL Data RX: Handle 11 flags 0x02 dlen 27                          #373 [hci0] 169.351354
      Channel: 65 len 23 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 19
         FCS: 0xe4
        41 54 2b 42 41 43 3d 31 2c 32 2c 31 32 38 2c 32  AT+BAC=1,2,128,2
        35 36 0d e4                                      56..            
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #374 [hci0] 169.351504
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
> HCI Event: Number of Completed Packets (0x13) plen 5               #375 [hci0] 169.371069
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #371: len 18 (6 Kb/s)
        Latency: 21 msec (2-71 msec ~13 msec)
        #374: len 14 (5 Kb/s)
        Latency: 19 msec (2-71 msec ~16 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 19 msec (2-19 msec ~13 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #376 [hci0] 169.372355
      Channel: 67 len 14 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Command: type 0x00 label 4 PID 0x110e
        AV/C: Status: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: GetCapabilities pt Single len 0x0001
            CapabilityID: 0x03 (EventsID)
> ACL Data RX: Handle 11 flags 0x02 dlen 22                          #377 [hci0] 169.372356
      Channel: 67 len 18 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Response: type 0x00 label 0 PID 0x110e
        AV/C: Stable: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: GetCapabilities pt Single len 0x0005
            CapabilityID: 0x03 (EventsID)
            CapabilityCount: 0x03
            EventsID: 0x01 (EVENT_PLAYBACK_STATUS_CHANGED)
            EventsID: 0x02 (EVENT_TRACK_CHANGED)
            EventsID: 0x0d (EVENT_VOLUME_CHANGED)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 27        #378 [hci0] 169.372483
      Channel: 6151 len 23 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Response: type 0x00 label 4 PID 0x110e
        AV/C: Stable: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: GetCapabilities pt Single len 0x000a
            CapabilityID: 0x03 (EventsID)
            CapabilityCount: 0x08
            EventsID: 0x01 (EVENT_PLAYBACK_STATUS_CHANGED)
            EventsID: 0x02 (EVENT_TRACK_CHANGED)
            EventsID: 0x03 (EVENT_TRACK_REACHED_END)
            EventsID: 0x04 (EVENT_TRACK_REACHED_START)
            EventsID: 0x08 (EVENT_PLAYER_APPLICATION_SETTING_CHANGED)
            EventsID: 0x0a (EVENT_AVAILABLE_PLAYERS_CHANGED)
            EventsID: 0x0b (EVENT_ADDRESSED_PLAYER_CHANGED)
            EventsID: 0x0d (EVENT_VOLUME_CHANGED)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #379 [hci0] 169.372491
      Channel: 6151 len 18 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Command: type 0x00 label 1 PID 0x110e
        AV/C: Notify: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: RegisterNotification pt Single len 0x0005
            EventID: 0x0d (EVENT_VOLUME_CHANGED)
            Interval: 0x00000000 (0 seconds)
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #380 [hci0] 169.374289
      L2CAP: Disconnection Request (0x06) ident 63 len 4
        Destination CID: 64
        Source CID: 5380
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #381 [hci0] 169.374327
      L2CAP: Disconnection Response (0x07) ident 63 len 4
        Destination CID: 64
        Source CID: 5380
> HCI Event: Number of Completed Packets (0x13) plen 5               #382 [hci0] 169.377039
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #378: len 27 (54 Kb/s)
        Latency: 4 msec (2-71 msec ~10 msec)
        Channel: 6151 [PSM 23 mode Basic (0x00)] {chan 5}
        Channel Latency: 4 msec (3-4 msec ~4 msec)
        #379: len 22 (44 Kb/s)
        Latency: 4 msec (2-71 msec ~7 msec)
        Channel: 6151 [PSM 23 mode Basic (0x00)] {chan 5}
        Channel Latency: 4 msec (3-4 msec ~4 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #383 [hci0] 169.378332
      Channel: 65 len 14 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0xe4
        41 54 2b 43 49 4e 44 3d 3f 0d e4                 AT+CIND=?..     
> ACL Data RX: Handle 11 flags 0x02 dlen 6                           #384 [hci0] 169.378333
      Channel: 66 len 2 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Discover (0x01) Command (0x00) type 0x00 label 6 nosp 0
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 44        #385 [hci0] 169.378476
      Channel: 5894 len 40 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Discover (0x01) Response Accept (0x02) type 0x00 label 6 nosp 0
        ACP SEID: 1
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 2
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
        ACP SEID: 3
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 4
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
        ACP SEID: 5
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 6
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
        ACP SEID: 7
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 8
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
        ACP SEID: 9
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 10
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 11
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 12
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 13
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 14
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 15
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 16
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
        ACP SEID: 17
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 18
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
        ACP SEID: 19
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
< ACL Data TX: Handle 11 flags 0x00 dlen 140                         #386 [hci0] 169.378490
      Channel: 5637 len 136 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 131
         FCS: 0x3e
        0d 0a 2b 43 49 4e 44 3a 28 22 73 65 72 76 69 63  ..+CIND:("servic
        65 22 2c 28 30 2d 31 29 29 2c 28 22 63 61 6c 6c  e",(0-1)),("call
        22 2c 28 30 2d 31 29 29 2c 28 22 63 61 6c 6c 73  ",(0-1)),("calls
        65 74 75 70 22 2c 28 30 2d 33 29 29 2c 28 22 63  etup",(0-3)),("c
        61 6c 6c 68 65 6c 64 22 2c 28 30 2d 32 29 29 2c  allheld",(0-2)),
        28 22 73 69 67 6e 61 6c 22 2c 28 30 2d 35 29 29  ("signal",(0-5))
        2c 28 22 72 6f 61 6d 22 2c 28 30 2d 31 29 29 2c  ,("roam",(0-1)),
        28 22 62 61 74 74 63 68 67 22 2c 28 30 2d 35 29  ("battchg",(0-5)
        29 0d 0a 3e                                      )..>            
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #387 [hci0] 169.378492
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
> HCI Event: Number of Completed Packets (0x13) plen 5               #388 [hci0] 169.381097
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #381: len 12 (16 Kb/s)
        Latency: 6 msec (2-71 msec ~7 msec)
        #385: len 44 (176 Kb/s)
        Latency: 2 msec (2-71 msec ~4 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 2 msec (2-2 msec ~2 msec)
> HCI Event: Number of Completed Packets (0x13) plen 5               #389 [hci0] 169.385053
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #386: len 140 (186 Kb/s)
        Latency: 6 msec (2-71 msec ~5 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 6 msec (2-19 msec ~10 msec)
        #387: len 14 (18 Kb/s)
        Latency: 6 msec (2-71 msec ~6 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 6 msec (2-19 msec ~8 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 22                          #390 [hci0] 169.385293
      Channel: 67 len 18 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Command: type 0x00 label 5 PID 0x110e
        AV/C: Notify: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: RegisterNotification pt Single len 0x0005
            EventID: 0x01 (EVENT_PLAYBACK_STATUS_CHANGED)
            Interval: 0x00000000 (0 seconds)
> ACL Data RX: Handle 11 flags 0x02 dlen 19                          #391 [hci0] 169.385294
      Channel: 67 len 15 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Response: type 0x00 label 1 PID 0x110e
        AV/C: Interim: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: RegisterNotification pt Single len 0x0002
            EventID: 0x0d (EVENT_VOLUME_CHANGED)
            Volume: 80.31% (102/127)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 19        #392 [hci0] 169.385366
      Channel: 6151 len 15 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Response: type 0x00 label 5 PID 0x110e
        AV/C: Interim: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: RegisterNotification pt Single len 0x0002
            EventID: 0x01 (EVENT_PLAYBACK_STATUS_CHANGED)
            PlayStatus: 0x00 (STOPPED)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #393 [hci0] 169.388290
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 7 nosp 0
        ACP SEID: 1
> ACL Data RX: Handle 11 flags 0x02 dlen 17                          #394 [hci0] 169.388292
      Channel: 65 len 13 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 9
         FCS: 0xe4
        41 54 2b 43 49 4e 44 3f 0d e4                    AT+CIND?..      
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #395 [hci0] 169.388341
      Channel: 5894 len 18 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 7 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Sony Corporation (0x0000012d)
            Vendor Specific Codec ID: LDAC (0x00aa)
              Unknown: 0x073c
        Service Category: Delay Reporting (0x08)
< ACL Data TX: Handle 11 flags 0x00 dlen 32                          #396 [hci0] 169.388349
      Channel: 5637 len 28 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 24
         FCS: 0x3e
        0d 0a 2b 43 49 4e 44 3a 20 30 2c 30 2c 30 2c 30  ..+CIND: 0,0,0,0
        2c 30 2c 30 2c 30 0d 0a 3e                       ,0,0,0..>       
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #397 [hci0] 169.388351
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
> HCI Event: Number of Completed Packets (0x13) plen 5               #398 [hci0] 169.391073
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #392: len 19 (30 Kb/s)
        Latency: 5 msec (2-71 msec ~5 msec)
        Channel: 6151 [PSM 23 mode Basic (0x00)] {chan 5}
        Channel Latency: 5 msec (3-5 msec ~4 msec)
        #395: len 22 (88 Kb/s)
        Latency: 2 msec (2-71 msec ~4 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 2 msec (2-2 msec ~2 msec)
> HCI Event: Number of Completed Packets (0x13) plen 5               #399 [hci0] 169.394046
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #396: len 32 (51 Kb/s)
        Latency: 5 msec (2-71 msec ~5 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 5 msec (2-19 msec ~7 msec)
        #397: len 14 (22 Kb/s)
        Latency: 5 msec (2-71 msec ~5 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 5 msec (2-19 msec ~6 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #400 [hci0] 169.398446
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 8 nosp 0
        ACP SEID: 3
> ACL Data RX: Handle 11 flags 0x02 dlen 24                          #401 [hci0] 169.398448
      Channel: 65 len 20 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 16
         FCS: 0xe4
        41 54 2b 43 4d 45 52 3d 33 2c 30 2c 30 2c 31 0d  AT+CMER=3,0,0,1.
        e4                                               .               
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 25        #402 [hci0] 169.398553
      Channel: 5894 len 21 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 8 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Qualcomm Technologies, Inc. (0x000000d7)
            Vendor Specific Codec ID: aptX HD (0x0024)
              Frequency: 0xf0
                16000
                32000
                44100
                48000
              Channel Mode: 0x02
                Stereo
        Service Category: Delay Reporting (0x08)
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #403 [hci0] 169.398617
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
< ACL Data TX: Handle 11 flags 0x00 dlen 19                          #404 [hci0] 169.398622
      Channel: 5637 len 15 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 11
         FCS: 0x3e
        0d 0a 2b 42 43 53 3a 20 32 0d 0a 3e              ..+BCS: 2..>    
> HCI Event: Number of Completed Packets (0x13) plen 5               #405 [hci0] 169.404072
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #402: len 25 (40 Kb/s)
        Latency: 5 msec (2-71 msec ~5 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 5 msec (2-5 msec ~4 msec)
        #403: len 14 (22 Kb/s)
        Latency: 5 msec (2-71 msec ~5 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 5 msec (2-19 msec ~5 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #406 [hci0] 169.437447
      Channel: 65 len 14 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0xe4
        41 54 2b 56 47 53 3d 31 31 0d e4                 AT+VGS=11..     
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #407 [hci0] 169.437449
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 9 nosp 0
        ACP SEID: 5
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 21        #408 [hci0] 169.437569
      Channel: 5894 len 17 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 9 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: APT Ltd. (0x0000004f)
            Vendor Specific Codec ID: aptX (0x0001)
              Frequency: 0xf0
                16000
                32000
                44100
                48000
              Channel Mode: 0x02
                Stereo
        Service Category: Delay Reporting (0x08)
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #409 [hci0] 169.437644
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
> HCI Event: Number of Completed Packets (0x13) plen 5               #410 [hci0] 169.441042
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #404: len 19 (3 Kb/s)
        Latency: 42 msec (2-71 msec ~23 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 42 msec (2-42 msec ~24 msec)
        #408: len 21 (56 Kb/s)
        Latency: 3 msec (2-71 msec ~13 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 3 msec (2-5 msec ~3 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 17                          #411 [hci0] 169.447443
      Channel: 65 len 13 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 9
         FCS: 0xe4
        41 54 2b 56 47 4d 3d 38 0d e4                    AT+VGM=8..      
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #412 [hci0] 169.447445
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 10 nosp 0
        ACP SEID: 7
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 18        #413 [hci0] 169.447565
      Channel: 5894 len 14 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 10 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: SBC (0x00)
            Frequency: 0xf0
              16000
              32000
              44100
              48000
            Channel Mode: 0x0f
              Mono
              Dual Channel
              Stereo
              Joint Stereo
            Block Length: 0xf0
              4
              8
              12
              16
            Subbands: 0x0c
              4
              8
            Allocation Method: 0x03
              SNR
              Loudness
            Minimum Bitpool: 2
            Maximum Bitpool: 64
        Service Category: Delay Reporting (0x08)
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #414 [hci0] 169.447646
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
> HCI Event: Number of Completed Packets (0x13) plen 5               #415 [hci0] 169.451161
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #409: len 14 (8 Kb/s)
        Latency: 13 msec (2-71 msec ~13 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 13 msec (2-42 msec ~18 msec)
        #413: len 18 (48 Kb/s)
        Latency: 3 msec (2-71 msec ~8 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 3 msec (2-5 msec ~3 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 29                          #416 [hci0] 169.457321
      Channel: 65 len 25 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 21
         FCS: 0xe4
        41 54 2b 42 49 41 3d 30 2c 31 2c 31 2c 31 2c 30  AT+BIA=0,1,1,1,0
        2c 30 2c 30 0d e4                                ,0,0..          
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #417 [hci0] 169.457322
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 11 nosp 0
        ACP SEID: 9
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #418 [hci0] 169.457446
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 18        #419 [hci0] 169.457454
      Channel: 5894 len 14 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 11 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: SBC (0x00)
            Frequency: 0xf0
              16000
              32000
              44100
              48000
            Channel Mode: 0x0f
              Mono
              Dual Channel
              Stereo
              Joint Stereo
            Block Length: 0xf0
              4
              8
              12
              16
            Subbands: 0x0c
              4
              8
            Allocation Method: 0x03
              SNR
              Loudness
            Minimum Bitpool: 2
            Maximum Bitpool: 64
        Service Category: Delay Reporting (0x08)
> HCI Event: Number of Completed Packets (0x13) plen 5               #420 [hci0] 169.461105
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #414: len 14 (8 Kb/s)
        Latency: 13 msec (2-71 msec ~11 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 13 msec (2-42 msec ~16 msec)
        #418: len 14 (37 Kb/s)
        Latency: 3 msec (2-71 msec ~7 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 3 msec (2-42 msec ~9 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #421 [hci0] 169.478288
      Channel: 65 len 14 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0xe4
        41 54 2b 4e 52 45 43 3d 30 0d e4                 AT+NREC=0..     
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #422 [hci0] 169.478289
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 12 nosp 0
        ACP SEID: 10
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #423 [hci0] 169.478468
      Channel: 5894 len 18 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 12 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Qualcomm Technologies, Inc. (0x000000d7)
            Vendor Specific Codec ID: Unknown (0x0002)
        f2 00                                            ..              
        Service Category: Delay Reporting (0x08)
< ACL Data TX: Handle 11 flags 0x00 dlen 17                          #424 [hci0] 169.478534
      Channel: 5637 len 13 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 9
         FCS: 0x3e
        0d 0a 45 52 52 4f 52 0d 0a 3e                    ..ERROR..>      
> HCI Event: Number of Completed Packets (0x13) plen 5               #425 [hci0] 169.514074
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #419: len 18 (2 Kb/s)
        Latency: 56 msec (2-71 msec ~31 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 56 msec (2-56 msec ~30 msec)
        #423: len 22 (5 Kb/s)
        Latency: 35 msec (2-71 msec ~33 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 35 msec (2-56 msec ~32 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #426 [hci0] 169.518289
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 13 nosp 0
        ACP SEID: 11
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #427 [hci0] 169.518377
      Channel: 5894 len 18 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 13 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Cambridge Silicon Radio (0x0000000a)
            Vendor Specific Codec ID: aptX Low Latency (0x0002)
              Frequency: 0xf0
                16000
                32000
                44100
                48000
              Channel Mode: 0x02
                Stereo
            Bidirectional link: No
        Service Category: Delay Reporting (0x08)
> HCI Event: Number of Completed Packets (0x13) plen 5               #428 [hci0] 169.521069
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #424: len 17 (3 Kb/s)
        Latency: 42 msec (2-71 msec ~38 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 42 msec (2-42 msec ~26 msec)
        #427: len 22 (88 Kb/s)
        Latency: 2 msec (2-71 msec ~20 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 2 msec (2-56 msec ~17 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #429 [hci0] 169.527315
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 14 nosp 0
        ACP SEID: 12
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #430 [hci0] 169.527421
      Channel: 5894 len 18 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 14 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Qualcomm Technologies, Inc. (0x000000d7)
            Vendor Specific Codec ID: Unknown (0x0002)
        f2 01                                            ..              
        Service Category: Delay Reporting (0x08)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #431 [hci0] 169.536452
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 15 nosp 0
        ACP SEID: 13
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #432 [hci0] 169.536558
      Channel: 5894 len 18 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 15 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Cambridge Silicon Radio (0x0000000a)
            Vendor Specific Codec ID: aptX Low Latency (0x0002)
              Frequency: 0xf0
                16000
                32000
                44100
                48000
              Channel Mode: 0x02
                Stereo
            Bidirectional link: Yes
        Service Category: Delay Reporting (0x08)
> HCI Event: Number of Completed Packets (0x13) plen 5               #433 [hci0] 169.540142
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #430: len 22 (14 Kb/s)
        Latency: 12 msec (2-71 msec ~16 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 12 msec (2-56 msec ~15 msec)
        #432: len 22 (58 Kb/s)
        Latency: 3 msec (2-71 msec ~10 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 3 msec (2-56 msec ~9 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #434 [hci0] 169.545334
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 0 nosp 0
        ACP SEID: 14
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #435 [hci0] 169.545432
      Channel: 5894 len 18 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 0 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Cambridge Silicon Radio (0x0000000a)
            Vendor Specific Codec ID: FastStream (0x0001)
              Direction: 0x01
                Sink
              Sink Frequency: 0x03
                48000
                44100
              Source Frequency: 0x20
                16000
        Service Category: Delay Reporting (0x08)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #436 [hci0] 169.573362
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 1 nosp 0
        ACP SEID: 15
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #437 [hci0] 169.573485
      Channel: 5894 len 18 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 1 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Cambridge Silicon Radio (0x0000000a)
            Vendor Specific Codec ID: FastStream (0x0001)
              Direction: 0x03
                Sink
                Source
              Sink Frequency: 0x03
                48000
                44100
              Source Frequency: 0x20
                16000
        Service Category: Delay Reporting (0x08)
> HCI Event: Number of Completed Packets (0x13) plen 5               #438 [hci0] 169.576039
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #435: len 22 (5 Kb/s)
        Latency: 30 msec (2-71 msec ~20 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 30 msec (2-56 msec ~20 msec)
        #437: len 22 (88 Kb/s)
        Latency: 2 msec (2-71 msec ~11 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 2 msec (2-56 msec ~11 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #439 [hci0] 169.581345
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 2 nosp 0
        ACP SEID: 17
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 38        #440 [hci0] 169.581480
      Channel: 5894 len 34 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 2 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: The Linux Foundation (0x000005f1)
            Vendor Specific Codec ID: Unknown (0x1005)
        40 00 ff ff ff 0f 1f 00 00 00 00 00 00 00 00 00  @...............
        00 00                                            ..              
        Service Category: Delay Reporting (0x08)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #441 [hci0] 169.590289
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 3 nosp 0
        ACP SEID: 19
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 38        #442 [hci0] 169.590439
      Channel: 5894 len 34 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 3 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: The Linux Foundation (0x000005f1)
            Vendor Specific Codec ID: Unknown (0x1005)
        40 00 ff ff ff 0f 1f 00 00 40 00 ff ff ff 0f 1f  @........@......
        00 00                                            ..              
        Service Category: Delay Reporting (0x08)
> HCI Event: Number of Completed Packets (0x13) plen 5               #443 [hci0] 169.594162
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #440: len 38 (25 Kb/s)
        Latency: 12 msec (2-71 msec ~12 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 12 msec (2-56 msec ~11 msec)
        #442: len 38 (101 Kb/s)
        Latency: 3 msec (2-71 msec ~7 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 3 msec (2-56 msec ~7 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 20                          #444 [hci0] 169.598453
      Channel: 66 len 16 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Set Configuration (0x03) Command (0x00) type 0x00 label 4 nosp 0
        ACP SEID: 9
        INT SEID: 1
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: SBC (0x00)
            Frequency: 44100 (0x20)
            Channel Mode: Joint Stereo (0x01)
            Block Length: 16 (0x10)
            Subbands: 8 (0x04)
            Allocation Method: Loudness (0x01)
            Minimum Bitpool: 2
            Maximum Bitpool: 53
        Service Category: Delay Reporting (0x08)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 6         #445 [hci0] 169.599498
      Channel: 5894 len 2 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Set Configuration (0x03) Response Accept (0x02) type 0x00 label 4 nosp 0
> ACL Data RX: Handle 11 flags 0x02 dlen 9                           #446 [hci0] 169.607295
      Channel: 66 len 5 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Delay Report (0x0d) Command (0x00) type 0x00 label 5 nosp 0
        ACP SEID: 9
        Delay: 150.0ms
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 6         #447 [hci0] 169.607442
      Channel: 5894 len 2 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Delay Report (0x0d) Response Accept (0x02) type 0x00 label 5 nosp 0
> HCI Event: Number of Completed Packets (0x13) plen 5               #448 [hci0] 169.611040
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #445: len 6 (4 Kb/s)
        Latency: 11 msec (2-71 msec ~9 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 11 msec (2-56 msec ~9 msec)
        #447: len 6 (16 Kb/s)
        Latency: 3 msec (2-71 msec ~6 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 3 msec (2-56 msec ~6 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #449 [hci0] 169.614289
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Open (0x06) Command (0x00) type 0x00 label 6 nosp 0
        ACP SEID: 9
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 6         #450 [hci0] 169.614438
      Channel: 5894 len 2 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Open (0x06) Response Accept (0x02) type 0x00 label 6 nosp 0
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #451 [hci0] 169.624302
      L2CAP: Connection Request (0x02) ident 64 len 4
        PSM: 25 (0x0019)
        Source CID: 6404
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #452 [hci0] 169.624357
      L2CAP: Connection Response (0x03) ident 64 len 8
        Destination CID: 64
        Source CID: 6404
        Result: Connection pending (0x0001)
        Status: Authorization pending (0x0002)
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #453 [hci0] 169.624433
      L2CAP: Connection Response (0x03) ident 64 len 8
        Destination CID: 64
        Source CID: 6404
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #454 [hci0] 169.624437
      L2CAP: Configure Request (0x04) ident 7 len 4
        Destination CID: 6404
        Flags: 0x0000
> HCI Event: Number of Completed Packets (0x13) plen 5               #455 [hci0] 169.629068
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #450: len 6 (3 Kb/s)
        Latency: 14 msec (2-71 msec ~10 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 14 msec (2-56 msec ~10 msec)
        #452: len 16 (32 Kb/s)
        Latency: 4 msec (2-71 msec ~7 msec)
> HCI Event: Number of Completed Packets (0x13) plen 5               #456 [hci0] 169.652155
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #453: len 16 (4 Kb/s)
        Latency: 27 msec (2-71 msec ~17 msec)
        #454: len 12 (3 Kb/s)
        Latency: 27 msec (2-71 msec ~22 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 20                          #457 [hci0] 169.652289
      L2CAP: Configure Request (0x04) ident 65 len 12
        Destination CID: 64
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
        Option: Flush Timeout (0x02) [mandatory]
          Flush timeout: 30
< ACL Data TX: Handle 11 flags 0x00 dlen 18                          #458 [hci0] 169.652296
      L2CAP: Configure Response (0x05) ident 65 len 10
        Source CID: 6404
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #459 [hci0] 169.656290
      L2CAP: Configure Response (0x05) ident 7 len 10
        Source CID: 64
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
> HCI Event: Number of Completed Packets (0x13) plen 5               #460 [hci0] 169.902168
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 1
        #458: len 18 (0 Kb/s)
        Latency: 249 msec (2-249 msec ~136 msec)
                  Audio (Speaker, Microphone, Headset)
> HCI Event: Inquiry Complete (0x01) plen 1                          #263 [hci0] 157.849435
        Status: Success (0x00)
@ MGMT Event: Discovering (0x0013) plen 2                        {0x0001} [hci0] 157.849474
        Address type: 0x01
          BR/EDR
        Discovery: Disabled (0x00)
bluetoothd[1272]: @ MGMT Command: Start Disco.. (0x0023) plen 1  {0x0001} [hci0] 163.604435
        Address type: 0x01
          BR/EDR
< HCI Command: Inquiry (0x01|0x0001) plen 5                          #264 [hci0] 163.604518
        Access code: 0x9e8b33 (General Inquiry)
        Length: 10.24s (0x08)
        Num responses: 0
> HCI Event: Command Status (0x0f) plen 4                            #265 [hci0] 163.605320
      Inquiry (0x01|0x0001) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 4                   {0x0001} [hci0] 163.605395
      Start Discovery (0x0023) plen 1
        Status: Success (0x00)
        Address type: 0x01
          BR/EDR
@ MGMT Event: Discovering (0x0013) plen 2                        {0x0001} [hci0] 163.605397
        Address type: 0x01
          BR/EDR
        Discovery: Enabled (0x01)
bluetoothd[1272]: @ MGMT Command: Set Connect.. (0x0007) plen 1  {0x0001} [hci0] 166.956354
        Connectable: Disabled (0x00)
< HCI Command: Inquiry Cancel (0x01|0x0002) plen 0                   #266 [hci0] 166.956354
> HCI Event: Command Complete (0x0e) plen 4                          #267 [hci0] 166.958141
      Inquiry Cancel (0x01|0x0002) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Discovering (0x0013) plen 2                        {0x0001} [hci0] 166.958175
        Address type: 0x01
          BR/EDR
        Discovery: Disabled (0x00)
< HCI Command: Create Connection (0x01|0x0005) plen 13               #268 [hci0] 166.958232
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Packet type: 0xcc18
          DM1 may be used
          DH1 may be used
          DM3 may be used
          DH3 may be used
          DM5 may be used
          DH5 may be used
        Page scan repetition mode: R2 (0x02)
        Page scan mode: Mandatory (0x00)
        Clock offset: 0x0000
        Role switch: Allow peripheral (0x01)
> HCI Event: Command Status (0x0f) plen 4                            #269 [hci0] 166.962107
      Create Connection (0x01|0x0005) ncmd 1
        Status: Success (0x00)
> HCI Event: Connect Complete (0x03) plen 11                         #270 [hci0] 168.906182
        Status: ACL Connection Already Exists (0x0b)
        Handle: 11
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Link type: ACL (0x01)
        Encryption: Disabled (0x00)
@ MGMT Event: Connect Failed (0x000d) plen 8                     {0x0001} [hci0] 168.906220
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Status: Already Connected (0x09)
bluetoothd[1272]: = src/profile.c:record_cb() Unable to get Hands-Free Voice..   168.906386
< HCI Command: Write Scan Enable (0x03|0x001a) plen 1                #271 [hci0] 168.919521
        Scan enable: Page Scan (0x02)
> HCI Event: Command Complete (0x0e) plen 4                          #272 [hci0] 168.920180
      Write Scan Enable (0x03|0x001a) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 7                   {0x0001} [hci0] 168.920260
      Set Connectable (0x0007) plen 4
        Status: Success (0x00)
        Current settings: 0x000000d1
          Powered
          Bondable
          Secure Simple Pairing
          BR/EDR
< HCI Command: Create Connection (0x01|0x0005) plen 13               #273 [hci0] 168.920267
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Packet type: 0xcc18
          DM1 may be used
          DH1 may be used
          DM3 may be used
          DH3 may be used
          DM5 may be used
          DH5 may be used
        Page scan repetition mode: R2 (0x02)
        Page scan mode: Mandatory (0x00)
        Clock offset: 0x0000
        Role switch: Allow peripheral (0x01)
> HCI Event: Command Status (0x0f) plen 4                            #274 [hci0] 168.921104
      Create Connection (0x01|0x0005) ncmd 1
        Status: ACL Connection Already Exists (0x0b)
bluetoothd[1272]: = profiles/audio/avdtp.c:avdtp_connect_cb() connect to 50:..   168.922911
bluetoothd[1272]: @ MGMT Command: Stop Discov.. (0x0024) plen 1  {0x0001} [hci0] 168.922911
        Address type: 0x01
          BR/EDR
> HCI Event: Connect Request (0x04) plen 10                          #275 [hci0] 168.927059
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
        Link type: ACL (0x01)
< HCI Command: Accept Connection Request (0x01|0x0009) plen 7        #276 [hci0] 168.941346
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Role: Central (0x00)
@ MGMT Event: Command Complete (0x0001) plen 4                   {0x0001} [hci0] 168.941392
      Stop Discovery (0x0024) plen 1
        Status: Rejected (0x0b)
        Address type: 0x01
          BR/EDR
bluetoothd[1272]: @ MGMT Command: Set Connect.. (0x0007) plen 1  {0x0001} [hci0] 168.942055
        Connectable: Enabled (0x01)
> HCI Event: Command Status (0x0f) plen 4                            #277 [hci0] 168.942056
      Accept Connection Request (0x01|0x0009) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 7                   {0x0001} [hci0] 168.942065
      Set Connectable (0x0007) plen 4
        Status: Success (0x00)
        Current settings: 0x000000d3
          Powered
          Connectable
          Bondable
          Secure Simple Pairing
          BR/EDR
bluetoothd[1272]: @ MGMT Command: Set Discove.. (0x0006) plen 3  {0x0001} [hci0] 168.942076
        Discoverable: General (0x01)
        Timeout: 0
< HCI Command: Write Current IAC LAP (0x03|0x003a) plen 4            #278 [hci0] 168.942081
        Number of IAC: 1
        Access code: 0x9e8b33 (General Inquiry)
> HCI Event: Command Complete (0x0e) plen 4                          #279 [hci0] 168.944057
      Write Current IAC LAP (0x03|0x003a) ncmd 1
        Status: Success (0x00)
< HCI Command: Write Scan Enable (0x03|0x001a) plen 1                #280 [hci0] 168.944074
        Scan enable: Inquiry Scan + Page Scan (0x03)
> HCI Event: Command Complete (0x0e) plen 4                          #281 [hci0] 168.945052
      Write Scan Enable (0x03|0x001a) ncmd 1
        Status: Success (0x00)
@ MGMT Event: Command Complete (0x0001) plen 7                   {0x0001} [hci0] 168.945091
      Set Discoverable (0x0006) plen 4
        Status: Success (0x00)
        Current settings: 0x000000db
          Powered
          Connectable
          Discoverable
          Bondable
          Secure Simple Pairing
          BR/EDR
> HCI Event: Role Change (0x12) plen 8                               #282 [hci0] 169.103081
        Status: Success (0x00)
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Role: Central (0x00)
> HCI Event: Connect Complete (0x03) plen 11                         #283 [hci0] 169.117079
        Status: Success (0x00)
        Handle: 11
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Link type: ACL (0x01)
        Encryption: Disabled (0x00)
< HCI Command: Read Remote Supported Features (0x01|0x001b) plen 2   #284 [hci0] 169.117131
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
btmon[6677]: @ RAW Open: btmon (privileged) version 2.22                {0x0002} 169.117221
btmon[6677]: @ RAW Close: btmon                                         {0x0002} 169.117224
> HCI Event: Command Status (0x0f) plen 4                            #285 [hci0] 169.118068
      Read Remote Supported Features (0x01|0x001b) ncmd 1
        Status: Success (0x00)
> HCI Event: Read Remote Supported Features (0x0b) plen 11           #286 [hci0] 169.119053
        Status: Success (0x00)
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Features: 0xbf 0xfe 0x2f 0xfe 0xdb 0xff 0x7b 0x87
          3 slot packets
          5 slot packets
          Encryption
          Slot offset
          Timing accuracy
          Role switch
          Sniff mode
          Power control requests
          Channel quality driven data rate (CQDDR)
          SCO link
          HV2 packets
          HV3 packets
          u-law log synchronous data
          A-law log synchronous data
          CVSD synchronous data
          Paging parameter negotiation
          Power control
          Transparent synchronous data
          Flow control lag (middle bit)
          Enhanced Data Rate ACL 2 Mbps mode
          Enhanced Data Rate ACL 3 Mbps mode
          Enhanced inquiry scan
          Interlaced inquiry scan
          Interlaced page scan
          RSSI with inquiry results
          Extended SCO link (EV3 packets)
          EV4 packets
          EV5 packets
          AFH capable peripheral
          AFH classification peripheral
          LE Supported (Controller)
          3-slot Enhanced Data Rate ACL packets
          5-slot Enhanced Data Rate ACL packets
          Sniff subrating
          Pause encryption
          AFH capable central
          AFH classification central
          Enhanced Data Rate eSCO 2 Mbps mode
          Enhanced Data Rate eSCO 3 Mbps mode
          3-slot Enhanced Data Rate eSCO packets
          Extended Inquiry Response
          Simultaneous LE and BR/EDR (Controller)
          Secure Simple Pairing
          Encapsulated PDU
          Erroneous Data Reporting
          Non-flushable Packet Boundary Flag
          Link Supervision Timeout Changed Event
          Inquiry TX Power Level
          Enhanced Power Control
          Extended features
< HCI Command: Read Remote Extended Features (0x01|0x001c) plen 3    #287 [hci0] 169.119064
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page: 1
> HCI Event: Command Status (0x0f) plen 4                            #288 [hci0] 169.120060
      Read Remote Extended Features (0x01|0x001c) ncmd 1
        Status: Success (0x00)
> HCI Event: Read Remote Extended Features (0x23) plen 13            #289 [hci0] 169.121056
        Status: Success (0x00)
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page: 1/1
        Features: 0x07 0x00 0x00 0x00 0x00 0x00 0x00 0x00
          Secure Simple Pairing (Host Support)
          LE Supported (Host)
          Simultaneous LE and BR/EDR (Host)
< HCI Command: Remote Name Request (0x01|0x0019) plen 10             #290 [hci0] 169.121090
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Page scan repetition mode: R2 (0x02)
        Page scan mode: Mandatory (0x00)
        Clock offset: 0x0000
< ACL Data TX: Handle 11 flags 0x00 dlen 10                          #291 [hci0] 169.121099
      L2CAP: Information Request (0x0a) ident 1 len 2
        Type: Extended features supported (0x0002)
> HCI Event: Command Status (0x0f) plen 4                            #292 [hci0] 169.122050
      Remote Name Request (0x01|0x0019) ncmd 1
        Status: Success (0x00)
> HCI Event: Max Slots Change (0x1b) plen 3                          #293 [hci0] 169.123049
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Max slots: 5
> ACL Data RX: Handle 11 flags 0x02 dlen 16                          #294 [hci0] 169.149450
      L2CAP: Information Response (0x0b) ident 1 len 8
        Type: Extended features supported (0x0002)
        Result: Success (0x0000)
        Features: 0x00000280
          Fixed Channels
          Unicast Connectionless Data Reception
< ACL Data TX: Handle 11 flags 0x00 dlen 10                          #295 [hci0] 169.149489
      L2CAP: Information Request (0x0a) ident 2 len 2
        Type: Fixed channels supported (0x0003)
> ACL Data RX: Handle 11 flags 0x02 dlen 10                          #296 [hci0] 169.154451
      L2CAP: Information Request (0x0a) ident 53 len 2
        Type: Extended features supported (0x0002)
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #297 [hci0] 169.154463
      L2CAP: Information Response (0x0b) ident 53 len 8
        Type: Extended features supported (0x0002)
        Result: Success (0x0000)
        Features: 0x000002b8
          Enhanced Retransmission Mode
          Streaming Mode
          FCS Option
          Fixed Channels
          Unicast Connectionless Data Reception
> ACL Data RX: Handle 11 flags 0x02 dlen 20                          #298 [hci0] 169.158292
      L2CAP: Information Response (0x0b) ident 2 len 12
        Type: Fixed channels supported (0x0003)
        Result: Success (0x0000)
        Channels: 0x0001000000000040
          Security Manager (LE)
          Unknown channels (0x1000000000000)
> HCI Event: Remote Name Req Complete (0x07) plen 255                #299 [hci0] 169.161075
        Status: Success (0x00)
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Name: AirPods Pro
@ MGMT Event: Device Connected (0x000b) plen 31                  {0x0001} [hci0] 169.161109
        BR/EDR Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Flags: 0x00000000
        Data length: 18
        Name (complete): AirPods Pro
        Class: 0x240418
          Major class: Audio/Video (headset, speaker, stereo, video, vcr)
          Minor class: Headphones
          Rendering (Printing, Speaker)
          Audio (Speaker, Microphone, Headset)
> HCI Event: Number of Completed Packets (0x13) plen 5               #300 [hci0] 169.162074
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #291: len 10 (2 Kb/s)
        Latency: 40 msec (40-40 msec ~40 msec)
        #295: len 10 (6 Kb/s)
        Latency: 12 msec (12-40 msec ~26 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 10                          #301 [hci0] 169.162288
      L2CAP: Information Request (0x0a) ident 54 len 2
        Type: Fixed channels supported (0x0003)
< ACL Data TX: Handle 11 flags 0x00 dlen 20                          #302 [hci0] 169.162299
      L2CAP: Information Response (0x0b) ident 54 len 12
        Type: Fixed channels supported (0x0003)
        Result: Success (0x0000)
        Channels: 0x0000000000000006
          L2CAP Signaling (BR/EDR)
          Connectionless reception
> HCI Event: Number of Completed Packets (0x13) plen 5               #303 [hci0] 169.166081
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #297: len 16 (11 Kb/s)
        Latency: 11 msec (11-40 msec ~19 msec)
        #302: len 20 (53 Kb/s)
        Latency: 3 msec (3-40 msec ~11 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #304 [hci0] 169.173333
      L2CAP: Connection Request (0x02) ident 55 len 4
        PSM: 1 (0x0001)
        Source CID: 5380
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #305 [hci0] 169.173374
      L2CAP: Connection Response (0x03) ident 55 len 8
        Destination CID: 64
        Source CID: 5380
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #306 [hci0] 169.173380
      L2CAP: Configure Request (0x04) ident 3 len 4
        Destination CID: 5380
        Flags: 0x0000
> HCI Event: Number of Completed Packets (0x13) plen 5               #307 [hci0] 169.177077
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #305: len 16 (42 Kb/s)
        Latency: 3 msec (3-40 msec ~7 msec)
        #306: len 12 (32 Kb/s)
        Latency: 3 msec (3-40 msec ~5 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 16                          #308 [hci0] 169.182189
      L2CAP: Configure Request (0x04) ident 56 len 8
        Destination CID: 64
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 128
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #309 [hci0] 169.182192
      L2CAP: Configure Response (0x05) ident 3 len 10
        Source CID: 64
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
< ACL Data TX: Handle 11 flags 0x00 dlen 18                          #310 [hci0] 169.182241
      L2CAP: Configure Response (0x05) ident 56 len 10
        Source CID: 5380
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 128
> ACL Data RX: Handle 11 flags 0x02 dlen 40                          #311 [hci0] 169.190289
      Channel: 64 len 36 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Request (0x06) tid 1 len 31
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              PnP Information (0x1200)
        Max record count: 64
        Attribute list: [len 23]
          Sequence (6) with 21 bytes [8 extra bits] len 23
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0201
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0202
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0203
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0205
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0xa000
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0xa001
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0xafff
        Continuation state: 0
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 40        #312 [hci0] 169.190430
      Channel: 5380 len 36 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Response (0x07) tid 1 len 31
        Attribute bytes: 28
          Attribute list: [len 24] {position 0}
            Attribute: Unknown (0x0201) [len 2]
              0x1d6b
            Attribute: Unknown (0x0202) [len 2]
              0x0246
            Attribute: Unknown (0x0203) [len 2]
              0x0548
            Attribute: Unknown (0x0205) [len 2]
              0x0002
        Continuation state: 0
> HCI Event: Number of Completed Packets (0x13) plen 5               #313 [hci0] 169.194050
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #310: len 18 (13 Kb/s)
        Latency: 11 msec (3-40 msec ~8 msec)
        #312: len 40 (106 Kb/s)
        Latency: 3 msec (3-40 msec ~6 msec)
        Channel: 5380 [PSM 1 mode Basic (0x00)] {chan 0}
        Channel Latency: 3 msec (3-3 msec ~3 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 22                          #314 [hci0] 169.198291
      Channel: 64 len 18 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Request (0x06) tid 2 len 13
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              Handsfree Audio Gateway (0x111f)
        Max record count: 64
        Attribute list: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0004
        Continuation state: 0
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 33        #315 [hci0] 169.198461
      Channel: 5380 len 29 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Response (0x07) tid 2 len 24
        Attribute bytes: 21
          Attribute list: [len 17] {position 0}
            Attribute: Protocol Descriptor List (0x0004) [len 2]
              Sequence (6) with 3 bytes [8 extra bits] len 5
                UUID (3) with 2 bytes [0 extra bits] len 3
                  L2CAP (0x0100)
              Sequence (6) with 5 bytes [8 extra bits] len 7
                UUID (3) with 2 bytes [0 extra bits] len 3
                  RFCOMM (0x0003)
                Unsigned Integer (1) with 1 byte [0 extra bits] len 2
                  0x0d
        Continuation state: 0
> ACL Data RX: Handle 11 flags 0x02 dlen 22                          #316 [hci0] 169.206339
      Channel: 64 len 18 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Request (0x06) tid 3 len 13
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              Audio Source (0x110a)
        Max record count: 64
        Attribute list: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0004
        Continuation state: 0
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 37        #317 [hci0] 169.206465
      Channel: 5380 len 33 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Response (0x07) tid 3 len 28
        Attribute bytes: 25
          Attribute list: [len 21] {position 0}
            Attribute: Protocol Descriptor List (0x0004) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  L2CAP (0x0100)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0019
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  AVDTP (0x0019)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0103
        Continuation state: 0
> HCI Event: Number of Completed Packets (0x13) plen 5               #318 [hci0] 169.210081
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #315: len 33 (24 Kb/s)
        Latency: 11 msec (3-40 msec ~8 msec)
        Channel: 5380 [PSM 1 mode Basic (0x00)] {chan 0}
        Channel Latency: 11 msec (3-11 msec ~7 msec)
        #317: len 37 (98 Kb/s)
        Latency: 3 msec (3-40 msec ~6 msec)
        Channel: 5380 [PSM 1 mode Basic (0x00)] {chan 0}
        Channel Latency: 3 msec (3-11 msec ~5 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 22                          #319 [hci0] 169.214352
      Channel: 64 len 18 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Request (0x06) tid 4 len 13
        Search pattern: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            UUID (3) with 2 bytes [0 extra bits] len 3
              A/V Remote Control Target (0x110c)
        Max record count: 64
        Attribute list: [len 5]
          Sequence (6) with 3 bytes [8 extra bits] len 5
            Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
              0x0009
        Continuation state: 0
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 29        #320 [hci0] 169.214503
      Channel: 5380 len 25 [PSM 1 mode Basic (0x00)] {chan 0}
      SDP: Service Search Attribute Response (0x07) tid 4 len 20
        Attribute bytes: 17
          Attribute list: [len 13] {position 0}
            Attribute: Bluetooth Profile Descriptor List (0x0009) [len 2]
              Sequence (6) with 6 bytes [8 extra bits] len 8
                UUID (3) with 2 bytes [0 extra bits] len 3
                  A/V Remote Control (0x110e)
                Unsigned Integer (1) with 2 bytes [0 extra bits] len 3
                  0x0105
        Continuation state: 0
> HCI Event: Link Key Request (0x17) plen 6                          #321 [hci0] 169.256172
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
< HCI Command: Link Key Request Reply (0x01|0x000b) plen 22          #322 [hci0] 169.256211
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Link key: 7a64ec98d997df6fe626ef7436a35880
> HCI Event: Command Complete (0x0e) plen 10                         #323 [hci0] 169.259073
      Link Key Request Reply (0x01|0x000b) ncmd 1
        Status: Success (0x00)
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
> HCI Event: Encryption Change (0x08) plen 4                         #324 [hci0] 169.277049
        Status: Success (0x00)
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Encryption: Enabled with E0 (0x01)
< HCI Command: Read Encryption Key Size (0x05|0x0008) plen 2         #325 [hci0] 169.277060
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
> HCI Event: Command Complete (0x0e) plen 7                          #326 [hci0] 169.278076
      Read Encryption Key Size (0x05|0x0008) ncmd 1
        Status: Success (0x00)
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Key size: 16
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #327 [hci0] 169.282444
      L2CAP: Connection Request (0x02) ident 57 len 4
        PSM: 3 (0x0003)
        Source CID: 5637
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #328 [hci0] 169.282501
      L2CAP: Connection Response (0x03) ident 57 len 8
        Destination CID: 65
        Source CID: 5637
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #329 [hci0] 169.282506
      L2CAP: Configure Request (0x04) ident 4 len 8
        Destination CID: 5637
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1021
> HCI Event: Number of Completed Packets (0x13) plen 5               #330 [hci0] 169.286166
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #320: len 29 (3 Kb/s)
        Latency: 71 msec (3-71 msec ~38 msec)
        Channel: 5380 [PSM 1 mode Basic (0x00)] {chan 0}
        Channel Latency: 71 msec (3-71 msec ~38 msec)
        #328: len 16 (42 Kb/s)
        Latency: 3 msec (3-71 msec ~21 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #331 [hci0] 169.291413
      L2CAP: Configure Request (0x04) ident 58 len 4
        Destination CID: 65
        Flags: 0x0000
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #332 [hci0] 169.291414
      L2CAP: Configure Response (0x05) ident 4 len 10
        Source CID: 65
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1021
< ACL Data TX: Handle 11 flags 0x00 dlen 18                          #333 [hci0] 169.291462
      L2CAP: Configure Response (0x05) ident 58 len 10
        Source CID: 5637
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
> HCI Event: Number of Completed Packets (0x13) plen 5               #334 [hci0] 169.295103
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #329: len 16 (10 Kb/s)
        Latency: 12 msec (3-71 msec ~16 msec)
        #333: len 18 (48 Kb/s)
        Latency: 3 msec (3-71 msec ~10 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 8                           #335 [hci0] 169.299444
      Channel: 65 len 4 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Set Async Balance Mode (SABM) (0x2f)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0x3f poll/final 1
         Length: 0
         FCS: 0x1c
< ACL Data TX: Handle 11 flags 0x00 dlen 8                           #336 [hci0] 169.299528
      Channel: 5637 len 4 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Ack (UA) (0x63)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0x73 poll/final 1
         Length: 0
         FCS: 0xd7
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #337 [hci0] 169.307333
      Channel: 65 len 14 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0x70
         MCC Message type: DLC Parameter Negotiation CMD (0x20)
           Length: 8
           dlci 26 frame_type 0 credit_flow 15 pri 0
           ack_timer 0 frame_size 1015 max_retrans 0 credits 0
< ACL Data TX: Handle 11 flags 0x00 dlen 18                          #338 [hci0] 169.307444
      Channel: 5637 len 14 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x01 cr 0 dlci 0x00
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0xaa
         MCC Message type: DLC Parameter Negotiation RSP (0x20)
           Length: 8
           dlci 26 frame_type 0 credit_flow 14 pri 0
           ack_timer 0 frame_size 667 max_retrans 0 credits 7
> HCI Event: Number of Completed Packets (0x13) plen 5               #339 [hci0] 169.311090
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #336: len 8 (5 Kb/s)
        Latency: 11 msec (3-71 msec ~10 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 11 msec (11-11 msec ~11 msec)
        #338: len 18 (48 Kb/s)
        Latency: 3 msec (3-71 msec ~7 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 3 msec (3-11 msec ~7 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 8                           #340 [hci0] 169.314345
      Channel: 65 len 4 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Set Async Balance Mode (SABM) (0x2f)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0x3f poll/final 1
         Length: 0
         FCS: 0xe7
< ACL Data TX: Handle 11 flags 0x00 dlen 8                           #341 [hci0] 169.314583
      Channel: 5637 len 4 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Ack (UA) (0x63)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0x73 poll/final 1
         Length: 0
         FCS: 0x2c
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #342 [hci0] 169.314590
      Channel: 5637 len 8 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x01 cr 0 dlci 0x00
         Control: 0xef poll/final 0
         Length: 4
         FCS: 0xaa
         MCC Message type: Modem Status Command CMD (0x38)
           Length: 2
           dlci 26 
           fc 0 rtc 1 rtr 1 ic 0 dv 1
> HCI Event: Number of Completed Packets (0x13) plen 5               #343 [hci0] 169.320074
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #341: len 8 (12 Kb/s)
        Latency: 5 msec (3-71 msec ~6 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 5 msec (3-11 msec ~6 msec)
        #342: len 12 (19 Kb/s)
        Latency: 5 msec (3-71 msec ~5 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 5 msec (3-11 msec ~6 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #344 [hci0] 169.321452
      Channel: 65 len 8 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0xef poll/final 0
         Length: 4
         FCS: 0x70
         MCC Message type: Modem Status Command CMD (0x38)
           Length: 2
           dlci 26 
           fc 0 rtc 1 rtr 1 ic 0 dv 1
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #345 [hci0] 169.321596
      Channel: 5637 len 8 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x01 cr 0 dlci 0x00
         Control: 0xef poll/final 0
         Length: 4
         FCS: 0xaa
         MCC Message type: Modem Status Command RSP (0x38)
           Length: 2
           dlci 26 
           fc 0 rtc 1 rtr 1 ic 0 dv 1
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #346 [hci0] 169.324288
      Channel: 65 len 8 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x03 cr 1 dlci 0x00
         Control: 0xef poll/final 0
         Length: 4
         FCS: 0x70
         MCC Message type: Modem Status Command RSP (0x38)
           Length: 2
           dlci 26 
           fc 0 rtc 1 rtr 1 ic 0 dv 1
< ACL Data TX: Handle 11 flags 0x00 dlen 9                           #347 [hci0] 169.324377
      Channel: 5637 len 5 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xff poll/final 1
         Length: 0
         FCS: 0x22
         Credits: 33
        22                                               "               
> HCI Event: Number of Completed Packets (0x13) plen 5               #348 [hci0] 169.327047
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #345: len 12 (19 Kb/s)
        Latency: 5 msec (3-71 msec ~5 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 5 msec (3-11 msec ~5 msec)
        #347: len 9 (36 Kb/s)
        Latency: 2 msec (2-71 msec ~4 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 2 msec (2-11 msec ~4 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 9                           #349 [hci0] 169.329345
      Channel: 65 len 5 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xff poll/final 1
         Length: 0
         FCS: 0xf8
         Credits: 255
        f8                                               .               
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #350 [hci0] 169.333288
      L2CAP: Connection Request (0x02) ident 59 len 4
        PSM: 25 (0x0019)
        Source CID: 5894
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #351 [hci0] 169.333289
      L2CAP: Connection Request (0x02) ident 60 len 4
        PSM: 23 (0x0017)
        Source CID: 6151
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #352 [hci0] 169.333366
      L2CAP: Connection Response (0x03) ident 59 len 8
        Destination CID: 66
        Source CID: 5894
        Result: Connection pending (0x0001)
        Status: Authorization pending (0x0002)
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #353 [hci0] 169.333372
      L2CAP: Connection Response (0x03) ident 60 len 8
        Destination CID: 67
        Source CID: 6151
        Result: Connection pending (0x0001)
        Status: Authorization pending (0x0002)
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #354 [hci0] 169.333493
      L2CAP: Connection Response (0x03) ident 60 len 8
        Destination CID: 67
        Source CID: 6151
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #355 [hci0] 169.333499
      L2CAP: Configure Request (0x04) ident 5 len 4
        Destination CID: 6151
        Flags: 0x0000
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #356 [hci0] 169.333501
      L2CAP: Connection Response (0x03) ident 59 len 8
        Destination CID: 66
        Source CID: 5894
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #357 [hci0] 169.333503
      L2CAP: Configure Request (0x04) ident 6 len 4
        Destination CID: 5894
        Flags: 0x0000
> ACL Data RX: Handle 11 flags 0x02 dlen 20                          #358 [hci0] 169.335443
      Channel: 65 len 16 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 12
         FCS: 0xe4
        41 54 2b 42 52 53 46 3d 36 36 37 0d e4           AT+BRSF=667..   
< ACL Data TX: Handle 11 flags 0x00 dlen 23                          #359 [hci0] 169.335730
      Channel: 5637 len 19 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 15
         FCS: 0x3e
        0d 0a 2b 42 52 53 46 3a 20 31 36 33 32 0d 0a 3e  ..+BRSF: 1632..>
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #360 [hci0] 169.335737
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
> HCI Event: Number of Completed Packets (0x13) plen 5               #361 [hci0] 169.337170
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #352: len 16 (42 Kb/s)
        Latency: 3 msec (2-71 msec ~3 msec)
        #353: len 16 (42 Kb/s)
        Latency: 3 msec (2-71 msec ~3 msec)
> HCI Event: Number of Completed Packets (0x13) plen 5               #362 [hci0] 169.340044
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #354: len 16 (21 Kb/s)
        Latency: 6 msec (2-71 msec ~5 msec)
        #355: len 12 (16 Kb/s)
        Latency: 6 msec (2-71 msec ~5 msec)
> HCI Event: Number of Completed Packets (0x13) plen 5               #363 [hci0] 169.342068
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #356: len 16 (16 Kb/s)
        Latency: 8 msec (2-71 msec ~7 msec)
        #357: len 12 (12 Kb/s)
        Latency: 8 msec (2-71 msec ~7 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 16                          #364 [hci0] 169.343289
      L2CAP: Configure Request (0x04) ident 61 len 8
        Destination CID: 67
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #365 [hci0] 169.343291
      L2CAP: Configure Response (0x05) ident 5 len 10
        Source CID: 67
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
< ACL Data TX: Handle 11 flags 0x00 dlen 18                          #366 [hci0] 169.343344
      L2CAP: Configure Response (0x05) ident 61 len 10
        Source CID: 6151
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 18        #367 [hci0] 169.343572
      Channel: 6151 len 14 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Command: type 0x00 label 0 PID 0x110e
        AV/C: Status: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: GetCapabilities pt Single len 0x0001
            CapabilityID: 0x03 (EventsID)
> HCI Event: Number of Completed Packets (0x13) plen 5               #368 [hci0] 169.345045
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #359: len 23 (20 Kb/s)
        Latency: 9 msec (2-71 msec ~8 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 9 msec (2-11 msec ~6 msec)
        #360: len 14 (12 Kb/s)
        Latency: 9 msec (2-71 msec ~8 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 9 msec (2-11 msec ~8 msec)
> HCI Event: Number of Completed Packets (0x13) plen 5               #369 [hci0] 169.347074
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #366: len 18 (48 Kb/s)
        Latency: 3 msec (2-71 msec ~6 msec)
        #367: len 18 (48 Kb/s)
        Latency: 3 msec (2-71 msec ~4 msec)
        Channel: 6151 [PSM 23 mode Basic (0x00)] {chan 5}
        Channel Latency: 3 msec (3-3 msec ~3 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 20                          #370 [hci0] 169.349303
      L2CAP: Configure Request (0x04) ident 62 len 12
        Destination CID: 66
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
        Option: Flush Timeout (0x02) [mandatory]
          Flush timeout: 30
< ACL Data TX: Handle 11 flags 0x00 dlen 18                          #371 [hci0] 169.349360
      L2CAP: Configure Response (0x05) ident 62 len 10
        Source CID: 5894
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #372 [hci0] 169.351353
      L2CAP: Configure Response (0x05) ident 6 len 10
        Source CID: 66
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
> ACL Data RX: Handle 11 flags 0x02 dlen 27                          #373 [hci0] 169.351354
      Channel: 65 len 23 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 19
         FCS: 0xe4
        41 54 2b 42 41 43 3d 31 2c 32 2c 31 32 38 2c 32  AT+BAC=1,2,128,2
        35 36 0d e4                                      56..            
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #374 [hci0] 169.351504
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
> HCI Event: Number of Completed Packets (0x13) plen 5               #375 [hci0] 169.371069
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #371: len 18 (6 Kb/s)
        Latency: 21 msec (2-71 msec ~13 msec)
        #374: len 14 (5 Kb/s)
        Latency: 19 msec (2-71 msec ~16 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 19 msec (2-19 msec ~13 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #376 [hci0] 169.372355
      Channel: 67 len 14 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Command: type 0x00 label 4 PID 0x110e
        AV/C: Status: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: GetCapabilities pt Single len 0x0001
            CapabilityID: 0x03 (EventsID)
> ACL Data RX: Handle 11 flags 0x02 dlen 22                          #377 [hci0] 169.372356
      Channel: 67 len 18 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Response: type 0x00 label 0 PID 0x110e
        AV/C: Stable: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: GetCapabilities pt Single len 0x0005
            CapabilityID: 0x03 (EventsID)
            CapabilityCount: 0x03
            EventsID: 0x01 (EVENT_PLAYBACK_STATUS_CHANGED)
            EventsID: 0x02 (EVENT_TRACK_CHANGED)
            EventsID: 0x0d (EVENT_VOLUME_CHANGED)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 27        #378 [hci0] 169.372483
      Channel: 6151 len 23 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Response: type 0x00 label 4 PID 0x110e
        AV/C: Stable: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: GetCapabilities pt Single len 0x000a
            CapabilityID: 0x03 (EventsID)
            CapabilityCount: 0x08
            EventsID: 0x01 (EVENT_PLAYBACK_STATUS_CHANGED)
            EventsID: 0x02 (EVENT_TRACK_CHANGED)
            EventsID: 0x03 (EVENT_TRACK_REACHED_END)
            EventsID: 0x04 (EVENT_TRACK_REACHED_START)
            EventsID: 0x08 (EVENT_PLAYER_APPLICATION_SETTING_CHANGED)
            EventsID: 0x0a (EVENT_AVAILABLE_PLAYERS_CHANGED)
            EventsID: 0x0b (EVENT_ADDRESSED_PLAYER_CHANGED)
            EventsID: 0x0d (EVENT_VOLUME_CHANGED)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #379 [hci0] 169.372491
      Channel: 6151 len 18 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Command: type 0x00 label 1 PID 0x110e
        AV/C: Notify: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: RegisterNotification pt Single len 0x0005
            EventID: 0x0d (EVENT_VOLUME_CHANGED)
            Interval: 0x00000000 (0 seconds)
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #380 [hci0] 169.374289
      L2CAP: Disconnection Request (0x06) ident 63 len 4
        Destination CID: 64
        Source CID: 5380
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #381 [hci0] 169.374327
      L2CAP: Disconnection Response (0x07) ident 63 len 4
        Destination CID: 64
        Source CID: 5380
> HCI Event: Number of Completed Packets (0x13) plen 5               #382 [hci0] 169.377039
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #378: len 27 (54 Kb/s)
        Latency: 4 msec (2-71 msec ~10 msec)
        Channel: 6151 [PSM 23 mode Basic (0x00)] {chan 5}
        Channel Latency: 4 msec (3-4 msec ~4 msec)
        #379: len 22 (44 Kb/s)
        Latency: 4 msec (2-71 msec ~7 msec)
        Channel: 6151 [PSM 23 mode Basic (0x00)] {chan 5}
        Channel Latency: 4 msec (3-4 msec ~4 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #383 [hci0] 169.378332
      Channel: 65 len 14 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0xe4
        41 54 2b 43 49 4e 44 3d 3f 0d e4                 AT+CIND=?..     
> ACL Data RX: Handle 11 flags 0x02 dlen 6                           #384 [hci0] 169.378333
      Channel: 66 len 2 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Discover (0x01) Command (0x00) type 0x00 label 6 nosp 0
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 44        #385 [hci0] 169.378476
      Channel: 5894 len 40 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Discover (0x01) Response Accept (0x02) type 0x00 label 6 nosp 0
        ACP SEID: 1
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 2
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
        ACP SEID: 3
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 4
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
        ACP SEID: 5
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 6
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
        ACP SEID: 7
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 8
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
        ACP SEID: 9
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 10
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 11
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 12
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 13
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 14
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 15
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 16
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
        ACP SEID: 17
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
        ACP SEID: 18
          Media Type: Audio (0x00)
          SEP Type: SNK (0x01)
          In use: No
        ACP SEID: 19
          Media Type: Audio (0x00)
          SEP Type: SRC (0x00)
          In use: No
< ACL Data TX: Handle 11 flags 0x00 dlen 140                         #386 [hci0] 169.378490
      Channel: 5637 len 136 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 131
         FCS: 0x3e
        0d 0a 2b 43 49 4e 44 3a 28 22 73 65 72 76 69 63  ..+CIND:("servic
        65 22 2c 28 30 2d 31 29 29 2c 28 22 63 61 6c 6c  e",(0-1)),("call
        22 2c 28 30 2d 31 29 29 2c 28 22 63 61 6c 6c 73  ",(0-1)),("calls
        65 74 75 70 22 2c 28 30 2d 33 29 29 2c 28 22 63  etup",(0-3)),("c
        61 6c 6c 68 65 6c 64 22 2c 28 30 2d 32 29 29 2c  allheld",(0-2)),
        28 22 73 69 67 6e 61 6c 22 2c 28 30 2d 35 29 29  ("signal",(0-5))
        2c 28 22 72 6f 61 6d 22 2c 28 30 2d 31 29 29 2c  ,("roam",(0-1)),
        28 22 62 61 74 74 63 68 67 22 2c 28 30 2d 35 29  ("battchg",(0-5)
        29 0d 0a 3e                                      )..>            
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #387 [hci0] 169.378492
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
> HCI Event: Number of Completed Packets (0x13) plen 5               #388 [hci0] 169.381097
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #381: len 12 (16 Kb/s)
        Latency: 6 msec (2-71 msec ~7 msec)
        #385: len 44 (176 Kb/s)
        Latency: 2 msec (2-71 msec ~4 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 2 msec (2-2 msec ~2 msec)
> HCI Event: Number of Completed Packets (0x13) plen 5               #389 [hci0] 169.385053
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #386: len 140 (186 Kb/s)
        Latency: 6 msec (2-71 msec ~5 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 6 msec (2-19 msec ~10 msec)
        #387: len 14 (18 Kb/s)
        Latency: 6 msec (2-71 msec ~6 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 6 msec (2-19 msec ~8 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 22                          #390 [hci0] 169.385293
      Channel: 67 len 18 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Command: type 0x00 label 5 PID 0x110e
        AV/C: Notify: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: RegisterNotification pt Single len 0x0005
            EventID: 0x01 (EVENT_PLAYBACK_STATUS_CHANGED)
            Interval: 0x00000000 (0 seconds)
> ACL Data RX: Handle 11 flags 0x02 dlen 19                          #391 [hci0] 169.385294
      Channel: 67 len 15 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Response: type 0x00 label 1 PID 0x110e
        AV/C: Interim: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: RegisterNotification pt Single len 0x0002
            EventID: 0x0d (EVENT_VOLUME_CHANGED)
            Volume: 80.31% (102/127)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 19        #392 [hci0] 169.385366
      Channel: 6151 len 15 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Response: type 0x00 label 5 PID 0x110e
        AV/C: Interim: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: RegisterNotification pt Single len 0x0002
            EventID: 0x01 (EVENT_PLAYBACK_STATUS_CHANGED)
            PlayStatus: 0x00 (STOPPED)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #393 [hci0] 169.388290
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 7 nosp 0
        ACP SEID: 1
> ACL Data RX: Handle 11 flags 0x02 dlen 17                          #394 [hci0] 169.388292
      Channel: 65 len 13 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 9
         FCS: 0xe4
        41 54 2b 43 49 4e 44 3f 0d e4                    AT+CIND?..      
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #395 [hci0] 169.388341
      Channel: 5894 len 18 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 7 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Sony Corporation (0x0000012d)
            Vendor Specific Codec ID: LDAC (0x00aa)
              Unknown: 0x073c
        Service Category: Delay Reporting (0x08)
< ACL Data TX: Handle 11 flags 0x00 dlen 32                          #396 [hci0] 169.388349
      Channel: 5637 len 28 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 24
         FCS: 0x3e
        0d 0a 2b 43 49 4e 44 3a 20 30 2c 30 2c 30 2c 30  ..+CIND: 0,0,0,0
        2c 30 2c 30 2c 30 0d 0a 3e                       ,0,0,0..>       
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #397 [hci0] 169.388351
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
> HCI Event: Number of Completed Packets (0x13) plen 5               #398 [hci0] 169.391073
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #392: len 19 (30 Kb/s)
        Latency: 5 msec (2-71 msec ~5 msec)
        Channel: 6151 [PSM 23 mode Basic (0x00)] {chan 5}
        Channel Latency: 5 msec (3-5 msec ~4 msec)
        #395: len 22 (88 Kb/s)
        Latency: 2 msec (2-71 msec ~4 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 2 msec (2-2 msec ~2 msec)
> HCI Event: Number of Completed Packets (0x13) plen 5               #399 [hci0] 169.394046
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #396: len 32 (51 Kb/s)
        Latency: 5 msec (2-71 msec ~5 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 5 msec (2-19 msec ~7 msec)
        #397: len 14 (22 Kb/s)
        Latency: 5 msec (2-71 msec ~5 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 5 msec (2-19 msec ~6 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #400 [hci0] 169.398446
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 8 nosp 0
        ACP SEID: 3
> ACL Data RX: Handle 11 flags 0x02 dlen 24                          #401 [hci0] 169.398448
      Channel: 65 len 20 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 16
         FCS: 0xe4
        41 54 2b 43 4d 45 52 3d 33 2c 30 2c 30 2c 31 0d  AT+CMER=3,0,0,1.
        e4                                               .               
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 25        #402 [hci0] 169.398553
      Channel: 5894 len 21 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 8 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Qualcomm Technologies, Inc. (0x000000d7)
            Vendor Specific Codec ID: aptX HD (0x0024)
              Frequency: 0xf0
                16000
                32000
                44100
                48000
              Channel Mode: 0x02
                Stereo
        Service Category: Delay Reporting (0x08)
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #403 [hci0] 169.398617
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
< ACL Data TX: Handle 11 flags 0x00 dlen 19                          #404 [hci0] 169.398622
      Channel: 5637 len 15 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 11
         FCS: 0x3e
        0d 0a 2b 42 43 53 3a 20 32 0d 0a 3e              ..+BCS: 2..>    
> HCI Event: Number of Completed Packets (0x13) plen 5               #405 [hci0] 169.404072
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #402: len 25 (40 Kb/s)
        Latency: 5 msec (2-71 msec ~5 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 5 msec (2-5 msec ~4 msec)
        #403: len 14 (22 Kb/s)
        Latency: 5 msec (2-71 msec ~5 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 5 msec (2-19 msec ~5 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #406 [hci0] 169.437447
      Channel: 65 len 14 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0xe4
        41 54 2b 56 47 53 3d 31 31 0d e4                 AT+VGS=11..     
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #407 [hci0] 169.437449
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 9 nosp 0
        ACP SEID: 5
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 21        #408 [hci0] 169.437569
      Channel: 5894 len 17 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 9 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: APT Ltd. (0x0000004f)
            Vendor Specific Codec ID: aptX (0x0001)
              Frequency: 0xf0
                16000
                32000
                44100
                48000
              Channel Mode: 0x02
                Stereo
        Service Category: Delay Reporting (0x08)
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #409 [hci0] 169.437644
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
> HCI Event: Number of Completed Packets (0x13) plen 5               #410 [hci0] 169.441042
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #404: len 19 (3 Kb/s)
        Latency: 42 msec (2-71 msec ~23 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 42 msec (2-42 msec ~24 msec)
        #408: len 21 (56 Kb/s)
        Latency: 3 msec (2-71 msec ~13 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 3 msec (2-5 msec ~3 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 17                          #411 [hci0] 169.447443
      Channel: 65 len 13 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 9
         FCS: 0xe4
        41 54 2b 56 47 4d 3d 38 0d e4                    AT+VGM=8..      
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #412 [hci0] 169.447445
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 10 nosp 0
        ACP SEID: 7
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 18        #413 [hci0] 169.447565
      Channel: 5894 len 14 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 10 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: SBC (0x00)
            Frequency: 0xf0
              16000
              32000
              44100
              48000
            Channel Mode: 0x0f
              Mono
              Dual Channel
              Stereo
              Joint Stereo
            Block Length: 0xf0
              4
              8
              12
              16
            Subbands: 0x0c
              4
              8
            Allocation Method: 0x03
              SNR
              Loudness
            Minimum Bitpool: 2
            Maximum Bitpool: 64
        Service Category: Delay Reporting (0x08)
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #414 [hci0] 169.447646
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
> HCI Event: Number of Completed Packets (0x13) plen 5               #415 [hci0] 169.451161
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #409: len 14 (8 Kb/s)
        Latency: 13 msec (2-71 msec ~13 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 13 msec (2-42 msec ~18 msec)
        #413: len 18 (48 Kb/s)
        Latency: 3 msec (2-71 msec ~8 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 3 msec (2-5 msec ~3 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 29                          #416 [hci0] 169.457321
      Channel: 65 len 25 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 21
         FCS: 0xe4
        41 54 2b 42 49 41 3d 30 2c 31 2c 31 2c 31 2c 30  AT+BIA=0,1,1,1,0
        2c 30 2c 30 0d e4                                ,0,0..          
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #417 [hci0] 169.457322
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 11 nosp 0
        ACP SEID: 9
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #418 [hci0] 169.457446
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 18        #419 [hci0] 169.457454
      Channel: 5894 len 14 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 11 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: SBC (0x00)
            Frequency: 0xf0
              16000
              32000
              44100
              48000
            Channel Mode: 0x0f
              Mono
              Dual Channel
              Stereo
              Joint Stereo
            Block Length: 0xf0
              4
              8
              12
              16
            Subbands: 0x0c
              4
              8
            Allocation Method: 0x03
              SNR
              Loudness
            Minimum Bitpool: 2
            Maximum Bitpool: 64
        Service Category: Delay Reporting (0x08)
> HCI Event: Number of Completed Packets (0x13) plen 5               #420 [hci0] 169.461105
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #414: len 14 (8 Kb/s)
        Latency: 13 msec (2-71 msec ~11 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 13 msec (2-42 msec ~16 msec)
        #418: len 14 (37 Kb/s)
        Latency: 3 msec (2-71 msec ~7 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 3 msec (2-42 msec ~9 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #421 [hci0] 169.478288
      Channel: 65 len 14 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 10
         FCS: 0xe4
        41 54 2b 4e 52 45 43 3d 30 0d e4                 AT+NREC=0..     
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #422 [hci0] 169.478289
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 12 nosp 0
        ACP SEID: 10
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #423 [hci0] 169.478468
      Channel: 5894 len 18 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 12 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Qualcomm Technologies, Inc. (0x000000d7)
            Vendor Specific Codec ID: Unknown (0x0002)
        f2 00                                            ..              
        Service Category: Delay Reporting (0x08)
< ACL Data TX: Handle 11 flags 0x00 dlen 17                          #424 [hci0] 169.478534
      Channel: 5637 len 13 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 9
         FCS: 0x3e
        0d 0a 45 52 52 4f 52 0d 0a 3e                    ..ERROR..>      
> HCI Event: Number of Completed Packets (0x13) plen 5               #425 [hci0] 169.514074
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #419: len 18 (2 Kb/s)
        Latency: 56 msec (2-71 msec ~31 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 56 msec (2-56 msec ~30 msec)
        #423: len 22 (5 Kb/s)
        Latency: 35 msec (2-71 msec ~33 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 35 msec (2-56 msec ~32 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #426 [hci0] 169.518289
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 13 nosp 0
        ACP SEID: 11
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #427 [hci0] 169.518377
      Channel: 5894 len 18 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 13 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Cambridge Silicon Radio (0x0000000a)
            Vendor Specific Codec ID: aptX Low Latency (0x0002)
              Frequency: 0xf0
                16000
                32000
                44100
                48000
              Channel Mode: 0x02
                Stereo
            Bidirectional link: No
        Service Category: Delay Reporting (0x08)
> HCI Event: Number of Completed Packets (0x13) plen 5               #428 [hci0] 169.521069
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #424: len 17 (3 Kb/s)
        Latency: 42 msec (2-71 msec ~38 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 42 msec (2-42 msec ~26 msec)
        #427: len 22 (88 Kb/s)
        Latency: 2 msec (2-71 msec ~20 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 2 msec (2-56 msec ~17 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #429 [hci0] 169.527315
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 14 nosp 0
        ACP SEID: 12
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #430 [hci0] 169.527421
      Channel: 5894 len 18 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 14 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Qualcomm Technologies, Inc. (0x000000d7)
            Vendor Specific Codec ID: Unknown (0x0002)
        f2 01                                            ..              
        Service Category: Delay Reporting (0x08)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #431 [hci0] 169.536452
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 15 nosp 0
        ACP SEID: 13
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #432 [hci0] 169.536558
      Channel: 5894 len 18 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 15 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Cambridge Silicon Radio (0x0000000a)
            Vendor Specific Codec ID: aptX Low Latency (0x0002)
              Frequency: 0xf0
                16000
                32000
                44100
                48000
              Channel Mode: 0x02
                Stereo
            Bidirectional link: Yes
        Service Category: Delay Reporting (0x08)
> HCI Event: Number of Completed Packets (0x13) plen 5               #433 [hci0] 169.540142
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #430: len 22 (14 Kb/s)
        Latency: 12 msec (2-71 msec ~16 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 12 msec (2-56 msec ~15 msec)
        #432: len 22 (58 Kb/s)
        Latency: 3 msec (2-71 msec ~10 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 3 msec (2-56 msec ~9 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #434 [hci0] 169.545334
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 0 nosp 0
        ACP SEID: 14
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #435 [hci0] 169.545432
      Channel: 5894 len 18 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 0 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Cambridge Silicon Radio (0x0000000a)
            Vendor Specific Codec ID: FastStream (0x0001)
              Direction: 0x01
                Sink
              Sink Frequency: 0x03
                48000
                44100
              Source Frequency: 0x20
                16000
        Service Category: Delay Reporting (0x08)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #436 [hci0] 169.573362
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 1 nosp 0
        ACP SEID: 15
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #437 [hci0] 169.573485
      Channel: 5894 len 18 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 1 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: Cambridge Silicon Radio (0x0000000a)
            Vendor Specific Codec ID: FastStream (0x0001)
              Direction: 0x03
                Sink
                Source
              Sink Frequency: 0x03
                48000
                44100
              Source Frequency: 0x20
                16000
        Service Category: Delay Reporting (0x08)
> HCI Event: Number of Completed Packets (0x13) plen 5               #438 [hci0] 169.576039
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #435: len 22 (5 Kb/s)
        Latency: 30 msec (2-71 msec ~20 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 30 msec (2-56 msec ~20 msec)
        #437: len 22 (88 Kb/s)
        Latency: 2 msec (2-71 msec ~11 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 2 msec (2-56 msec ~11 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #439 [hci0] 169.581345
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 2 nosp 0
        ACP SEID: 17
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 38        #440 [hci0] 169.581480
      Channel: 5894 len 34 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 2 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: The Linux Foundation (0x000005f1)
            Vendor Specific Codec ID: Unknown (0x1005)
        40 00 ff ff ff 0f 1f 00 00 00 00 00 00 00 00 00  @...............
        00 00                                            ..              
        Service Category: Delay Reporting (0x08)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #441 [hci0] 169.590289
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Command (0x00) type 0x00 label 3 nosp 0
        ACP SEID: 19
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 38        #442 [hci0] 169.590439
      Channel: 5894 len 34 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Get All Capabilities (0x0c) Response Accept (0x02) type 0x00 label 3 nosp 0
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: Non-A2DP (0xff)
            Vendor ID: The Linux Foundation (0x000005f1)
            Vendor Specific Codec ID: Unknown (0x1005)
        40 00 ff ff ff 0f 1f 00 00 40 00 ff ff ff 0f 1f  @........@......
        00 00                                            ..              
        Service Category: Delay Reporting (0x08)
> HCI Event: Number of Completed Packets (0x13) plen 5               #443 [hci0] 169.594162
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #440: len 38 (25 Kb/s)
        Latency: 12 msec (2-71 msec ~12 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 12 msec (2-56 msec ~11 msec)
        #442: len 38 (101 Kb/s)
        Latency: 3 msec (2-71 msec ~7 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 3 msec (2-56 msec ~7 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 20                          #444 [hci0] 169.598453
      Channel: 66 len 16 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Set Configuration (0x03) Command (0x00) type 0x00 label 4 nosp 0
        ACP SEID: 9
        INT SEID: 1
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: SBC (0x00)
            Frequency: 44100 (0x20)
            Channel Mode: Joint Stereo (0x01)
            Block Length: 16 (0x10)
            Subbands: 8 (0x04)
            Allocation Method: Loudness (0x01)
            Minimum Bitpool: 2
            Maximum Bitpool: 53
        Service Category: Delay Reporting (0x08)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 6         #445 [hci0] 169.599498
      Channel: 5894 len 2 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Set Configuration (0x03) Response Accept (0x02) type 0x00 label 4 nosp 0
> ACL Data RX: Handle 11 flags 0x02 dlen 9                           #446 [hci0] 169.607295
      Channel: 66 len 5 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Delay Report (0x0d) Command (0x00) type 0x00 label 5 nosp 0
        ACP SEID: 9
        Delay: 150.0ms
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 6         #447 [hci0] 169.607442
      Channel: 5894 len 2 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Delay Report (0x0d) Response Accept (0x02) type 0x00 label 5 nosp 0
> HCI Event: Number of Completed Packets (0x13) plen 5               #448 [hci0] 169.611040
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #445: len 6 (4 Kb/s)
        Latency: 11 msec (2-71 msec ~9 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 11 msec (2-56 msec ~9 msec)
        #447: len 6 (16 Kb/s)
        Latency: 3 msec (2-71 msec ~6 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 3 msec (2-56 msec ~6 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 7                           #449 [hci0] 169.614289
      Channel: 66 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Open (0x06) Command (0x00) type 0x00 label 6 nosp 0
        ACP SEID: 9
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 6         #450 [hci0] 169.614438
      Channel: 5894 len 2 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Open (0x06) Response Accept (0x02) type 0x00 label 6 nosp 0
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #451 [hci0] 169.624302
      L2CAP: Connection Request (0x02) ident 64 len 4
        PSM: 25 (0x0019)
        Source CID: 6404
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #452 [hci0] 169.624357
      L2CAP: Connection Response (0x03) ident 64 len 8
        Destination CID: 64
        Source CID: 6404
        Result: Connection pending (0x0001)
        Status: Authorization pending (0x0002)
< ACL Data TX: Handle 11 flags 0x00 dlen 16                          #453 [hci0] 169.624433
      L2CAP: Connection Response (0x03) ident 64 len 8
        Destination CID: 64
        Source CID: 6404
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #454 [hci0] 169.624437
      L2CAP: Configure Request (0x04) ident 7 len 4
        Destination CID: 6404
        Flags: 0x0000
> HCI Event: Number of Completed Packets (0x13) plen 5               #455 [hci0] 169.629068
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #450: len 6 (3 Kb/s)
        Latency: 14 msec (2-71 msec ~10 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 14 msec (2-56 msec ~10 msec)
        #452: len 16 (32 Kb/s)
        Latency: 4 msec (2-71 msec ~7 msec)
> HCI Event: Number of Completed Packets (0x13) plen 5               #456 [hci0] 169.652155
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #453: len 16 (4 Kb/s)
        Latency: 27 msec (2-71 msec ~17 msec)
        #454: len 12 (3 Kb/s)
        Latency: 27 msec (2-71 msec ~22 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 20                          #457 [hci0] 169.652289
      L2CAP: Configure Request (0x04) ident 65 len 12
        Destination CID: 64
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
        Option: Flush Timeout (0x02) [mandatory]
          Flush timeout: 30
< ACL Data TX: Handle 11 flags 0x00 dlen 18                          #458 [hci0] 169.652296
      L2CAP: Configure Response (0x05) ident 65 len 10
        Source CID: 6404
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #459 [hci0] 169.656290
      L2CAP: Configure Response (0x05) ident 7 len 10
        Source CID: 64
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
> HCI Event: Number of Completed Packets (0x13) plen 5               #460 [hci0] 169.902168
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 1
        #458: len 18 (0 Kb/s)
        Latency: 249 msec (2-249 msec ~136 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 7         #461 [hci0] 172.599323
      Channel: 5894 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Close (0x08) Command (0x00) type 0x00 label 13 nosp 0
        ACP SEID: 1
> ACL Data RX: Handle 11 flags 0x02 dlen 6                           #462 [hci0] 172.620290
      Channel: 66 len 2 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Close (0x08) Response Accept (0x02) type 0x00 label 13 nosp 0
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #463 [hci0] 172.620446
      L2CAP: Disconnection Request (0x06) ident 8 len 4
        Destination CID: 6404
        Source CID: 64
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #464 [hci0] 172.620515
      L2CAP: Disconnection Request (0x06) ident 9 len 4
        Destination CID: 6151
        Source CID: 67
> HCI Event: Number of Completed Packets (0x13) plen 5               #465 [hci0] 172.623995
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #461: len 7 (2 Kb/s)
        Latency: 24 msec (2-249 msec ~80 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 24 msec (2-56 msec ~17 msec)
        #463: len 12 (32 Kb/s)
        Latency: 3 msec (2-249 msec ~42 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #466 [hci0] 172.629288
      L2CAP: Disconnection Response (0x07) ident 8 len 4
        Destination CID: 6404
        Source CID: 64
> ACL Data RX: Handle 11 flags 0x02 dlen 12                          #467 [hci0] 172.629290
      L2CAP: Disconnection Response (0x07) ident 9 len 4
        Destination CID: 6151
        Source CID: 67
> HCI Event: Number of Completed Packets (0x13) plen 5               #468 [hci0] 172.777092
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 1
        #464: len 12 (0 Kb/s)
        Latency: 156 msec (2-249 msec ~99 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 20        #469 [hci0] 173.121335
      Channel: 5894 len 16 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Set Configuration (0x03) Command (0x00) type 0x00 label 14 nosp 0
        ACP SEID: 1
        INT SEID: 7
        Service Category: Media Transport (0x01)
        Service Category: Media Codec (0x07)
          Media Type: Audio (0x00)
          Media Codec: SBC (0x00)
            Frequency: 48000 (0x10)
            Channel Mode: Joint Stereo (0x01)
            Block Length: 16 (0x10)
            Subbands: 8 (0x04)
            Allocation Method: Loudness (0x01)
            Minimum Bitpool: 2
            Maximum Bitpool: 53
        Service Category: Delay Reporting (0x08)
> ACL Data RX: Handle 11 flags 0x02 dlen 6                           #470 [hci0] 173.130364
      Channel: 66 len 2 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Set Configuration (0x03) Response Accept (0x02) type 0x00 label 14 nosp 0
> ACL Data RX: Handle 11 flags 0x02 dlen 9                           #471 [hci0] 173.130366
      Channel: 66 len 5 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Delay Report (0x0d) Command (0x00) type 0x00 label 7 nosp 0
        ACP SEID: 7
        Delay: 150.0ms
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 6         #472 [hci0] 173.130573
      Channel: 5894 len 2 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Delay Report (0x0d) Response Accept (0x02) type 0x00 label 7 nosp 0
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 7         #473 [hci0] 173.131106
      Channel: 5894 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Open (0x06) Command (0x00) type 0x00 label 15 nosp 0
        ACP SEID: 1
> HCI Event: Number of Completed Packets (0x13) plen 5               #474 [hci0] 173.133981
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #469: len 20 (13 Kb/s)
        Latency: 12 msec (2-249 msec ~55 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 12 msec (2-56 msec ~15 msec)
        #472: len 6 (16 Kb/s)
        Latency: 3 msec (2-249 msec ~29 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 3 msec (2-56 msec ~9 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 6                           #475 [hci0] 173.138297
      Channel: 66 len 2 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Open (0x06) Response Accept (0x02) type 0x00 label 15 nosp 0
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #476 [hci0] 173.138396
      L2CAP: Connection Request (0x02) ident 10 len 4
        PSM: 25 (0x0019)
        Source CID: 64
> HCI Event: Number of Completed Packets (0x13) plen 5               #477 [hci0] 173.140954
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #473: len 7 (6 Kb/s)
        Latency: 9 msec (2-249 msec ~19 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 9 msec (2-56 msec ~9 msec)
        #476: len 12 (48 Kb/s)
        Latency: 2 msec (2-249 msec ~11 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 16                          #478 [hci0] 173.145332
      L2CAP: Connection Response (0x03) ident 10 len 8
        Destination CID: 6660
        Source CID: 64
        Result: Connection pending (0x0001)
        Status: Authentication pending (0x0001)
> ACL Data RX: Handle 11 flags 0x02 dlen 16                          #479 [hci0] 173.148346
      L2CAP: Connection Response (0x03) ident 10 len 8
        Destination CID: 6660
        Source CID: 64
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #480 [hci0] 173.148358
      L2CAP: Configure Request (0x04) ident 11 len 4
        Destination CID: 6660
        Flags: 0x0000
> ACL Data RX: Handle 11 flags 0x02 dlen 20                          #481 [hci0] 173.173290
      L2CAP: Configure Request (0x04) ident 66 len 12
        Destination CID: 64
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
        Option: Flush Timeout (0x02) [mandatory]
          Flush timeout: 30
< ACL Data TX: Handle 11 flags 0x00 dlen 18                          #482 [hci0] 173.173333
      L2CAP: Configure Response (0x05) ident 66 len 10
        Source CID: 6660
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
> HCI Event: Number of Completed Packets (0x13) plen 5               #483 [hci0] 173.178947
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #480: len 12 (3 Kb/s)
        Latency: 30 msec (2-249 msec ~20 msec)
        #482: len 18 (28 Kb/s)
        Latency: 5 msec (2-249 msec ~13 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #484 [hci0] 173.181286
      L2CAP: Configure Response (0x05) ident 11 len 10
        Source CID: 64
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 7         #485 [hci0] 173.195057
      Channel: 5894 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Start (0x07) Command (0x00) type 0x00 label 0 nosp 0
        ACP SEID: 1
> ACL Data RX: Handle 11 flags 0x02 dlen 6                           #486 [hci0] 173.334822
      Channel: 66 len 2 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Start (0x07) Response Accept (0x02) type 0x00 label 0 nosp 0
bluetoothd[1272]: = /org/bluez/hci0/dev_50_57_8A_CA_BE_CB/sep1/fd4: fd(37) r..   173.334940
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #487 [hci0] 173.344414
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #488 [hci0] 173.351983
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #485: len 7 (0 Kb/s)
        Latency: 156 msec (2-249 msec ~85 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 156 msec (2-156 msec ~83 msec)
        #487: len 969 (1107 Kb/s)
        Latency: 7 msec (2-249 msec ~46 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 7 msec (7-7 msec ~7 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #489 [hci0] 173.386929
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #490 [hci0] 173.408428
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #491 [hci0] 173.420981
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #489: len 969 (228 Kb/s)
        Latency: 34 msec (2-249 msec ~40 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 34 msec (7-34 msec ~20 msec)
        #490: len 969 (646 Kb/s)
        Latency: 12 msec (2-249 msec ~26 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 12 msec (7-34 msec ~16 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #492 [hci0] 173.429661
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #493 [hci0] 173.450974
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #494 [hci0] 173.458945
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #492: len 969 (267 Kb/s)
        Latency: 29 msec (2-249 msec ~27 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 29 msec (7-34 msec ~22 msec)
        #493: len 969 (1107 Kb/s)
        Latency: 7 msec (2-249 msec ~17 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 7 msec (7-34 msec ~15 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #495 [hci0] 173.472262
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #496 [hci0] 173.493649
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #497 [hci0] 173.505067
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #495: len 969 (242 Kb/s)
        Latency: 32 msec (2-249 msec ~25 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 32 msec (7-34 msec ~24 msec)
        #496: len 969 (704 Kb/s)
        Latency: 11 msec (2-249 msec ~18 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 11 msec (7-34 msec ~17 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #498 [hci0] 173.514990
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #499 [hci0] 173.536354
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #500 [hci0] 173.544967
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #498: len 969 (267 Kb/s)
        Latency: 29 msec (2-249 msec ~24 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 29 msec (7-34 msec ~23 msec)
        #499: len 969 (969 Kb/s)
        Latency: 8 msec (2-249 msec ~16 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 8 msec (7-34 msec ~16 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #501 [hci0] 173.557595
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #502 [hci0] 173.578965
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #503 [hci0] 173.598972
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #501: len 969 (189 Kb/s)
        Latency: 41 msec (2-249 msec ~28 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 41 msec (7-41 msec ~28 msec)
        #502: len 969 (387 Kb/s)
        Latency: 20 msec (2-249 msec ~24 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 20 msec (7-41 msec ~24 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #504 [hci0] 173.600260
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #505 [hci0] 173.625249
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #506 [hci0] 173.636065
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #504: len 969 (221 Kb/s)
        Latency: 35 msec (2-249 msec ~30 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 35 msec (7-41 msec ~30 msec)
        #505: len 969 (775 Kb/s)
        Latency: 10 msec (2-249 msec ~20 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 10 msec (7-41 msec ~20 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #507 [hci0] 173.664334
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #508 [hci0] 173.685623
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #509 [hci0] 173.705944
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #507: len 969 (189 Kb/s)
        Latency: 41 msec (2-249 msec ~31 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 41 msec (7-41 msec ~31 msec)
        #508: len 969 (387 Kb/s)
        Latency: 20 msec (2-249 msec ~25 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 20 msec (7-41 msec ~25 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #510 [hci0] 173.706979
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #511 [hci0] 173.728361
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #512 [hci0] 173.749621
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #513 [hci0] 173.751971
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #510: len 969 (176 Kb/s)
        Latency: 44 msec (2-249 msec ~35 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 44 msec (7-44 msec ~35 msec)
        #511: len 969 (337 Kb/s)
        Latency: 23 msec (2-249 msec ~29 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 23 msec (7-44 msec ~29 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #514 [hci0] 173.771077
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #515 [hci0] 173.786061
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #512: len 969 (215 Kb/s)
        Latency: 36 msec (2-249 msec ~32 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 36 msec (7-44 msec ~32 msec)
        #514: len 969 (553 Kb/s)
        Latency: 14 msec (2-249 msec ~23 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 14 msec (7-44 msec ~23 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #516 [hci0] 173.792341
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #517 [hci0] 173.813623
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #518 [hci0] 173.835074
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #519 [hci0] 173.851060
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #516: len 969 (133 Kb/s)
        Latency: 58 msec (2-249 msec ~41 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 58 msec (7-58 msec ~41 msec)
        #517: len 969 (209 Kb/s)
        Latency: 37 msec (2-249 msec ~39 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 37 msec (7-58 msec ~39 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #520 [hci0] 173.856352
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #521 [hci0] 173.866978
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #518: len 969 (250 Kb/s)
        Latency: 31 msec (2-249 msec ~35 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 31 msec (7-58 msec ~35 msec)
        #520: len 969 (775 Kb/s)
        Latency: 10 msec (2-249 msec ~23 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 10 msec (7-58 msec ~23 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #522 [hci0] 173.877659
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #523 [hci0] 173.898958
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #524 [hci0] 173.909938
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #522: len 969 (242 Kb/s)
        Latency: 32 msec (2-249 msec ~27 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 32 msec (7-58 msec ~27 msec)
        #523: len 969 (775 Kb/s)
        Latency: 10 msec (2-249 msec ~19 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 10 msec (7-58 msec ~19 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #525 [hci0] 173.920419
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #526 [hci0] 173.941647
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #527 [hci0] 173.948970
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #525: len 969 (276 Kb/s)
        Latency: 28 msec (2-249 msec ~23 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 28 msec (7-58 msec ~23 msec)
        #526: len 969 (1107 Kb/s)
        Latency: 7 msec (2-249 msec ~15 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 7 msec (7-58 msec ~15 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #528 [hci0] 173.962986
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #529 [hci0] 173.984318
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #530 [hci0] 173.998976
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #528: len 969 (221 Kb/s)
        Latency: 35 msec (2-249 msec ~25 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 35 msec (7-58 msec ~25 msec)
        #529: len 969 (553 Kb/s)
        Latency: 14 msec (2-249 msec ~20 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 14 msec (7-58 msec ~20 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #531 [hci0] 174.005657
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #532 [hci0] 174.026992
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #533 [hci0] 174.048261
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #534 [hci0] 174.048933
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #531: len 969 (180 Kb/s)
        Latency: 43 msec (2-249 msec ~31 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 43 msec (7-58 msec ~31 msec)
        #532: len 969 (369 Kb/s)
        Latency: 21 msec (2-249 msec ~26 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 21 msec (7-58 msec ~26 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #535 [hci0] 174.069669
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #536 [hci0] 174.079934
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #533: len 969 (250 Kb/s)
        Latency: 31 msec (2-249 msec ~29 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 31 msec (7-58 msec ~29 msec)
        #535: len 969 (775 Kb/s)
        Latency: 10 msec (2-249 msec ~19 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 10 msec (7-58 msec ~19 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #537 [hci0] 174.090974
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #538 [hci0] 174.115215
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #539 [hci0] 174.121963
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #537: len 969 (258 Kb/s)
        Latency: 30 msec (2-249 msec ~25 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 30 msec (7-58 msec ~25 msec)
        #538: len 969 (1292 Kb/s)
        Latency: 6 msec (2-249 msec ~16 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 6 msec (6-58 msec ~16 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #540 [hci0] 174.133651
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #541 [hci0] 174.154980
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #542 [hci0] 174.170931
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #540: len 969 (209 Kb/s)
        Latency: 37 msec (2-249 msec ~26 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 37 msec (6-58 msec ~26 msec)
        #541: len 969 (516 Kb/s)
        Latency: 15 msec (2-249 msec ~21 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 15 msec (6-58 msec ~21 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #543 [hci0] 174.176352
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #544 [hci0] 174.197664
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #545 [hci0] 174.205958
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #543: len 969 (267 Kb/s)
        Latency: 29 msec (2-249 msec ~25 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 29 msec (6-58 msec ~25 msec)
        #544: len 969 (969 Kb/s)
        Latency: 8 msec (2-249 msec ~16 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 8 msec (6-58 msec ~16 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #546 [hci0] 174.219073
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #547 [hci0] 174.240261
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #548 [hci0] 174.248961
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #546: len 969 (267 Kb/s)
        Latency: 29 msec (2-249 msec ~23 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 29 msec (6-58 msec ~23 msec)
        #547: len 969 (969 Kb/s)
        Latency: 8 msec (2-249 msec ~16 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 8 msec (6-58 msec ~16 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #549 [hci0] 174.261645
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #550 [hci0] 174.283090
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #551 [hci0] 174.290959
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #549: len 969 (267 Kb/s)
        Latency: 29 msec (2-249 msec ~22 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 29 msec (6-58 msec ~22 msec)
        #550: len 969 (1107 Kb/s)
        Latency: 7 msec (2-249 msec ~15 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 7 msec (6-58 msec ~15 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #552 [hci0] 174.304403
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #553 [hci0] 174.325654
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #554 [hci0] 174.335046
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #552: len 969 (258 Kb/s)
        Latency: 30 msec (2-249 msec ~22 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 30 msec (6-58 msec ~22 msec)
        #553: len 969 (861 Kb/s)
        Latency: 9 msec (2-249 msec ~16 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 9 msec (6-58 msec ~16 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #555 [hci0] 174.346937
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #556 [hci0] 174.368420
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #557 [hci0] 174.382050
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #555: len 969 (221 Kb/s)
        Latency: 35 msec (2-249 msec ~25 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 35 msec (6-58 msec ~25 msec)
        #556: len 969 (596 Kb/s)
        Latency: 13 msec (2-249 msec ~19 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 13 msec (6-58 msec ~19 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #558 [hci0] 174.389674
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
< ACL Data TX: Handle 11 flags 0x00 dlen 19                          #559 [hci0] 174.398689
      Channel: 5637 len 15 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 11
         FCS: 0x3e
        0d 0a 2b 42 43 53 3a 20 32 0d 0a 3e              ..+BCS: 2..>    
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #560 [hci0] 174.410979
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #561 [hci0] 174.423943
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #558: len 969 (228 Kb/s)
        Latency: 34 msec (2-249 msec ~26 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 34 msec (6-58 msec ~26 msec)
        #559: len 19 (6 Kb/s)
        Latency: 25 msec (2-249 msec ~26 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 25 msec (2-42 msec ~25 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 17                          #562 [hci0] 174.428456
      Channel: 65 len 13 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x6b cr 1 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 9
         FCS: 0xe4
        41 54 2b 42 43 53 3d 32 0d e4                    AT+BCS=2..      
< ACL Data TX: Handle 11 flags 0x00 dlen 14                          #563 [hci0] 174.429087
      Channel: 5637 len 10 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 6
         FCS: 0x3e
        0d 0a 4f 4b 0d 0a 3e                             ..OK..>         
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x02 dlen 969       #564 [hci0] 174.432286
      Channel: 6660 len 965 [PSM 25 mode Basic (0x00)] {chan 0}
> HCI Event: Number of Completed Packets (0x13) plen 5               #565 [hci0] 174.433952
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #560: len 969 (352 Kb/s)
        Latency: 22 msec (2-249 msec ~24 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 22 msec (6-58 msec ~24 msec)
        #563: len 14 (28 Kb/s)
        Latency: 4 msec (2-249 msec ~14 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 4 msec (2-42 msec ~15 msec)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 7         #566 [hci0] 174.436072
      Channel: 5894 len 3 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Suspend (0x09) Command (0x00) type 0x00 label 1 nosp 0
        ACP SEID: 1
> HCI Event: Number of Completed Packets (0x13) plen 5               #567 [hci0] 174.440928
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #564: len 969 (969 Kb/s)
        Latency: 8 msec (2-249 msec ~11 msec)
        Channel: 6660 [PSM 25 mode Basic (0x00)] {chan 0}
        Channel Latency: 8 msec (6-58 msec ~16 msec)
        #566: len 7 (14 Kb/s)
        Latency: 4 msec (2-249 msec ~8 msec)
        Channel: 5894 [PSM 25 mode Basic (0x00)] {chan 4}
        Channel Latency: 4 msec (2-156 msec ~44 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 6                           #568 [hci0] 174.445437
      Channel: 66 len 2 [PSM 25 mode Basic (0x00)] {chan 4}
      AVDTP: Suspend (0x09) Response Accept (0x02) type 0x00 label 1 nosp 0
< ACL Data TX: Handle 11 flags 0x00 dlen 19                          #569 [hci0] 174.454533
      Channel: 5637 len 15 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 11
         FCS: 0x3e
        0d 0a 2b 56 47 53 3a 20 36 0d 0a 3e              ..+VGS: 6..>    
< HCI Command: Setup Synchronous Connection (0x01|0x0028) plen 17    #570 [hci0] 174.464774
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Transmit bandwidth: 8000
        Receive bandwidth: 8000
        Max latency: 13
        Setting: 0x0003
          Input Coding: Linear
          Input Data Format: 1's complement
          Input Sample Size: 8-bit
          # of bits padding at MSB: 0
          Air Coding Format: Transparent Data
        Retransmission effort: Optimize for link quality (0x02)
        Packet type: 0x0380
          3-EV3 may not be used
          2-EV5 may not be used
          3-EV5 may not be used
< ACL Data TX: Handle 11 flags 0x00 dlen 22                          #571 [hci0] 174.464843
      Channel: 5637 len 18 [PSM 3 mode Basic (0x00)] {chan 3}
      RFCOMM: Unnumbered Info with Header Check (UIH) (0xef)
         Address: 0x69 cr 0 dlci 0x1a
         Control: 0xef poll/final 0
         Length: 14
         FCS: 0x3e
        0d 0a 2b 43 49 45 56 3a 20 32 2c 31 0d 0a 3e     ..+CIEV: 2,1..> 
> HCI Event: Command Status (0x0f) plen 4                            #572 [hci0] 174.465951
      Setup Synchronous Connection (0x01|0x0028) ncmd 1
        Status: Success (0x00)
> HCI Event: Number of Completed Packets (0x13) plen 5               #573 [hci0] 174.652042
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 1
        #569: len 19 (0 Kb/s)
        Latency: 197 msec (2-249 msec ~102 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 197 msec (2-197 msec ~106 msec)
> HCI Event: Synchronous Connect Complete (0x2c) plen 17             #574 [hci0] 174.912940
        Status: Success (0x00)
        Handle: 6
        Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Link type: eSCO (0x02)
        Transmission interval: 0x0c
        Retransmission window: 0x04
        RX packet length: 60
        TX packet length: 60
        Air mode: Transparent (0x03)
btmon[6677]: @ RAW Open: btmon (privileged) version 2.22                {0x0002} 174.913165
btmon[6677]: @ RAW Close: btmon                                         {0x0002} 174.913167
> HCI Event: Max Slots Change (0x1b) plen 3                          #575 [hci0] 174.941038
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Max slots: 1
> HCI Event: Number of Completed Packets (0x13) plen 5               #576 [hci0] 175.152032
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 1
        #571: len 22 (0 Kb/s)
        Latency: 687 msec (2-687 msec ~395 msec)
        Channel: 5637 [PSM 3 mode Basic (0x00)] {chan 3}
        Channel Latency: 687 msec (2-687 msec ~396 msec)
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #577 [hci0] 175.600507
      L2CAP: Connection Request (0x02) ident 12 len 4
        PSM: 23 (0x0017)
        Source CID: 67
> ACL Data RX: Handle 11 flags 0x02 dlen 16                          #578 [hci0] 175.609317
      L2CAP: Connection Response (0x03) ident 12 len 8
        Destination CID: 6919
        Source CID: 67
        Result: Connection pending (0x0001)
        Status: Authentication pending (0x0001)
> ACL Data RX: Handle 11 flags 0x02 dlen 16                          #579 [hci0] 175.609317
      L2CAP: Connection Response (0x03) ident 12 len 8
        Destination CID: 6919
        Source CID: 67
        Result: Connection successful (0x0000)
        Status: No further information available (0x0000)
< ACL Data TX: Handle 11 flags 0x00 dlen 12                          #580 [hci0] 175.609387
      L2CAP: Configure Request (0x04) ident 13 len 4
        Destination CID: 6919
        Flags: 0x0000
> HCI Event: Number of Completed Packets (0x13) plen 5               #581 [hci0] 175.611928
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #577: len 12 (8 Kb/s)
        Latency: 11 msec (2-687 msec ~203 msec)
        #580: len 12 (48 Kb/s)
        Latency: 2 msec (2-687 msec ~102 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #582 [hci0] 175.617330
      L2CAP: Configure Response (0x05) ident 13 len 10
        Source CID: 67
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 672
> ACL Data RX: Handle 11 flags 0x02 dlen 16                          #583 [hci0] 175.617331
      L2CAP: Configure Request (0x04) ident 67 len 8
        Destination CID: 67
        Flags: 0x0000
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
< ACL Data TX: Handle 11 flags 0x00 dlen 18                          #584 [hci0] 175.617383
      L2CAP: Configure Response (0x05) ident 67 len 10
        Source CID: 6919
        Flags: 0x0000
        Result: Success (0x0000)
        Option: Maximum Transmission Unit (0x01) [mandatory]
          MTU: 1023
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 18        #585 [hci0] 175.617737
      Channel: 6919 len 14 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Command: type 0x00 label 0 PID 0x110e
        AV/C: Status: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: GetCapabilities pt Single len 0x0001
            CapabilityID: 0x03 (EventsID)
> HCI Event: Number of Completed Packets (0x13) plen 5               #586 [hci0] 175.623934
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #584: len 18 (24 Kb/s)
        Latency: 6 msec (2-687 msec ~54 msec)
        #585: len 18 (24 Kb/s)
        Latency: 6 msec (2-687 msec ~30 msec)
        Channel: 6919 [PSM 23 mode Basic (0x00)] {chan 5}
        Channel Latency: 6 msec (6-6 msec ~6 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 18                          #587 [hci0] 175.632288
      Channel: 67 len 14 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Command: type 0x00 label 6 PID 0x110e
        AV/C: Status: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: GetCapabilities pt Single len 0x0001
            CapabilityID: 0x03 (EventsID)
> ACL Data RX: Handle 11 flags 0x02 dlen 22                          #588 [hci0] 175.632289
      Channel: 67 len 18 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Response: type 0x00 label 0 PID 0x110e
        AV/C: Stable: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: GetCapabilities pt Single len 0x0005
            CapabilityID: 0x03 (EventsID)
            CapabilityCount: 0x03
            EventsID: 0x01 (EVENT_PLAYBACK_STATUS_CHANGED)
            EventsID: 0x02 (EVENT_TRACK_CHANGED)
            EventsID: 0x0d (EVENT_VOLUME_CHANGED)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 27        #589 [hci0] 175.632446
      Channel: 6919 len 23 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Response: type 0x00 label 6 PID 0x110e
        AV/C: Stable: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: GetCapabilities pt Single len 0x000a
            CapabilityID: 0x03 (EventsID)
            CapabilityCount: 0x08
            EventsID: 0x01 (EVENT_PLAYBACK_STATUS_CHANGED)
            EventsID: 0x02 (EVENT_TRACK_CHANGED)
            EventsID: 0x03 (EVENT_TRACK_REACHED_END)
            EventsID: 0x04 (EVENT_TRACK_REACHED_START)
            EventsID: 0x08 (EVENT_PLAYER_APPLICATION_SETTING_CHANGED)
            EventsID: 0x0a (EVENT_AVAILABLE_PLAYERS_CHANGED)
            EventsID: 0x0b (EVENT_ADDRESSED_PLAYER_CHANGED)
            EventsID: 0x0d (EVENT_VOLUME_CHANGED)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 22        #590 [hci0] 175.632455
      Channel: 6919 len 18 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Command: type 0x00 label 1 PID 0x110e
        AV/C: Notify: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: RegisterNotification pt Single len 0x0005
            EventID: 0x0d (EVENT_VOLUME_CHANGED)
            Interval: 0x00000000 (0 seconds)
> HCI Event: Number of Completed Packets (0x13) plen 5               #591 [hci0] 175.638915
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 2
        #589: len 27 (36 Kb/s)
        Latency: 6 msec (2-687 msec ~18 msec)
        Channel: 6919 [PSM 23 mode Basic (0x00)] {chan 5}
        Channel Latency: 6 msec (6-6 msec ~6 msec)
        #590: len 22 (29 Kb/s)
        Latency: 6 msec (2-687 msec ~12 msec)
        Channel: 6919 [PSM 23 mode Basic (0x00)] {chan 5}
        Channel Latency: 6 msec (6-6 msec ~6 msec)
> ACL Data RX: Handle 11 flags 0x02 dlen 22                          #592 [hci0] 175.641294
      Channel: 67 len 18 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Command: type 0x00 label 7 PID 0x110e
        AV/C: Notify: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: RegisterNotification pt Single len 0x0005
            EventID: 0x01 (EVENT_PLAYBACK_STATUS_CHANGED)
            Interval: 0x00000000 (0 seconds)
bluetoothd[1272]: < ACL Data TX: Handle 11 flags 0x00 dlen 19        #593 [hci0] 175.641350
      Channel: 6919 len 15 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Response: type 0x00 label 7 PID 0x110e
        AV/C: Interim: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: RegisterNotification pt Single len 0x0002
            EventID: 0x01 (EVENT_PLAYBACK_STATUS_CHANGED)
            PlayStatus: 0x00 (STOPPED)
> ACL Data RX: Handle 11 flags 0x02 dlen 19                          #594 [hci0] 175.644296
      Channel: 67 len 15 [PSM 23 mode Basic (0x00)] {chan 5}
      AVCTP Control: Response: type 0x00 label 1 PID 0x110e
        AV/C: Interim: address 0x48 opcode 0x00
          Subunit: Panel
          Opcode: Vendor Dependent
          Company ID: 0x001958
          AVRCP: RegisterNotification pt Single len 0x0002
            EventID: 0x0d (EVENT_VOLUME_CHANGED)
            Volume: 80.31% (102/127)
> HCI Event: Number of Completed Packets (0x13) plen 5               #595 [hci0] 175.776922
        Num handles: 1
        Handle: 11 Address: 50:57:8A:CA:BE:CB (Apple, Inc.)
        Count: 1
        #593: len 19 (1 Kb/s)
        Latency: 135 msec (2-687 msec ~74 msec)
        Channel: 6919 [PSM 23 mode Basic (0x00)] {chan 5}
        Channel Latency: 135 msec (6-135 msec ~70 msec)