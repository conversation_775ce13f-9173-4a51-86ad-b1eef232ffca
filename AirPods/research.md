# AirPods-Mik<PERSON>fon unter Ubuntu mit Apple Bluetooth Controller

Es gibt mehrere bekannte Probleme bei der Verwendung von AirPods als Aufnahmegerät unter Linux, insbesondere bei Verwendung eines Apple Bluetooth Host Controllers (ID 05ac:828d). Hier sind detaillierte Lösungsansätze:

## Grundlegendes Verständnis der Profile

Die Anzeige "High Fidelity-Wiedergabe (A2DP-Senke, Codec SBC-XQ)" ist korrekt für die Audiowiedergabe mit PipeWire, unterstützt jedoch nicht die Mikrofonfunktion. Dies ist keine Fehlfunktion, sondern eine Einschränkung des A2DP-Profils[2][3].

A2DP (Advanced Audio Distribution Profile) ist nur für Audioausgabe in hoher Qualität konzipiert, während Sie für die Mikrofonnutzung auf das HSP/HFP-Profil (Headset/Handsfree Profile) umschalten müssen[2][5].

## Bluetooth-Controller-Modus anpassen

Eine bekannte Lösung für AirPods unter Linux, insbesondere bei Apple Bluetooth-Controllern:

1. Öff<PERSON> Sie die Konfigurationsdatei:
   ```
   sudo nano /etc/bluetooth/main.conf
   ```

2. Finden Sie die Zeile `#ControllerMode = dual`[1]

3. Ändern Sie diese zu `ControllerMode = bredr` (Entfernen Sie das #-Zeichen)[1]

4. Speichern Sie die Datei (Strg+X, dann Y, dann Enter)[1]

5. Starten Sie den Bluetooth-Dienst neu:
   ```
   sudo /etc/init.d/bluetooth restart
   ```
   oder
   ```
   sudo systemctl restart bluetooth
   ```

## Socket-Schnittstelle deaktivieren

Ein weiterer möglicher Lösungsansatz ist die Deaktivierung der Socket-Schnittstelle:

1. Öffnen Sie erneut `/etc/bluetooth/main.conf`
2. Falls vorhanden, entfernen Sie die Zeile `Enable=Socket`
3. Fügen Sie am Ende der Datei hinzu: `Disable=Socket`
4. Starten Sie Bluetooth neu: `sudo systemctl restart bluetooth`[3]

Hinweis: Diese Einstellung kann in manchen Fällen auch umgekehrt wirken, daher bei Bedarf wieder entfernen.

## AirPods Pro spezifische Probleme

Bei AirPods Pro (insbesondere Generation 1) gibt es bekannte Probleme mit dem HSP/HFP-Profil unter Linux. Auch wenn das Profil auswählbar ist, funktionieren Mikrofon und Audio möglicherweise nicht[2].

Versuchen Sie diese zusätzlichen Einstellungen für die PipeWire-Konfiguration:

1. Bearbeiten Sie die Datei `/etc/pipewire/media-session.d/bluez-monitor.conf` oder `/usr/share/pipewire/media-session.d/bluez-monitor.conf` (abhängig von Ihrer Installation)

2. Finden und aktivieren Sie folgende Einstellungen (entfernen Sie # falls vorhanden):
   ```
   bluez5.msbc-support = true
   bluez5.sbc-xq-support = true
   ```

## Vollständige Bluetooth-Gerätebereinigung

Wenn alle anderen Methoden fehlschlagen:

1. Trennen Sie die AirPods in den Bluetooth-Einstellungen
2. Entfernen Sie alte Bluetooth-Einstellungen:
   ```
   sudo rm -fr /var/lib/bluetooth
   ```
3. Starten Sie Bluetooth neu oder führen Sie einen Systemneustart durch
4. Koppeln Sie die AirPods erneut[6]

## Automatische Profilumschaltung aktivieren

Damit PipeWire automatisch auf das richtige Profil umschaltet:

1. Bearbeiten Sie `/usr/share/pipewire/pipewire-pulse.conf`
2. Entfernen Sie unter `content.exec` das Kommentarzeichen vor der Zeile:
   ```
   { path = "pactl" args = "load-module module-switch-on-connect" }
   ```
3. Starten Sie PipeWire neu oder führen Sie einen Systemneustart durch[6]

## Kernel-Kompatibilität

Aktuelle Linux-Kernel (insbesondere ab 6.4.7) haben verbesserte Unterstützung für Apple Bluetooth Host Controller (05ac:828d). Falls Sie eine ältere Kernel-Version verwenden, könnte ein Kernel-Update Ihr Problem lösen[7].

Die oben genannten Schritte sollten helfen, Ihr Problem zu beheben. Falls das Mikrofon nach diesen Änderungen immer noch nicht funktioniert, überprüfen Sie die Soundeinstellungen, ob dort beide Profile (A2DP und HSP/HFP) zur Auswahl stehen und ob Sie manuell zwischen ihnen wechseln können.

Citations:
[1] https://gist.github.com/aidos-dev/b49078c1d8c6bb1621e4ac199d18213b
[2] https://gitlab.freedesktop.org/pipewire/pipewire/-/issues/2689
[3] https://unix.stackexchange.com/questions/642381/impossible-to-set-a2dp-profile-on-bluetooth-headset-on-ubuntu-20-04
[4] https://linux-hardware.org/?id=usb%3A05ac-828d
[5] https://www.youtube.com/watch?v=3TJDgIveiI8
[6] https://alexandra-zaharia.github.io/posts/fix-disabled-a2dp-profile-for-bluetooth-headset-in-linux/
[7] https://bugzilla.redhat.com/show_bug.cgi?id=2224824
[8] https://linux-hardware.org/?id=usb%3A05ac-828d
[9] https://askubuntu.com/questions/1408647/unable-to-pair-airpods-pro-with-ubuntu-22-04
[10] https://devicehunt.com/view/type/usb/vendor/05AC/device/8286
[11] https://www.reddit.com/r/linux4noobs/comments/105j9it/macbook_pro_bluetooth_adapter_detected_but_not/
[12] https://linux-hardware.org/?probe=fc29a80949
[13] https://unix.stackexchange.com/questions/750785/bluetooth-issues-with-airpods-pro-2-on-debian-12
[14] https://askubuntu.com/questions/863930/bluetooth-headset-cant-set-a2dp-high-fidelity-playback-poor-sound-quality
[15] https://forums.linuxmint.com/viewtopic.php?t=399249
[16] https://askubuntu.com/questions/1242565/pairing-apple-airpods-pro-as-headset-on-ubuntu-20-04
[17] https://bugzilla.redhat.com/show_bug.cgi?id=2224824
[18] https://discussions.apple.com/thread/251159387
[19] https://discussion.fedoraproject.org/t/airpods-pro-gen-1-not-working-under-hsp-hfp-codec/134415
[20] https://bbs.archlinux.org/viewtopic.php?id=270465
[21] https://linux-hardware.ru/?probe=495a705c9e&upgrade
[22] https://forums.linuxmint.com/viewtopic.php?t=438437
[23] https://linux-hardware.org/?probe=5b39420471
[24] https://forums.linuxmint.com/viewtopic.php?t=442268
[25] https://forum.garudalinux.org/t/can-not-install-appropriate-driver-for-rtx4080/27530
[26] https://forums.linuxmint.com/viewtopic.php?t=411553
[27] https://forums.jangafx.com/t/embergen-crashes-on-launch-on-linux/2293
[28] https://discussions.apple.com/thread/6812673
[29] https://www.tonymacx86.com/threads/broadcom-wifi-bluetooth-guide.242423/page-51
[30] https://www.insanelymac.com/forum/topic/328690-bcm943602cs-and-bt-module-20703a1-windows-10/
[31] http://www.linux-usb.org/usb.ids
[32] https://discussion.fedoraproject.org/t/fedora-does-not-turn-off-it-stays-on-logo-for-ever/106210
[33] https://www.reddit.com/r/linuxquestions/comments/myyib1/hsphfp_low_quality_audio_on_bluetooth_headphones/
[34] https://forum.manjaro.org/t/manjaro-kde-kernel-5-10-adjust-brightness-on-2015-imac/65732
[35] https://wiki.debian.org/InstallingDebianOn/Apple/MacBookPro/5-5
[36] https://bbs.archlinux.org/viewtopic.php?id=275337
[37] https://forum.manjaro.org/t/gnome-seems-to-crash-taking-to-much-processes-with-it/154973
[38] https://bbs.archlinux.org/viewtopic.php?id=287022

---
Antwort von Perplexity: pplx.ai/share