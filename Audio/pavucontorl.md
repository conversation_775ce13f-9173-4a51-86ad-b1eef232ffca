pavucont<PERSON>
Dieser Artikel wurde für die folgenden Ubuntu-Versionen getestet:

Ubuntu 24.04 Noble Numbat

Ubuntu 22.04 Jammy Jellyfish

Ubuntu 20.04 Focal Fossa

Du möchtest den Artikel für eine weitere Ubuntu-Version testen? Mitarbeit im Wiki ist immer willkommen! Dazu sind die Hinweise zum Testen von Artikeln zu beachten.


Zum Verständnis dieses Artikels sind folgende Seiten hilfreich:
⚓︎Installation von Programmen

⚓︎ein Programm starten

Inhaltsverzeichnis
Installation
Benutzung
Reiter
Links
./pulseaudio_logo.pngDas Programm PulseAudio Volume Control 🇬🇧 oder kurz pavucontrol ist das wichtigste Werkzeug für die Soundkontrolle des Soundservers PulseAudio. Es arbeitet programm-orientiert, stellt also Lautstärkeregler für Anwendungen bereit, die entweder eine permanente Verbindung zu PulseAudio etablieren oder sich nur bei akuten Klangereignissen verbinden. Zudem stellt es einen Master-Lautstärkeregler bereit. Es kann auch für den Soundserver PipeWire eingesetzt werden.

Installation
Pavucontrol ist bei den meisten offiziellen Ubuntu-Varianten vorinstalliert. Ansonsten kann das folgende Paket installiert werden [1]:

pavucontrol (universe)

Befehl zum Installieren der Pakete:

sudo apt-get install pavucontrol 
Zusätzlich nur für den Soundserver PipeWire:

pipewire-pulse (universe, ab 24.04 main )

Befehl zum Installieren der Pakete:

sudo apt-get install pipewire-pulse 
Benutzung
./pavucontrol_trusty.pngAnschließend findet man es bei Ubuntu-Varianten mit einem Anwendungsmenü unter "Multimedia → PulseAudio-Lautstärkeregler"

PulseAudio Volume Control kann

über das Startmenü mit PulseAudio-Lautstärkeregler

oder den Terminalbefehl


pavucontrol  
gestartet werden[2].

Reiter
Hinweis:
In "Wiedergabe", "Ausgabegeräte", "Eingabegeräte" auf "Stumm" (Checkbox rechts) prüfen.

"Default" Aus- und Eingabegerät beachen. Reiter "Ausgabegeräte" und "Eingabegeräte".

Wiedergabe	Hier finden sich die derzeit Anwendung bezogenen laufenden Soundausgaben. Sowohl Pegel(100%) als auch Zuweisung wohin ausgegeben werden soll(z.B. bei mehreren Soundkarten), sollten möglichst auf den Defaultwerten belassen bleiben. Max. Pegel wird ggf. die die Anw. beeinflusst. Pegelanzeige der laufenden Anwendung sollte ausschlagen, ansonsten Kontrolle der Soundeinstellung in der jeweiligen Anwendung selbst.
Aufnahme	Zeigt welche Anwendung momentan aufnimmt.
Ausgabegeräte	Hier Lautstärke regeln. Es erscheinen die Geräte, über die Sound ausgegeben werden kann. Es kann die Ausgabe-Lautstärke jedes Streams individuell geregelt werden. Port: Hier kann kann individuell ein Ausgabemodul gewählt werden, sofern mehrere Soundgeräte verfügbar sind. Festlegung des momentanen Default Ausgabegerätes rechts Checkbox "Als Ausweichoption setzen" Betreffende Pegelanzeige sollte dann Ausschlag zeigen, wenn nicht, Kontrolle im Reiter Wiedergabe.
Eingabegeräte	Mikrofone, etc. Festlegung des momentanen Default Eingabegerätes rechts Checkbox "Als Ausweichoption setzen"
Konfiguration	Alle erkannten Soundkarten und deren Konfiguration. Falls dort nichts (oder nicht alle Karten) aufgeführt sind, hilft ggf. die Problembeschreibung Geräte werden nicht erkannt im PulseAudio Artikel weiter bzw. Systeminformationen Sound kann herangezogen werden.