Ich suche nach Lösungen für die vollständige Nutzung Ihrer M-Audio 192|14 Soundkarte unter Ubuntu 24.04.2 LTS.

Basierend auf meiner Suche habe ich umfassende Informationen zu den Lösungen für Ihre M-Audio AIR 192|14 Soundkarte unter Ubuntu 24.04.2 LTS gefunden. Hier sind die wichtigsten Erkenntnisse:

## **Hauptproblem und Lösungen**

Das Problem, das Sie beschreiben - dass die Soundkarte zwar erkannt wird, aber nur als einfacher Input/Output ohne vollständige Kanalverwaltung - ist typisch für USB-Audio-Interfaces unter Linux, die über die grundlegende ALSA-Unterstützung hinaus erweiterte Steuerung benötigen.

## **Empfohlene Lösungen:**

### **1. JACK Audio mit Studio Controls (Beste Lösung)**

Die M-Audio AIR 192|14 ist "class compliant" und funktioniert out-of-the-box mit Linux. Für die vollständige Nutzung aller 8 Eingänge und 4 Ausgänge empfehle ich:

**Installation:**
```bash
sudo apt install ubuntustudio-controls jackd2 qjackctl
```

**Konfiguration:**
- Öffnen Sie "Studio Controls" (Ubuntu Studio Controls) für die einfachste JACK-Konfiguration
- Aktivieren Sie "Real Time Permissions" und loggen Sie sich aus/ein
- Stellen Sie CPU Governor auf "Performance" für niedrige Latenz
- Konfigurieren Sie JACK für Ihr Interface mit angemessener Puffergröße

### **2. Multichannel-Routing Tools**

Für die Verwaltung einzelner Kanäle und Routing benötigen Sie:

**QJackCtl:**
```bash
sudo apt install qjackctl
```
- Bietet graphische Patchbay für flexible Audio-Routing zwischen Anwendungen
- Ermöglicht Verbindung einzelner Kanäle zu verschiedenen Anwendungen

**Carla Patchbay:**
```bash
sudo apt install carla
```
- Virtueller Audio-Rack und Patchbay für Plugin-Hosting
- Erweiterte Routing-Funktionen

### **3. Spezielle Mixer-Tools (falls nötig)**

Obwohl mudita24/envy24control für ice1712-basierte Karten (wie M-Audio Delta-Serie) entwickelt wurden, funktioniert die AIR 192|14 als class-compliant USB-Device anders. Dennoch können Sie folgende Tools installieren:

```bash
sudo apt install alsa-tools-gui mudita24
```

### **4. Detaillierte Setup-Schritte:**

1. **JACK-Setup:**
   - Sample Rate auf 44100 oder 48000 Hz setzen
   - Buffer Size experimentell für beste Latenz ohne Xruns anpassen
   - Für USB-Geräte JACK Periods auf 3 setzen

2. **PulseAudio-JACK Bridge:**
   - Ermöglicht Desktop-Audio über JACK zu laufen
   - Wird automatisch von Studio Controls konfiguriert

3. **Multichannel-Zugriff:**
   - JACK ermöglicht es, spezifische Kanäle verschiedenen Anwendungen zuzuweisen
   - Jeder Kanal kann als separater Ein-/Ausgang behandelt werden

## **Praktische Empfehlung:**

1. Installieren Sie zuerst Ubuntu Studio Controls und JACK
2. Konfigurieren Sie JACK für Ihr Interface
3. Verwenden Sie QJackCtl's Connections-Dialog für Channel-Routing
4. Ein Nutzer bestätigt: "I currently have the M-audio air 192/14 and it is really good value for money"

Diese Lösung sollte Ihnen vollständigen Zugriff auf alle 8 Eingänge und 4 Ausgänge Ihrer M-Audio AIR 192|14 geben, mit individueller Kanalsteuerung und professioneller Audio-Routing-Flexibilität unter Ubuntu 24.04.2 LTS.