From the libinput wiki page:

libinput is a library to handle input devices in Wayland compositors and to provide a generic X.Org input driver. It provides device detection, device handling, input device event processing and abstraction to minimize the amount of custom input code compositors need to provide the common set of functionality that users expect.
The X.Org input driver supports most regular input devices. Particularly notable is the project's goal to provide advanced support for touch (multitouch and gesture) features of touchpads and touchscreens. See the libinput documentation for more information.

Installation
If you installed either Xorg or Wayland, then Libinput should already be installed as a dependency; there are no necessary extra packages.

For Wayland, the libinput package should be installed as a dependency of any graphical environment you use that has Wayland, and no additional driver is needed.
For Xorg, xf86-input-libinput is also a dependency. It is "a thin wrapper around libinput and allows for libinput to be used for input devices in X. This driver can be used as as drop-in replacement for evdev and synaptics." [1]. It replaces other packages used for input with X (i.e., those prefixed with xf86-input-).
You may want to install xorg-xinput to be able to change settings at runtime.
Configuration
For Wayland, there is no libinput configuration file. The configurable options depend on the progress of your desktop environment's support for them (see #Graphical tools) or by applying desktop-agnostic udev rules (see Calibrating Touchscreen#Do it automatically via a udev rule and #Via Udev Rule). To configure options that your desktop environment does not yet support (e.g. touchpad scroll speed on GNOME), libinput-config-gitAUR may be used as a work-around. Available options for that tool are documented in the libinput-config README.

For Xorg, a default configuration file for the wrapper is installed to /usr/share/X11/xorg.conf.d/40-libinput.conf. No extra configuration is necessary for it to autodetect keyboards, touchpads, trackpointers and supported touchscreens.

Via xinput on Xorg
First, execute:

# libinput list-devices
It will output the devices on the system and their respective features supported by libinput.

After a restart of the graphical environment, the devices should be managed by libinput with default configuration, if no other drivers are configured to take precedence.

See libinput(4) for general options to set and information about allowable values. The xinput tool is used to view or change options available for a particular device at runtime. For example:

$ xinput list
to view all devices and determine their names and numbers. In the following, device is either the name or number identifying the device to operate with.

$ xinput list-props device
to view and

$ xinput set-prop device option setting
to change a setting. option can be either the number or the name of the option. For example, to set both options of libinput Click Method Enabled (303), either of the following can be issued:

$ xinput set-prop 14 303 1 1
or

$ xinput set-prop 14 "libinput Click Method Enabled" 1 1
Via Xorg configuration file
See Xorg#Using .conf files for permanent option settings. Logitech Marble Mouse#libinput and #Tapping button re-mapping illustrate examples.

Alternative drivers for Xorg#Input devices can generally be installed in parallel. If you intend to switch driver for a device to use libinput, ensure no legacy configuration files /etc/X11/xorg.conf.d/ for other drivers take precedence.

Tip:
If you have libinput and synaptics installed in parallel with default configuration (i.e. no files in /etc/X11/xorg.conf.d for either), synaptics will take precedence due to its higher numeric order 70- in the default installation directory. To avoid this, you can symlink the default libinput configuration (40-libinput.conf) to /etc/X11/xorg.conf.d/ where directory search order precedence over 70-synaptics.conf will take place instead:
# ln -s /usr/share/X11/xorg.conf.d/40-libinput.conf /etc/X11/xorg.conf.d/40-libinput.conf
If you do have /etc/X11/xorg.conf.d/ configuration files for both, the libinput file must be ordered second; see Xorg#Using .conf files. If you want to disable libinput (and fallback to older drivers) - just remove the previously created symbolic link from /etc/X11/xorg.conf.d/.
If a configuration file seems to have no effect at all, check that it is readable by unprivileged users.
One way to check which devices are managed by libinput is the xorg logfile. For example, the following:

$ grep -e "Using input driver 'libinput'" /path/to/Xorg.0.log
[    28.799] (II) Using input driver 'libinput' for 'Power Button'
[    28.847] (II) Using input driver 'libinput' for 'Video Bus'
[    28.853] (II) Using input driver 'libinput' for 'Power Button'
[    28.860] (II) Using input driver 'libinput' for 'Sleep Button'
[    28.872] (II) Using input driver 'libinput' for 'AT Translated Set 2 keyboard'
[    28.878] (II) Using input driver 'libinput' for 'SynPS/2 Synaptics TouchPad'
[    28.886] (II) Using input driver 'libinput' for 'TPPS/2 IBM TrackPoint'
[    28.895] (II) Using input driver 'libinput' for 'ThinkPad Extra Buttons'
is a notebook without any configuration files in /etc/X11/xorg.conf.d/, i.e. devices are auto-detected.

Of course you can elect to use an alternative driver for one device and libinput for others. A number of factors may influence which driver to use. For example, in comparison to Touchpad Synaptics the libinput driver has fewer options to customize touchpad behaviour to one's own taste, but far more programmatic logic to process multitouch events (e.g. palm detection as well). Hence, it makes sense to try the alternative, if you are experiencing problems on your hardware with one driver or the other.

Custom configuration files should be placed in /etc/X11/xorg.conf.d/ and following a widely used naming schema 30-touchpad.conf is often chosen as filename.

Tip: Have a look at CONFIGURATION DETAILS in /usr/share/X11/xorg.conf.d/40-libinput.conf for guidance and refer to the libinput(4) manual page for a detailed description of available configuration options.
A basic configuration should have the following structure:

/etc/X11/xorg.conf.d/30-touchpad.conf
Section "InputClass"
    Identifier "devname"
    Driver "libinput"
    ...
EndSection
You may define as many sections as you like in a single configuration file (usually one per input device). To configure the device of your choice specify a filter by using one of the available filters from xorg.conf(5) § INPUTCLASS_SECTION, e.g.

MatchIsPointer "on" (trackpoint)
MatchIsKeyboard "on"
MatchIsTouchpad "on"
MatchIsTouchscreen "on"
The input device can then be configured with any of the lines of libinput(4) § CONFIGURATION_DETAILS. Common options include:

Option "Tapping" "on": tapping a.k.a. tap-to-click
Option "ClickMethod" "clickfinger": trackpad no longer has middle and right button areas and instead two-finger click is a context click and three-finger click is a middle click, see the docs.
Option "NaturalScrolling" "true": natural (reverse) scrolling
Option "ScrollMethod" "edge": edge (vertical) scrolling
Bear in mind that some of them may only apply to certain devices and you will need to restart X for changes to take effect.

Via Udev Rule
This article or section needs language, wiki syntax or style improvements. See Help:Style for reference.

Reason: This does not look like a general configuration method, more than just LIBINPUT_CALIBRATION_MATRIX can be actually set this way. (Discuss in Talk:Libinput)
The main udev and Calibrating Touchscreen articles have more details, but to summarize:

# libinput list-devices
Take note of the Device: name and Kernel path for the relevant input device. For example this might be HID 02eb:114e at path /dev/input/event0.

Create a rule for that device. E.g. to rotate all touchscreen input by 270 degrees:

/etc/udev/rules.d/99-mytouchscreen-calibration.rules
# Do a 270 degree rotation on the input
ENV{ID_USB_SERIAL}=="02eb_114e", ENV{LIBINPUT_CALIBRATION_MATRIX}="0 1 0 -1 0 1"
Tip: Have a look at Static Device Configuration Via Udev heading at the Libinput docs for other coordinate transformations.
Then, reload the rules and test to see if your device has registered the rule:

# udevadm info /dev/input/event0
You should see your LIBINPUT_CALIBRATION_MATRIX listed there for the device.

Finally, either restart your machine or restart your Wayland desktop.

Graphical tools
There are different GUI tools:

GNOME:
Control center has a basic UI. See GNOME#Mouse and touchpad.
gnome-tweaks offers some additional settings.
Cinnamon:
Similar to the GNOME UI, with more options.
MATE:
Mouse option available in the settings panel. Also accessible using mate-mouse-properties
KDE Plasma:
Keyboard, mouse and controller devices can be configured from System Settings.
Xfce:
Configured from the Mouse and Touchpad submenu in xfce4-settings.
Tips and tricks
Tapping button re-mapping
Swapping two- and three-finger tap for a touchpad is a straight forward example. Instead of the default three-finger tap for pasting, you can configure two-finger tap pasting by setting the TappingButtonMap option in your Xorg configuration file. To set 1/2/3-finger taps to left/right/middle, set TappingButtonMap to lrm, for left/middle/right set it to lmr.

/etc/X11/xorg.conf.d/30-touchpad.conf
Section "InputClass"
    Identifier "touchpad"
    Driver "libinput"
    MatchIsTouchpad "on"
    Option "Tapping" "on"
    Option "TappingButtonMap" "lmr"
EndSection
Remember to remove MatchIsTouchpad "on" if your device is not a touchpad and adjust the Identifier accordingly.

Manual button re-mapping
For some devices, it is desirable to change the button mapping. A common example is the use of a thumb button instead of the middle button (used in X11 for pasting) on mice where the middle button is part of the mouse wheel. You can query the current button mapping via:

$ xinput get-button-map device
where device is either the device name or the device ID, as returned by xinput list. You can freely permutate the button numbers and write them back. Example:

$ xinput set-button-map device 1 6 3 4 5 0 7
In this example, we mapped button 6 to be the middle button and disabled the original middle button by assigning it to button 0. For more information, please read about "ButtonMapping" section in libinput(4). This may also be used for Wayland, but be aware both the device number and its button-map will be different. Hence, settings are not directly interchangeable.

Tip: You can use xev (from the xorg-xev package) to find out which physical button is currently mapped to which ID.
This article or section needs language, wiki syntax or style improvements. See Help:Style for reference.

Reason: The rest of this section is overly complicated with parameters for different devices. Unique ideas should be split into subsections to make it more readable. (Discuss in Talk:Libinput)
Some devices occur several times under the same device name, with a different amount of buttons exposed. The following is an example for reliably changing the button mapping for a Logitech Revolution MX mouse via xinitrc:

~/.xinitrc
...
for i in $(xinput list | grep "Logitech USB Receiver" | perl -n -e'/id=(\d+)/ && print "$1\n"')
	do if xinput get-button-map "$i" 2>/dev/null| grep -q 20; then
		xinput set-button-map "$i" 1 17 3 4 5 8 7 6 9 10 11 12 13 14 15 16 2 18 19 20
	fi
done
...
You could also use the Xorg configuration file to do that. The trackball used in this example has a physical scroll wheel, devices without one may need to refer the configuration for Logitech Marble Mouse. The physical buttons in Kensington Slimblade Trackball layout are:

-----------
|2(M)|8(S)|
-----------
|1(L)|3(R)|
-----------
So, for the left hand user, you may use the configuration below. Although the device has no scroll up and scroll down buttons, you cannot disable them in the configuration or some application will not response to the action of the wheel.

-----------
|2(N)|8(M)|
-----------
|1(R)|3(L)|
-----------
/etc/X11/xorg.conf
 Section "InputClass"
     Identifier   "Trackball"
     MatchProduct "Kensington Kensington Slimblade Trackball"
     MatchProduct "Trackball"
     Driver       "libinput"
     Option       "ButtonMapping"      "3 0 1 4 5 6 7 8 2"
 EndSection
Change touchpad sensitivity
The method of finding the correct thresholds for when libinput registers a touch as DOWN and back UP again can be found [2] in the upstream documentation.

Custom touchpad pressure values can be set via temporary local device quirks. See [3].

Note: Quirks are an internal API and are not guaranteed to work in future libinput versions. Between versions 1.11 and 1.12, udev rules [4] were replaced by .quirk files [5].
Disable device
Using environment variable
The LIBINPUT_IGNORE_DEVICE environment variable can be used to prevent initialization of a specific device. [6] This is best set using a udev rule:

/etc/udev/rules.d/99-libinput-ignore.rules
ACTION=="add|change", device_delineation, ENV{LIBINPUT_IGNORE_DEVICE}="1"
where device_delineation delineates a specific device using udev syntax. For example, for a whole USB device you would like libinput to ignore, you could use SUBSYSTEMS=="usb", ATTRS{idVendor}=="vendor_id", ATTRS{idProduct}=="product_id" using the IDs from lsusb.

Once the file is created, udev will automatically pick up on the change (see Udev#Loading new rules); you just need to reconnect the device for the change to take place.

Using xinput
To disable a device such as a touchpad, first get its name with xinput list and then disable it with xinput disable name.

Note:
It is more robust to disable it by name than by ID number. The devices may be renumbered.
It will be necessary to quote the name if it contains spaces.
To make it permanent, see Autostarting.

To toggle, write a script such as [7].

Gestures
While the libinput driver already contains logic to process advanced multitouch events like swipe and pinch gestures, the Desktop environment or Window manager might not have implemented actions for all of them yet.

libinput-gestures
For EWMH (see also wm-spec) compliant window managers, the libinput-gestures utility can be used meanwhile. The program reads libinput gestures (through libinput debug-events) from the touchpad and maps them to gestures according to a configuration file. Hence, it offers some flexibility within the boundaries of libinput's built-in recognition.

To use it, install the libinput-gesturesAUR package.

libinput-gestures needs access to the touchpad device. Traditionally, you can set this by adding yourself to the INPUT group, but the more modern and safer approach is to manage access dynamically using udev, logind and acls. For this to work, create a file:

/etc/udev/rules.d/71-touchpad.rules
ENV{ID_INPUT_TOUCHPAD}=="1", TAG+="uaccess"
The number at the beginning of the file is important: at index 70, the device property ENV{ID_INPUT_TOUCHPAD might be unset, and the uaccess tag needs to be added before index 73 for systemd-logind(8).

You can use the default system-wide configured swipe and pinch gestures or define your own in a personal configuration file, see the README for details.

If using touchegg, uninstall the libinput-gesturesAUR package to prevent conflicts (see [8]).

fusuma
Fusuma is a multitouch gesture recognizer, written in Ruby, which can be used as an alternative to libinput-gestures.

Install the fusuma Ruby gem:

$ gem install fusuma
Alternatively, there is also ruby-fusumaAUR.

Other than the fusuma Ruby gem you have to install the fusuma-plugin-sendkey Ruby gem or one between the xdotool (for X) and ydotool (generic: Wayland, X11, etc). Other alternatives are listed here.

Tip: The fusuma-plugin-sendkey Ruby gem supports both X and Wayland
Then in ~/.config/fusuma/config.yml you have to set something like:

~/.config/fusuma/config.yml
swipe:
  3: 
    left: 
      sendkey: 'LEFTALT+RIGHT'
    right: 
      sendkey: 'LEFTALT+LEFT'
    up: 
      sendkey: 'LEFTALT+LEFTSHIFT+TAB'
    down: 
      sendkey: 'LEFTALT+TAB'
pinch:
  in:
    sendkey: 'LEFTALT+LEFTSHIFT+TAB'
  out:
    sendkey: 'LEFTALT+TAB'

threshold:
  swipe: 0.5
  pinch: 0.2

interval:
  swipe: 0.2
  pinch: 0.2
Or for xdotool:

~/.config/fusuma/config.yml
swipe:
  3: 
    left: 
      command: 'xdotool key alt+Right'
    right: 
      command: 'xdotool key alt+Left'
    up: 
      command: 'xdotool key ctrl+shift+plus'
    down: 
      command: 'xdotool key ctrl+minus'
pinch:
  in:
    command: 'xdotool key ctrl+shift+plus'
  out:
    command: 'xdotool key ctrl+minus'

threshold:
  swipe: 0.5
  pinch: 0.2

interval:
  swipe: 0.2
  pinch: 0.2
Same thing for ydotool.

The swipe threshold is important for not swiping back too many pages.

Notice that the configure is for three fingers swipe. Two fingers swipe is not supported [9].

Gebaar
Gebaar is another gesture recognizer. Unlike Fusuma, it does not support pinching (support is planned in the future though) and thresholds, but in addition to swiping left, right, up and down with 3/4 fingers, it also supports diagonal swipes, which Fusuma does not.

There is a fork of gebaar at Gebaar which could be installed through gebaar-libinput-gitAUR which supports pinch gestures and adds additional features to original gebaar. Take in mind that this version is currently under active development and introduces configuration changes which makes it incompatable to original Gebaar

GnomeExtendedGestures
For deeper integration with GNOME, there is GnomeExtendedGestures (gnome-shell-extension-extended-gestures-gitAUR). Three finger horizontal and vertical gestures can be configured to perform gnome-shell actions (such as toggling the application overview or cycling between them).

Scroll with mouse by holding a button
There is a nice trick to optimize scrolling with a mouse or trackball by holding a mouse button (like right or middle button, or some other if the mouse has more buttons) and moving the mouse. Very useful in case your mouse does not have the mouse wheel (often the case with the trackballs). To do that one has to set ScrollMethod to button and specify the mouse button in the ScrollButton option for the action. Here is an example for configuration to achieve that:

/etc/X11/xorg.conf.d/00-mouse.conf
Section "InputClass"
    Identifier "system-mouse"
    MatchIsPointer "on"
    Option "ScrollMethod" "button"
    Option "ScrollButton" "3"
EndSection
Mouse wheel scrolling speed scaling
For some mice, especially when using on a HiDPI desktop, the wheel scrolls too slow. A patch is submitted to libinput but it has not been accepted. There is a third-party xf86-input-libinput that incorporates this patch.

This patch introduces a new property libinput Scroll Distance Scale to mice, and you can set a scaling factor like

$ xinput --set-prop 'device_name' 'libinput Scroll Distance Scale' 2.5 2.5
where the device_name is the name of your mouse device, listed in xinput --list. 2.5 2.5 are the scaling factors, for x- and y-axis, respectively.

Alternatively, install libinput-multiplierAUR and restart Xorg, then enlarge y-axis scroll distance to 6 times by

$ echo 6 > /tmp/libinput_discrete_deltay_multiplier
Here is an example to modify the scaling factor upon focusing change

Enable the touchpad while typing
By default, libinput disables the mousepad when typing. This is conflicting for some software such as Inkscape which has keybindings that require mouse movement while a key is pressed. You can disable this by adding the following line to the InputClass section of /etc/X11/xorg.conf.d/30-touchpad.conf:

Section "InputClass"
    ...
    Option "DisableWhileTyping" "0"
EndSection
Troubleshooting
First, see whether executing libinput debug-events can support you in debugging the problem, see libinput-debug-events(1) for options.

Some inputs require kernel support. The tool evemu-describe from the evemu package can be used to check:

Compare its output (for example) with a supported trackpad. i.e. a couple of ABS_ axes, a couple of ABS_MT axes and no REL_X/Y axis. For a clickpad the INPUT_PROP_BUTTONPAD property should also be set, if it is supported.

Touchpad not working in GNOME
Ensure the touchpad events are being sent to the GNOME desktop by running the following command:

$ gsettings set org.gnome.desktop.peripherals.touchpad send-events enabled
Additionally, GNOME may override certain behaviors, like turning off Tapping and forcing Natural Scrolling. In this case the settings must be adapted using GNOMEs gsettings command line tool or a graphical frontend of your choice. For example if you wish to enable Tapping and disable Natural Scrolling for your user, adjust the touchpad key-values like the following:

$ gsettings set org.gnome.desktop.peripherals.touchpad tap-to-click true
$ gsettings set org.gnome.desktop.peripherals.touchpad natural-scroll false
Inertial scrolling does not work in KDE
The feature is currently not implemented, see KDE bug 456383. As a workaround for Chromium-based browsers, install the SmoothScroll extension.

Keys stuck after entering tablet mode
On some Tablet PCs (notably Lenovo Yogas), holding a keyboard key while entering the tablet mode can cause the key be stuck until the tablet mode is disabled. It is sometimes possible to fix this behavior by modifying libinput quirks files. See Issue 914.

For example, find the name of the keyboard device:

# libinput list-devices
...
Device:           AT Translated Set 2 keyboard
Kernel:           /dev/input/event3
Capabilities:     keyboard 
...
Then, create an override file:

/etc/libinput/local-overrides.quirks
[Lenovo Thinkpad Yoga]
MatchName=AT Translated Set 2 keyboard
ModelTabletModeNoSuspend=0
ModelTabletModeNoSuspend=0 disables behavior that causes the bug. Consult Device quirks for information about configuration format and Match directives that select the device to configure. It is often possible to create an override file based on an existing quirks entry for your particular device. Default quirks files can be found in /usr/share/libinput/.