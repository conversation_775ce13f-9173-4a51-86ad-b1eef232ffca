Ghostty Ubuntu
This repository contains build scripts to produce an unofficial Ubuntu/Debian package (.deb) for Ghostty.

This is an unofficial community project to provide a package that's easy to install on Ubuntu. If you're looking for the Ghostty source code, see ghostty-org/ghostty.

Install/Update
⚡ Just paste this into your terminal and run it!

/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/mkasberg/ghostty-ubuntu/HEAD/install.sh)"
Warning

A recent GTK is required for <PERSON><PERSON> to work with Nvidia (GL) drivers under X11. Ubuntu 22.04 LTS has GTK 4.6 which is not new enough. Ubuntu 23.10+ should be fine. (See the note in the Ghostty docs.)

Manual Installation
If you prefer to download and install the package manually instead of running the short script above, here are instructions.

Download the .deb package for your Ubuntu version. (Also available on our Releases page.)

Ubuntu 24.10 Oracular: ghostty_1.1.2-0.ppa1_amd64_24.10.deb
Ubuntu 24.04 LTS Noble: ghostty_1.1.2-0.ppa1_amd64_24.04.deb
Ubuntu 22.04 LTS Jammy: ghostty_1.1.2-0.ppa1_amd64_22.04.deb
Debian Bookworm: ghostty_1.1.2-0.ppa1_amd64_bookworm.deb
Arm64 Ubuntu 24.10 Oracular: ghostty_1.1.2-0.ppa1_arm64_24.10.deb
Arm64 Ubuntu 24.04 LTS Noble: ghostty_1.1.2-0.ppa1_arm64_24.04.deb
Arm64 Ubuntu 22.04 LTS Jammy: ghostty_1.1.2-0.ppa1_arm64_22.04.deb
Arm64 Debian Bookworm: ghostty_1.1.2-0.ppa1_arm64_bookworm.deb
Install the downloaded .deb package.

sudo dpkg -i <filename>.deb
Updating
To update to a new version, just follow any of the installation methods above. There's no need to uninstall the old version; it will be updated correctly.

Contributing
I want to have an easy-to-install Ghostty package for Ubuntu, so I'm doing what I can to make it happen. (Ghostty relies on the community to produce non-macOS packages.) I'm sure the scripts I have so far can be improved, so please open an issue or PR if you notice any problems!

GitHub Actions will run CI on each PR to test that we can produce a build.

If you want to test locally, our current approach uses Docker for a build environment. The details of how the process works are in build.yml, but at a high level you can build the docker image

docker build -t ghostty-ubuntu:latest --build-arg DISTRO=ubuntu --build-arg DISTRO_VERSION=24.10 .
And then use that build environment to produce a binary .deb package

docker run --rm -v$PWD:/workspace -w /workspace ghostty-ubuntu:latest /bin/bash build-ghostty.sh
Alternatively, you can try running build-ghostty.sh on your own system, but you'll have to have all the build dependencies installed as in the Dockerfile.