Terminal API (VT)
APIs for developers writing applications that run in Ghostty.

Programs running in terminals use control sequences to interact with the terminal. Control sequences are a series of special characters that the terminal interprets as commands. This is sometimes referred as VT sequences or escape codes.

For a full list of supported sequences, see the reference. For a conceptual overview of control sequences, see the concepts page. The current page will give an overview of Ghostty's control sequence support.

