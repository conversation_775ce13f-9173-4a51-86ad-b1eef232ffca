VT Sequence Reference
A reference of all VT sequences supported by <PERSON><PERSON>.

This page lists many of the VT sequences that <PERSON><PERSON> supports.

This page is a work-in-progress. <PERSON>ty supports many more sequences than are listed here and for the sequences listed here the quality of the linked documentation varies. This is a very contributor friendly area to help improve the documentation!

They are currently grouped by sequence type (control, esc, CSI, etc.) and the listed alphabetically by syntax. In the future, we will introduce better organization and search capabilities.

Name	Syntax	Description
BEL	0x07	Alert the user (beep)
BS	0x08	Move cursor backward one position
TAB	0x09	Move cursor right to the next tab stop
LF	0x0A	Move cursor down one line, scrolling if necessary
CR	0x0D	Move cursor to the left margin
DECSC	ESC 7	Save cursor
DECRC	ESC 8	Restore cursor
IND	ESC D	Move cursor down, scrolling if necessary
RI	ESC M	Move cursor up, scrolling if necessary
RIS	ESC c	Full reset
DECSCUSR	ESC Pn " " q	Set cursor style
DECKPAM	ESC =	Set numeric keypad to application mode
DECKPNM	ESC >	Set numeric keypad to numeric mode
DECALN	ESC # 8	Screen alignment test
CUU	CSI Pn A	Move cursor up
CUD	CSI Pn B	Move cursor down
CUF	CSI Pn C	Move cursor right
CUB	CSI Pn D	Move cursor left
CNL	CSI Pn E	Move cursor down n lines and to the leftmost column
CPL	CSI Pn F	Move cursor up n lines and to the leftmost column
CUP	CSI Py ; Px H	Move cursor to the specified row and column
CHT	CSI Pn I	Move cursor right n tabs
ED	CSI Pn J	Erase display
EL	CSI Pn K	Erase line
DL	CSI Pn M	Delete n lines at the cursor
IL	CSI Pn L	Insert n lines at the cursor
DCH	CSI Pn P	Delete n characters at the cursor
SU	CSI Pn S	Scroll up n lines
SD	CSI Pn T	Scroll down n lines
ECH	CSI Pn X	Erase n characters at the cursor
CBT	CSI Pn Z	Move cursor left n tabs
HPR	CSI Pn a	Move cursor to a column relative to the cursor
REP	CSI Pn b	Repeat the preceding character n times
VPA	CSI Py d	Move cursor to the specified row
VPR	CSI Pn e	Move cursor down n rows relative to the cursor
TBC	CSI Pn g	Clear one or all tab stops
DSR	CSI Pn n	Device status report
DECSTBM	CSI Pt ; Pb r	Set top and bottom margins
DECSLRM	CSI Pl ; Pr s	Set left and right margins
ICH	CSI Pn @	Insert n characters at the cursor
HPA	CSI Px `	Move cursor to the specified column
XTSHIFTESCAPE	CSI > Pn s	Configure shift modifier behavior with mouse reports
