[{"description": "treehash per file", "signed_content": {"payload": "eyJjb250ZW50X2hhc2hlcyI6W3siYmxvY2tfc2l6ZSI6NDA5NiwiZGlnZXN0Ijoic2hhMjU2IiwiZmlsZXMiOlt7InBhdGgiOiJjcnMucGIiLCJyb290X2hhc2giOiJoN090VXpfdlJUX1ZEYm9FUlpBbWgzX29GY0haUl9QcGVyVkRtaU1nb05RIn0seyJwYXRoIjoiY3RfY29uZmlnLnBiIiwicm9vdF9oYXNoIjoiZUNhZGpSWG03Tjl3ZkxPblFrR0pBYy1lWTFVTkdjSjR0VzBSMXVra1N3byJ9LHsicGF0aCI6ImtwX3BpbnNsaXN0LnBiIiwicm9vdF9oYXNoIjoibFF2RHg0MWtSYWlTUlBpYkY1NHBJWlYtUkZvbFhmOHBidWVpZjZKR1JxbyJ9LHsicGF0aCI6Im1hbmlmZXN0Lmpzb24iLCJyb290X2hhc2giOiJQeGFTV0h2bzV5OVdNNkd5NWJrRmNVQWk5bzNueVFlWUJqVGV6LTFneUR3In1dLCJmb3JtYXQiOiJ0cmVlaGFzaCIsImhhc2hfYmxvY2tfc2l6ZSI6NDA5Nn1dLCJpdGVtX2lkIjoiZWZuaW9qbG5qbmRtY2JpaWVlZ2tpY2Fkbm9lY2pqZWYiLCJpdGVtX3ZlcnNpb24iOiIxMjc4IiwicHJvdG9jb2xfdmVyc2lvbiI6MX0", "signatures": [{"header": {"kid": "publisher"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "ftFYPXbpfixlMvzK3EF1n8iSA1htIcpcxlOTVpo9EhqKWHQEgG5_xN8HmlnjVZyltriKvPY82blyd9skfyVx-XmOTHkJqDr-LsN1RtAdg1JGU51xci_THtPqohDe4KRpAdNCpT8Jv-gxfXryaQfDawx1cAy4glAqLLGXvpljyd1V6PR1j7I21lL2N07kDnMRtQBYdECtJQWBvJDRUOvR9ctTbj4x4u5uBmbtPhITkTtL1K4hWsLQ6TidRz6K147qBM7JayrmXnI8rpZ1XId86rQWHgpftOPZPSRax5AS_yvRwVqXm6Ek63-zGZD6xYzHunq5y2ZQ_fDkVAXrmcjpaidI_Btst8FX4VWjdjXUq7NURR-wZe4a4SPO8noCha6SghrpJIhWlbQaL1jO8Ij4V8R59IoUI9YUNgiA2xq_h_qFB8cALLMaZwwo4UgQ5Wf0rAkwvuwMvlV72U97Bmj6EXgTon828H4d327Zvv33QkIdV2o7z2I9zuEjYlj6u3wb9cYd0MWqIBFB5ykfMc_yh20thGTrCHVCayWqSsoX2MKuvnBhosc2X7yh1SQGtFBQ_kjYI00Po801TnCd51YVIphzzVLk6ryD4dE_TqO66cDrnUcHA_j7Okzt7OtJhRWpeyerHZL3bLob7CMAYRKB2CSBwKc0p53F_lCxn7fUMe4"}, {"header": {"kid": "webstore"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "BehUb5QPN7KTgmUnMQ6XTaIqCh8_lm0fRzZwupnnnKAjKFH8ONY-140-Oc1cWDhc4mPUmYdpa2g1_HwZe0IOuXZoCPMqLkIReukfeANctiCBlFjpBbvJqiLgpRt-haC0x2B5_6lmR1DySVgMUf1NcYfQlsf0Zsm71RHI6GJ4uH8J2AXsxPlVwzcGVwb1oHTByxKemKXxHHRiTecw5LuAAaW48Pd_X8dvrhUeBNX0Yc6S4HJ9KsHH5H1TCwUGvbFAA_y_vJkhbWBaznMYu6I0vq4hsSb9DeTZwcqC36hQzhFMo-vdGsOy8tl15oNb6WtqRZl3nsVQ0zRDloxIN19fJQ"}]}}]