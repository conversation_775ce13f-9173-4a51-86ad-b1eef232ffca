{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"live_caption_language": "de-DE"}}, "account_info": [{"access_point": 18, "account_id": "112802436725173337000", "accountcapabilities": {"accountcapabilities/g42tslldmfya": 1, "accountcapabilities/g44tilldmfya": 0, "accountcapabilities/ge2dinbnmnqxa": 0, "accountcapabilities/ge2tkmznmnqxa": 1, "accountcapabilities/ge2tknznmnqxa": 1, "accountcapabilities/ge2tkobnmnqxa": 1, "accountcapabilities/ge3dgmjnmnqxa": 1, "accountcapabilities/ge3dgobnmnqxa": 1, "accountcapabilities/geydgnznmnqxa": 1, "accountcapabilities/geytcnbnmnqxa": 1, "accountcapabilities/gezdcnbnmnqxa": 1, "accountcapabilities/gezdsmbnmnqxa": 0, "accountcapabilities/geztenjnmnqxa": 1, "accountcapabilities/gi2tklldmfya": 1, "accountcapabilities/gu2dqlldmfya": 1, "accountcapabilities/gu4dmlldmfya": 1, "accountcapabilities/guydolldmfya": 0, "accountcapabilities/guzdslldmfya": 0, "accountcapabilities/haytqlldmfya": 1, "accountcapabilities/he4tolldmfya": 0}, "email": "<EMAIL>", "full_name": "Freez AI", "gaia": "112802436725173337000", "given_name": "Freez", "hd": "NO_HOSTED_DOMAIN", "is_supervised_child": 0, "is_under_advanced_protection": false, "last_downloaded_image_url_with_size": "https://lh3.googleusercontent.com/a/ACg8ocJqgimw06si-ej1hgABfUBY4id9r2eGRfPlwpyHOh_nJ5phbw=s256-c-ns", "locale": "de", "picture_url": "https://lh3.googleusercontent.com/a/ACg8ocJqgimw06si-ej1hgABfUBY4id9r2eGRfPlwpyHOh_nJ5phbw=s96-c"}], "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "announcement_notification_service_first_run_time": "*****************", "apps": {"shortcuts_arch": "", "shortcuts_version": 0}, "autocomplete": {"retention_policy_last_version": 135}, "autofill": {"last_version_deduped": 135, "metadata_upload_events": {"2B2": 1}, "upload_encoding_seed": "F3ED38275A629DE5BD97ED13A5F65D59", "upload_events_last_reset_timestamp": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1105, "left": 662, "maximized": false, "right": 1942, "top": 5, "work_area_bottom": 1080, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "default_apps_install_state": 3, "default_search_provider": {"choice_screen_completion_timestamp": "***********", "choice_screen_completion_version": "135.0.7049.52", "choice_screen_random_shuffle_seed": "-**********706401455", "choice_screen_shuffle_milestone": 135, "guid": "485bf7d3-0215-45af-87dc-************", "synced_guid": "485bf7d3-0215-45af-87dc-************"}, "default_search_provider_data": {"mirrored_template_url_data": {"alternate_urls": ["{google:baseURL}#q={searchTerms}", "{google:baseURL}search#q={searchTerms}", "{google:baseURL}webhp#q={searchTerms}", "{google:baseURL}s#q={searchTerms}", "{google:baseURL}s?q={searchTerms}"], "contextual_search_url": "{google:baseURL}_/contextualsearch?{google:contextualSearchVersion}{google:contextualSearchContextData}", "created_from_play_api": false, "date_created": "0", "doodle_url": "", "enforced_by_policy": false, "favicon_url": "https://www.google.com/images/branding/product/ico/googleg_alldp.ico", "featured_by_policy": false, "id": "4", "image_search_branding_label": "", "image_translate_source_language_param_key": "sourcelang", "image_translate_target_language_param_key": "targetlang", "image_translate_url": "{google:baseSearchByImageURL}upload?filtertype=tr&{imageTranslateSourceLocale}{imageTranslateTargetLocale}", "image_url": "{google:baseSearchByImageURL}upload", "image_url_post_params": "encoded_image={google:imageThumbnail},image_url={google:imageURL},sbisrc={google:imageSearchSource},original_width={google:imageOriginalWidth},original_height={google:imageOriginalHeight},processed_image_dimensions={google:processedImageDimensions}", "input_encodings": ["UTF-8"], "is_active": 0, "keyword": "google.com", "last_modified": "0", "last_visited": "0", "logo_url": "", "new_tab_url": "", "originating_url": "", "policy_origin": 0, "preconnect_to_search_url": true, "prefetch_likely_navigations": true, "prepopulate_id": 1, "safe_for_autoreplace": true, "search_intent_params": ["si", "gs_ssp"], "search_url_post_params": "", "short_name": "Google", "starter_pack_id": 0, "suggestions_url": "{google:baseSuggestURL}search?{google:searchFieldtrialParameter}client={google:suggestClient}&gs_ri={google:suggestRid}&xssi=t&q={searchTerms}&{google:inputType}{google:omniboxFocusType}{google:cursorPosition}{google:currentPageUrl}{google:pageClassification}{google:clientCacheTimeToLive}{google:searchVersion}{google:sessionToken}{google:prefetchQuery}sugkey={google:suggestAPIKeyParameter}", "suggestions_url_post_params": "", "synced_guid": "485bf7d3-0215-45af-87dc-************", "url": "{google:baseURL}search?q={searchTerms}&{google:RLZ}{google:originalQueryForSuggestion}{google:assistedQueryStats}{google:searchFieldtrialParameter}{google:language}{google:prefetchSource}{google:searchClient}{google:sourceId}{google:contextualSearchVersion}ie={inputEncoding}", "usage_count": 0}, "template_url_data": {"alternate_urls": ["{google:baseURL}#q={searchTerms}", "{google:baseURL}search#q={searchTerms}", "{google:baseURL}webhp#q={searchTerms}", "{google:baseURL}s#q={searchTerms}", "{google:baseURL}s?q={searchTerms}"], "contextual_search_url": "{google:baseURL}_/contextualsearch?{google:contextualSearchVersion}{google:contextualSearchContextData}", "created_from_play_api": false, "date_created": "0", "doodle_url": "", "enforced_by_policy": false, "favicon_url": "https://www.google.com/images/branding/product/ico/googleg_alldp.ico", "featured_by_policy": false, "id": "4", "image_search_branding_label": "", "image_translate_source_language_param_key": "sourcelang", "image_translate_target_language_param_key": "targetlang", "image_translate_url": "{google:baseSearchByImageURL}upload?filtertype=tr&{imageTranslateSourceLocale}{imageTranslateTargetLocale}", "image_url": "{google:baseSearchByImageURL}upload", "image_url_post_params": "encoded_image={google:imageThumbnail},image_url={google:imageURL},sbisrc={google:imageSearchSource},original_width={google:imageOriginalWidth},original_height={google:imageOriginalHeight},processed_image_dimensions={google:processedImageDimensions}", "input_encodings": ["UTF-8"], "is_active": 0, "keyword": "google.com", "last_modified": "0", "last_visited": "0", "logo_url": "", "new_tab_url": "", "originating_url": "", "policy_origin": 0, "preconnect_to_search_url": true, "prefetch_likely_navigations": true, "prepopulate_id": 1, "safe_for_autoreplace": true, "search_intent_params": ["si", "gs_ssp"], "search_url_post_params": "", "short_name": "Google", "starter_pack_id": 0, "suggestions_url": "{google:baseSuggestURL}search?{google:searchFieldtrialParameter}client={google:suggestClient}&gs_ri={google:suggestRid}&xssi=t&q={searchTerms}&{google:inputType}{google:omniboxFocusType}{google:cursorPosition}{google:currentPageUrl}{google:pageClassification}{google:clientCacheTimeToLive}{google:searchVersion}{google:sessionToken}{google:prefetchQuery}sugkey={google:suggestAPIKeyParameter}", "suggestions_url_post_params": "", "synced_guid": "485bf7d3-0215-45af-87dc-************", "url": "{google:baseURL}search?q={searchTerms}&{google:RLZ}{google:originalQueryForSuggestion}{google:assistedQueryStats}{google:searchFieldtrialParameter}{google:language}{google:prefetchSource}{google:searchClient}{google:sourceId}{google:contextualSearchVersion}ie={inputEncoding}", "usage_count": 0}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "download_bubble": {"partial_view_impressions": 2}, "enterprise_profile_guid": "555dec28-6ea8-4210-8c0c-32332aa1e52e", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "135.0.7049.52", "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Entdecke tolle Apps, <PERSON><PERSON><PERSON>, Erweiterungen und Designs für Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "/opt/google/chrome/resources/web_store", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "ghbmnnjooekpmoecnnnilnnbdlolhkhi": {"ack_external": true, "lastpingday": "*****************"}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "/opt/google/chrome/resources/pdf", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "neajdppkdcdipfabeoofebfddakdcjhd": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["tts_extension.js"]}, "description": "Component extension providing speech via the Google network text-to-speech service.", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GSbNUMGygqQTNDMFGIjZNcwXsHLzkNkHjWbuY37PbNdSDZ4VqlVjzbWqODSe+MjELdv5Keb51IdytnoGYXBMyqKmWpUrg+RnKvQ5ibWr4MW9pyIceOIdp9GrzC1WZGgTmZismYR3AjaIpufZ7xDdQQv+XrghPWCkdVqLN+qZDA1HU+DURznkMICiDDSH2sU0egm9UbWfS218bZqzKeQDiC3OnTPlaxcbJtKUuupIm5knjze3Wo9Ae9poTDMzKgchg0VlFCv3uqox+wlD8sjXBoyBCCK9HpImdVAF1a7jpdgiUHpPeV/26oYzM9/grltwNR3bzECQgSpyXp0eyoegwIDAQAB", "manifest_version": 2, "name": "Google Network Speech", "permissions": ["metricsPrivate", "systemPrivate", "ttsEngine", "https://www.google.com/"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "/opt/google/chrome/resources/network_speech_synthesis", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.23"}, "path": "/opt/google/chrome/resources/hangout_services", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "nmmhkkegccagdldgiimedpiccmgmieda": {"account_extension_type": 0, "ack_external": true, "active_permissions": {"api": ["identity", "webview"], "explicit_host": ["https://payments.google.com/*", "https://sandbox.google.com/*", "https://www.google.com/*", "https://www.googleapis.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 137, "events": ["app.runtime.onLaunched", "runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": true, "granted_permissions": {"api": ["identity", "webview"], "explicit_host": ["https://payments.google.com/*", "https://sandbox.google.com/*", "https://www.google.com/*", "https://www.googleapis.com/*"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 10, "manifest": {"app": {"background": {"scripts": ["craw_background.js"]}}, "current_locale": "de", "default_locale": "en", "description": "Chrome Web Store-Zahlungen", "display_in_launcher": false, "display_in_new_tab_page": false, "icons": {"128": "images/icon_128.png", "16": "images/icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrKfMnLqViEyokd1wk57FxJtW2XXpGXzIHBzv9vQI/01UsuP0IV5/lj0wx7zJ/xcibUgDeIxobvv9XD+zO1MdjMWuqJFcKuSS4Suqkje6u+pMrTSGOSHq1bmBVh0kpToN8YoJs/P/yrRd7FEtAXTaFTGxQL4C385MeXSjaQfiRiQIDAQAB", "manifest_version": 2, "minimum_chrome_version": "29", "name": "Chrome Web Store-Zahlungen", "oauth2": {"auto_approve": true, "client_id": "203784468217.apps.googleusercontent.com", "scopes": ["https://www.googleapis.com/auth/sierra", "https://www.googleapis.com/auth/sierrasandbox", "https://www.googleapis.com/auth/chromewebstore", "https://www.googleapis.com/auth/chromewebstore.readonly"]}, "permissions": ["identity", "webview", "https://www.google.com/", "https://www.googleapis.com/*", "https://payments.google.com/payments/v4/js/integrator.js", "https://sandbox.google.com/payments/v4/js/integrator.js"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.0.0.6"}, "path": "nmmhkkegccagdldgiimedpiccmgmieda/1.0.0.6_0", "preferences": {}, "regular_only_preferences": {}, "running": false, "state": 1, "was_installed_by_default": true, "was_installed_by_oem": false}}, "theme": {"system_theme": 1}}, "gaia_cookie": {"changed_time": **********.907954, "hash": "F9VRaZS/BnH1mdlP9Dnl6Z5qmVM=", "last_list_accounts_data": "[\"gaia.l.a.r\",[[\"gaia.l.a\",1,\"Freez AI\",\"<EMAIL>\",\"https://lh3.googleusercontent.com/-l-h71Tthlwo/AAAAAAAAAAI/AAAAAAAAAAA/iUhTWIdE08k/s48-c/photo.jpg\",1,1,0,null,1,\"112802436725173337000\",null,null,null,null,1]]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.linux"}, "google": {"services": {"account_id": "112802436725173337000", "last_signed_in_username": "<EMAIL>", "signin": {"LAST_SIGNIN_ACCESS_POINT": {"time": "2025-04-09T18:48:41.947Z", "value": "18"}, "REFRESH_TOKEN_RECEIVED": {"time": "2025-04-09T18:48:41.947Z", "value": "Successful (112802436725173337000)"}}, "signin_scoped_device_id": "d2f9af67-98bc-407a-9361-8f2b168f93d5"}}, "https_upgrade_navigations": {"2025-04-09": 10}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "de-DE,de,en-US,en"}, "invalidation": {"per_sender_client_id_cache": {"1013309121859": "fZ4JcfsX5NM"}, "per_sender_registered_for_invalidation": {"1013309121859": {}}, "per_sender_topics_to_handler": {"1013309121859": {}}}, "language_model_counters": {"de": 3}, "media": {"device_id_salt": "2F505EE7E8BC2B44FACCDD9B491E59E9", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "JaUYI2t5XxU1HWWjJOgQMG+T3jhpmNh13bJGs8gEMkh+SLq96ohLA5t4dMCuIudDyLj9zXfrFL8thHlB6COEWA=="}, "ntp": {"num_personal_suggestions": 3}, "optimization_guide": {"predictionmodelfetcher": {"last_fetch_attempt": "13388698705610724", "last_fetch_success": "13388698705667323"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "EWALLET_MERCHANT_ALLOWLIST": true, "FORMS_ANNOTATIONS": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_account_store_login_database": true, "autofillable_credentials_profile_store_login_database": false, "relaunch_chrome_bubble_dismissed_counter": 0}, "pinned_tabs": [], "privacy_sandbox": {"fake_notice": {"first_sign_in_time": "*****************", "prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true, "m1": {"ad_measurement_enabled": true, "consent_decision_made": true, "eea_notice_acknowledged": true, "fledge_enabled": true, "topics_enabled": false}, "notices": {"ProtectedAudienceMeasurementNoticeModal": {"chrome_version": "135.0.7049.52", "notice_action_taken": 1, "notice_action_taken_time": "*****************", "notice_first_shown": "*****************", "notice_last_shown": "*****************", "notice_shown_duration": "2170682", "schema_version": 1}, "TopicsConsentDesktopModal": {"chrome_version": "135.0.7049.52", "notice_action_taken": 5, "notice_action_taken_time": "*****************", "notice_first_shown": "13388698815933476", "notice_last_shown": "13388698815933476", "notice_shown_duration": "3164549", "schema_version": 1}}, "topics_consent": {"consent_given": false, "last_update_reason": 1, "last_update_time": "13388698819098109", "text_at_last_update": "Eine Funktion zum Datenschutz bei Werbung aktivieren Wir führen neue Datenschutzeinstellungen ein, mit denen du mehr Auswahlmöglichkeiten in Bezug auf die angezeigte Werbung hast. Werbethemen unterstützen Websites dabei, dir relevante Werbung zu zeigen – deine Identität und dein Browserverlauf werden dabei geschützt. Chrome kann aufgrund deines aktuellen Browserverlaufs auf Themen schließen, die dich interessieren könnten. Websites, die du besuchst, können dann relevante Themen von Chrome anfordern, um die dir angezeigte Werbung zu personalisieren. Du kannst dir die Werbethemen in deinen Einstellungen ansehen und diejenigen blockieren, die nicht mit Websites geteilt werden sollen. Darüber hinaus werden in Chrome Werbethemen automatisch gelöscht, die älter als vier Wochen sind. Wenn du deine Meinung änderst, kannst du die Chrome-Einstellungen jederzeit entsprechend anpassen. Weitere Informationen zu Werbethemen Folgende Daten werden verwendet: Deine Werbethemen basieren auf deinem aktuellen Browserverlauf, also einer Liste der Websites, die du auf diesem Gerät in Chrome besucht hast. So verwenden wir diese Daten: Chrome schließt aufgrund der von dir besuchten Websites auf Themen, die dich interessieren könnten. Die Themenbereiche sind vordefiniert, wie beispielsweise „Kunst und Unterhaltung“, „Shopping“ und „Sport“. Wenn du dann eine Website besuchst, kann diese von Chrome einige deiner Themen anfordern, um die dir angezeigte Werbung zu personalisieren. Daten über deinen Browserverlauf werden allerdings nicht weitergegeben. So kannst du deine Daten verwalten: Themen, die älter als vier Wochen sind, werden in Chrome automatisch gelöscht. Je nach deinen Browseraktivitäten können die Themen wieder in der Liste erscheinen. Du kannst in den Einstellungen von Chrome auch Themen blockieren, die Chrome nicht mit Websites teilen soll, und Werbethemen deaktivieren. Weitere Informationen dazu, wie Google zum Schutz deiner Daten beiträgt, findest du in unserer Datenschutzerklärung."}}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "2592000000000", "check_mon_weight": 8, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "13390722444709845"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://accounts.google.de:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://accounts.youtube.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://www.amazon.de:443,*": {"last_modified": "*****************", "setting": {"client_hints": [0, 1, 3, 4, 5, 6, 10, 14, 19, 20, 22]}}, "https://www.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [4, 5, 9, 10, 11, 13, 14, 15, 16, 23, 25, 29]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]amazon.de,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]google.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": true}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "media_engagement": {"https://accounts.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.amazon.de:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://downloads/,*": {"last_modified": "13388698194392741", "setting": {"lastEngagementTime": 1.3388698194392738e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "chrome://newtab/,*": {"last_modified": "13388698695815482", "setting": {"lastEngagementTime": 1.3388698695815476e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "chrome://settings/,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3388698910597988e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 6.0, "rawScore": 6.0}}, "chrome://whats-new/,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3388698695935926e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.338869811165148e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}, "https://www.amazon.de:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3388698778843636e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 8.1, "rawScore": 8.1}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "135.0.7049.52", "creation_time": "13388698012152061", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "SessionEnded", "family_member_role": "not_in_family", "last_engagement_time": "13388698910597989", "last_time_obsolete_http_credentials_removed": 1744224472.162164, "last_time_password_store_metrics_reported": 1744224442.162239, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Mein Chrome", "password_hash_data_list": [{"hash": "djExRlbE+uzFGX8xFx+qoaHfmA==", "is_gaia": "djExrPt3fHX9Zv3SJkzMgsIfMA==", "last_signin": **********.668445, "salt_length": "djEx3NdFM6gZQp/oabbzPbdzg0d2PPQcSyAzxR2oy+7zYH0=", "username": "djEx9dBvrHgngPONOrsz48l1CHk88G4j+iU+xWp7EnZHmgQ="}], "were_old_google_logins_removed": true}, "protection": {"macs": {"browser": {"show_home_button": "9DDE23BD288B95F7CE675BBD01A9E2B63A7624B8C3CDB431097FDF3F63AB4E51"}, "default_search_provider_data": {"template_url_data": "C9FF8E9919B4835FA11451ACFEE1A5A207299C624E36D8831A16B3D6884167EC"}, "enterprise_signin": {"policy_recovery_token": "591DA1FC050B131B34673892259777A173A67541C1F956250F1D29B9ED8E6EA2"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "70AB04D2257271DCBDBCD9F406F3F86BF44130D7C7CF6B70D5812D50BC258B3D", "ghbmnnjooekpmoecnnnilnnbdlolhkhi": "7672BA290D2B3F749D75F3CFC67687097360BF8F0E9BE42375BB0E4F1C7E6B57", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "7A768BFB2ADDEC8F197453C2C8CC7F43BC9258C2053117E52DCCD87D7A53B870", "neajdppkdcdipfabeoofebfddakdcjhd": "49BFE721E2A2F82AAB9F03BDC50ACD36E1EC6E71B79F6CA4C9D64C4018F9F730", "nkeimhogjdpnpccoofpliimaahmaaome": "877213DA6A606CEABCDEA5EB354BE09D22A53CC1BDBA0B6FBD271549299732B6", "nmmhkkegccagdldgiimedpiccmgmieda": "0B5F1B95010824BFF39563DC4E73F448594B9C72B87CA58CB7D183BDCF7E0666"}, "ui": {"developer_mode": "ECA9732C00731C7A8DE889A1D309D022C867224F5F2A4E964384070306B2FD58"}}, "google": {"services": {"account_id": "56593F71083E5A87FF2C3545BB04BD4F2864F5B5EE8B77F164B571AA150129CC", "last_signed_in_username": "3503399A6F3AB299ABF3782C75350F56D6E1BC0A8C5F9AD5571955B6ED0E1D58", "last_username": "C202CF3B01A560B8B7D71D3B0076B61126EF72F4B11D79B3EA6E3661DB757E93"}}, "homepage": "B2A199504AEACAAD5C3A7BB4A96D9C3A9536D7A29672EB4DA3B9552B8D39C49C", "homepage_is_newtabpage": "306C67E79E036278678ED45B3C668C4421665A206FC4B97F053015981C8BAAE2", "media": {"storage_id_salt": "C29149AE129B959FDEB0CA9E54B924BF0A8BAF533937C017ADFBC9AA2FC7BC0C"}, "pinned_tabs": "14F8B2B035A86C0AEA5637DFD2AA7F5BDEADD0AAFF13141260E56C9477047715", "prefs": {"preference_reset_time": "7B22235E8A603BE387D81441C8C88F0C4E591567147FA05BE235C96189AC4490"}, "safebrowsing": {"incidents_sent": "F1827D0C55798CE7843DAF5DDEAB06A9BB2F9628970A5DCDA2543102436E4749"}, "search_provider_overrides": "99AC1EA12DA6196886F08A934B3B5006A725063DF41E9D0EE38F1FCFFDFDD5B0", "session": {"restore_on_startup": "74E1D625EF359DDAF159A835BC3731F9BCEC2AFE542FE783845A6292F572D0F5", "startup_urls": "D7174760A7168B445632139CD74E389AA027590889201AF1A252FFDE27B0531D"}}}, "safebrowsing": {"advanced_protection_last_refresh": "13388698695798691", "event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13388957212292698", "hash_real_time_ohttp_key": "zgAgINFJTz35hiSoEUY7UkKanSmd3mIlE/fZ7LGpzPGNsQIABAABAAI=", "metrics_last_log_time": "13388698012", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQnOfK6rKe5BcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADELDnyuqynuQX", "device_switcher_util": {"result": {"labels": ["Desktop"]}}, "last_db_compaction_time": "13388543999000000", "uma_in_sql_start_time": "13388698012162648"}, "sessions": {"event_log": [{"crashed": false, "time": "13388698012161630", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13388698384853701", "type": 2, "window_count": 1}, {"crashed": false, "time": "13388698695611772", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13388698920233366", "type": 2, "window_count": 1}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "sharing": {"fcm_registration": {"registration_timestamp": "13388698707381893"}, "local_sharing_info": {"enabled_features": [4, 8], "sender_id_target_info": {"device_auth_secret": "cjCtk50l8LLvTHn8FB8JSw==", "device_fcm_token": "ej-fh0ZoJYQ:APA91bGQqGCk7VJjCm2stOQCDwubLLqDBxTY2mx30O6WFFC83-5Aw_RHoiOGZ2BZ6iKyCQ9hSaSAhxgMyBaBO8UW-qTgm1nfc-jo8H6gyoEVfMwVwd9Ie-4", "device_p256dh": "BJ1hwgwXnCcRJ7KeoJ4NtA1xGwb0tb96XiL18t+L4IsSK8eAfk2m7Wnt6R4LRkMMUIs1hAbEZtUEqs5jcK9FoMk="}, "vapid_target_info": {"device_auth_secret": "", "device_fcm_token": "", "device_p256dh": ""}}}, "should_read_incoming_syncing_theme_prefs": true, "signin": {"PasswordSignInPromoShownCount": 1, "accounts_metadata_dict": {"112802436725173337000": {"ExtensionsExplicitBrowserSigninEnabled": false}}, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true, "explicit_browser_signin": true, "signin_with_explicit_browser_signin_on": true}, "spellcheck": {"dictionaries": ["de"], "dictionary": ""}, "sync": {"cached_passphrase_type": 2, "cached_persistent_auth_error": false, "cached_trusted_vault_auto_upgrade_experiment_group": "", "data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_loyalty_card": false, "autofill_profiles": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "gaia_id": "112802436725173337000", "local_device_guids_with_timestamp": [{"cache_guid": "2HFFxo2ktu2FvNK7Os0PUw==", "timestamp": 154961}], "passwords_per_account_pref_migration_done": true, "selected_types_per_account": {"tcFgDrKPJtN1Kj8uELsqClXisUO6HnWBcgTXCXDYmxA=": {"sync.passwords": true}}, "transport_data_per_account": {"tcFgDrKPJtN1Kj8uELsqClXisUO6HnWBcgTXCXDYmxA=": {"sync.bag_of_chips": "CoABEn5DaHJvbWUgTElOVVggMTM1LjAuNzA0OS41MiAoOWJhN2U2MDlkMjhjNTA5YThjZTkyNjVjMjI0NzA2NWQ4ZDI1MTE3My1yZWZzL2JyYW5jaC1oZWFkcy83MDQ5XzQxQHsjNH0pIGNoYW5uZWwoc3RhYmxlKSxnemlwKGdmZSk=", "sync.birthday": "z00000196-1a71-7f43-0000-000067f662af", "sync.cache_guid": "2HFFxo2ktu2FvNK7Os0PUw==", "sync.last_poll_time": "*****************", "sync.last_synced_time": "*****************", "sync.short_poll_interval": "***********"}}}, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "135", "migrated_default_apps": ["aohghmighlieiainnegkcijnfilokake", "aapocclcgogkmnckokdopfmhonfmgoek", "felcaaldnbdncclmgdcncolpebgiejap", "apdfllckaahabafndbhieahigkjlhalf", "pjkljhegncpnkpknbcohdijeoejaedia", "blpcfgokakmgnkcojhhkbfbldkacnbeo"]}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"heilbronner falken hannover scorpions\",\"sommerreifenpflicht in italien\",\"thermomix tm7\",\"sturm der liebe\",\"assassins creed shadows update\",\"köln fahrbahn abgesackt\",\"the last of us staffel 2\",\"luke littler new darts\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"CiAIkk4SGwoXVHJlbmRzIGJlaSBTdWNoYW5mcmFnZW4oCg\\u003d\\u003d\",\"google:suggestdetail\":[{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"google:entityinfo\":\"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\",\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002}],\"google:suggesteventid\":\"8381009613357060306\",\"google:suggestrelevance\":[1257,1256,1255,1254,1253,1252,1251,1250],\"google:suggestsubtypes\":[[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"ENTITY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\"]}]"}}