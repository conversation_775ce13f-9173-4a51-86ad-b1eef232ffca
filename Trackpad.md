# Konfiguration der Scrollgeschwindigkeit unter Ubuntu 24.04.2 LTS mit GNOME auf Wayland

Das Anpassen der Touchpad-Scrollgeschwindigkeit unter Ubuntu mit GNOME auf Wayland kann eine Herausforderung sein, da diese Funktion in den Standardeinstellungen nicht vorhanden ist. Im Folgenden finden Sie verschiedene Lösungen, die mit Ubuntu 24.04.2 LTS und dem Wayland Display Server kompatibel sind.


## Konfiguration mit libinput-config

Eine  leistungsfähige Lösung ist die Installation und Verwendung von libinput-config, das den Parameter "scroll-factor" direkt konfigurieren kann.

### Installation von libinput-config

1. Erstellen Sie eine libinput.conf-Datei im Root-etc-Verzeichnis:
   ```bash
   sudo touch /etc/libinput.conf
   ```

2. Installieren Sie meson:
   ```bash
   sudo apt install meson
   ```

3. Konfigurieren Sie meson für die Verwendung des nicht-glibc-Compilers:
   ```bash
   meson setup -Duse-glibc=false
   ```

4. Laden Sie den libinput-config-Code von GitLab herunter und extrahieren Sie ihn: Bereits heruntergeladen, er befindet sich hier: `TrackPad/libinput-config-default.zip`

5. Erstellen Sie die Installationsdateien:
   ```bash
   meson setup build
   cd build
   ninja
   sudo ninja install
   ```

6. Bearbeiten Sie die libinput.conf-Datei:
   ```bash
   sudo nano /etc/libinput.conf
   ```
   Fügen Sie folgende Zeile hinzu, um die Scrollgeschwindigkeit anzupassen:
   ```
   scroll-factor=0.3
   ```
   Ein niedrigerer Wert führt zu schnellerem Scrollen, ein höherer Wert zu langsamerem Scrollen.

7. Melden Sie sich ab und wieder an oder starten Sie Ihren Computer neu, damit die Änderungen wirksam werden
