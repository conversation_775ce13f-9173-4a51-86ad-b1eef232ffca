# Systemdokumentation Desktop

## Einleitung

Dieses Dokument dient als umfassendes Handbuch für die Systemadministration dieses Desktop-Systems. Es enthält detaillierte Informationen zu Hardware, Software, Netzwerkkonfiguration und Sicherheitsmaßnahmen. Dieses Dokument soll inkrementell wachsen und nur finale Entscheidungen und Regeln für dieses Sytem enthalten. Temporäre Tasks, <PERSON>hr<PERSON>, Ana<PERSON>sen und Versuche die dafür vorausgehen, werden in `activeTasks.md` festgehalten. 

## Hardware-Inventar und -Spezifikationen

### Prozessor
- Intel(R) Core(TM) i7-9700K CPU @ 3.60GHz

### Arbeitsspeicher
- 32 GB RAM

### Grafikkarten
- AMD Radeon RX 570 (Metal Default Device)
- Intel Iris Plus Graphics 655

### Netzwerkadapter
- Realtek RTL PCI Express Gigabit Ethernet Controller
- Broadcom Inc. und subsidiaries BCM4360 802.11ac Wireless Network Adapter
- Bluetooth BCM_20702B0

### Soundkarte
- Realtek ALC882

### Motherboard
- Gigabyte Z390

### Speichergeräte
- NVMe SSD Controller MAP1602
- Verbatim Vi7000G Internal PCIe 4 TB M.2 SSD (für Ubuntu)
  - Solid State: Ja
  - S.M.A.R.T.-Status: Überprüft
  - Kapazität: 4 TB (4.096.805.658.624 Bytes)

### Bootloader
- OpenCore 0.9.6 (Verfügbar: 1.0.4)

### Kernel
?
## Software-Inventar

### Betriebssystem
- Ubuntu Desktop Version 24.04.2 LTS

### Display-Server
- Wayland

### Login-Manager
- GNOME Display Manager (GDM3)

### Desktop-Umgebung
- GNOME Shell (Ubuntu-Modus)
  - Hinweis: Obwohl die Umgebungsvariable XDG_CURRENT_DESKTOP "Unity" anzeigt, wird tatsächlich GNOME Shell verwendet. Dies ist eine bewusste Konfiguration von Ubuntu, möglicherweise um Kompatibilität mit älteren Anwendungen zu gewährleisten.

#### Installierte GNOME-Erweiterungen
- Gesture Improvements (gestureImprovements@gestures)
  - Installationsort: ~/.local/share/gnome-shell/extensions/gestureImprovements@gestures
  - Beschreibung: Verbessert Touchpad-Gesten für Wayland/X11
  - Hinweis: Diese Erweiterung beeinflusst spezielle Gesten wie Workspace-Wechsel und Fenster-Tiling, jedoch nicht die normale Scrollgeschwindigkeit des Touchpads

### Installierte Anwendungen
#### Ollama
- Version: 0.6.2
- Installation: Erfolgreich installiert und als Systemdienst konfiguriert
- Status: Aktiv und läuft als Systemdienst (ollama.service)
- Autostart: Aktiviert (startet automatisch beim Systemboot)
- Speicherort: /usr/local/bin/ollama
- Daten-Verzeichnis: Standardmäßig in ~/.ollama
- Modelle: Noch keine Modelle heruntergeladen
- Nutzung:
  ```bash
  # Modell herunterladen
  ollama pull [modellname]
  
  # Mit einem Modell chatten
  ollama run [modellname]
  
  # Liste der installierten Modelle anzeigen
  ollama list
  
  # Informationen zu einem Modell anzeigen
  ollama show [modellname]
  ```
- Hinweise:
  - Ollama bietet standardmäßig keine grafische Benutzeroberfläche
  - Läuft als API-Server im Hintergrund auf Port 11434
  - Kann über die Kommandozeile oder kompatible Anwendungen genutzt werden
  - Für eine grafische Oberfläche kann OpenWebUI installiert werden

#### LM Studio
- Version: 0.3.13-2
- Installation: Als AppImage installiert und extrahiert
- Status: Manuell startbar über Anwendungsmenü oder Dock
- Speicherort: /home/<USER>/Applications/LM-Studio/
- Desktop-Integration:
  - Desktop-Datei: ~/.local/share/applications/lm-studio.desktop
  - Icon: ~/.local/share/icons/hicolor/256x256/apps/lm-studio.png
- Funktionen:
  - Vollständige grafische Benutzeroberfläche für LLM-Interaktion
  - Chat-UI für direkte Konversationen mit Modellen
  - Lokaler Server zum Experimentieren und Entwickeln mit lokalen LLMs
  - Stellt eine OpenAI-kompatible API bereit (kann als Ersatz für OpenAI-API verwendet werden)
  - Unterstützt verschiedene Modellformate (GGUF, GGML, etc.)
- Nutzung:
  - Modelle können direkt über die Benutzeroberfläche heruntergeladen werden
  - Eigene Modelle können importiert werden
  - Modellparameter (Temperatur, Top-P, etc.) können angepasst werden
  - API-Server kann für die Verwendung mit anderen Anwendungen gestartet werden
- Hinweise:
  - Unabhängig von Ollama (separate Implementierung)
  - Benötigt ausreichend RAM und GPU-Leistung für größere Modelle
  - Modelle werden standardmäßig im Verzeichnis `opt/llm/` gespeichert
  - Kann parallel zu Ollama verwendet werden, aber nicht gleichzeitig mit denselben GPU-Ressourcen

#### OpenWebUI
- Version: main (Docker-Image: ghcr.io/open-webui/open-webui:main)
- Installation: Als Docker-Container mit Host-Netzwerk
- Status: Aktiv und läuft als Docker-Container
- Autostart: Aktiviert (--restart always)
- Zugriff: http://localhost:8080
- Speicherort: Docker-Container
- Daten-Verzeichnis: Docker-Volume "open-webui" und Host-Verzeichnis "/home/<USER>/Projekte/open-webui-plugins" für Plugins
- Funktionen:
  - Grafische Benutzeroberfläche für Ollama
  - Chat-Interface für Interaktion mit LLMs
  - Unterstützung für Dokumente und RAG (Retrieval Augmented Generation)
  - Erweiterbar durch Plugins
  - Unterstützung für mehrere Modelle und parallele Konversationen
- Konfiguration:
  - Verbindung zu Ollama über http://127.0.0.1:11434
  - Netzwerkkonfiguration: --network=host für direkte Kommunikation mit Ollama
- Installationsbefehl:
  ```bash
  sudo docker run -d --network=host -v open-webui:/app/backend/data -e OLLAMA_BASE_URL=http://127.0.0.1:11434 -v /home/<USER>/Projekte/open-webui-plugins:/app/plugins --name open-webui --restart always ghcr.io/open-webui/open-webui:main
  ```
- Hinweise:
  - Benötigt eine laufende Ollama-Instanz
  - Bei Verbindungsproblemen ist die --network=host Option erforderlich
  - Zugriff erfolgt über Port 8080 (statt 3000) bei Verwendung von --network=host
  - Plugins können im Verzeichnis /home/<USER>/Projekte/open-webui-plugins entwickelt werden

#### Thunderbird
- Version: 136.0.1
- Installation: Manuell aus tar.xz-Archiv
- Status: Manuell startbar über Anwendungsmenü oder Terminal
- Speicherort: /opt/thunderbird
- Desktop-Integration:
  - Desktop-Datei: /usr/share/applications/thunderbird.desktop
  - Icon: /opt/thunderbird/chrome/icons/default/default256.png
- Ausführbare Datei: /usr/local/bin/thunderbird (symbolischer Link auf /opt/thunderbird/thunderbird)

#### Docker
- Version: Docker Engine 28.0.2 (Community Edition)
- Installation: Über das offizielle Docker-Repository
- Komponenten:
  - docker-ce
  - docker-ce-cli
  - containerd.io
  - docker-buildx-plugin
  - docker-compose-plugin
- Konfiguration: Siehe Docker-Abschnitt unten

### Festplatten/Partitionen
#### Systempartitionen
- EFI-Partition (/boot/efi): FAT32, 210 MB
- Boot-Partition (/boot): ext4, 100 GB
- Swap-Partition: 256 GB
- Root-Partition (/): btrfs, 500 GB
- Home-Partition (/home): btrfs, 100 GB
- LLM-Daten-Partition (/opt/llm): xfs, 300 GB
- Docker-Partition (/var/lib/docker): btrfs, 100 GB

## Netzwerktopologie und -konfiguration

### Netzwerkschnittstellen
*Wird noch festgelegt*

### IP-Konfiguration
*Wird noch festgelegt*

### DNS-Einstellungen
*Wird noch festgelegt*

### Firewall-Konfiguration
*Wird noch festgelegt*

## Sicherheitsrichtlinien und -maßnahmen

### Benutzerkonten und -berechtigungen
*Wird noch festgelegt*

### Authentifizierungsmethoden
*Wird noch festgelegt*

### Verschlüsselung
*Wird noch festgelegt*

### Backup-Strategie
*Wird noch festgelegt*

### Update-Richtlinien
*Wird noch festgelegt*

## Docker-Konfiguration und -Nutzung

### Konfiguration
- Storage-Driver: btrfs (optimiert für Snapshots und effiziente Speichernutzung)
- Docker Root Dir: /var/lib/docker (dedizierte 100 GB btrfs-Partition)
- Log-Konfiguration: json-file mit max-size=10m und max-file=3
- Ulimits: nofile=64000 (erhöhtes Limit für offene Dateien)

### Berechtigungen
- Der Benutzer "freez" ist Mitglied der docker-Gruppe und kann Docker-Befehle ohne sudo ausführen
- Für die Verwaltung von Docker-Volumes und -Images sind keine besonderen Berechtigungen erforderlich

### Wichtige Hinweise zur Docker-Nutzung
1. **Images und Container-Management**:
   - Regelmäßige Bereinigung nicht verwendeter Container und Images empfohlen:
     ```bash
     # Nicht verwendete Container entfernen
     docker container prune
     
     # Nicht verwendete Images entfernen
     docker image prune
     
     # Alle ungenutzten Ressourcen entfernen (Container, Images, Netzwerke, Volumes)
     docker system prune
     ```

2. **Speicherplatz-Überwachung**:
   - Docker-Speichernutzung überwachen:
     ```bash
     docker system df
     ```
   - Detaillierte Informationen zur Speichernutzung:
     ```bash
     docker system df -v
     ```

3. **Ressourcen-Limits**:
   - Bei Bedarf Container-Ressourcen begrenzen:
     ```bash
     # Beispiel: Container mit begrenztem Speicher und CPU starten
     docker run --memory=2g --cpus=2 [image]
     ```

4. **Netzwerk-Konfiguration**:
   - Standard-Bridge-Netzwerk für einfache Anwendungen
   - Für komplexere Setups eigene Netzwerke erstellen:
     ```bash
     docker network create [netzwerk-name]
     ```

5. **Volumes und Persistenz**:
   - Für persistente Daten Docker-Volumes verwenden:
     ```bash
     # Volume erstellen
     docker volume create [volume-name]
     
     # Container mit Volume starten
     docker run -v [volume-name]:/pfad/im/container [image]
     ```
   - Alternativ Host-Verzeichnisse einbinden:
     ```bash
     docker run -v /host/pfad:/container/pfad [image]
     ```

6. **Docker Compose**:
   - Für Multi-Container-Anwendungen Docker Compose verwenden
   - Compose-Dateien im YAML-Format im Projektverzeichnis speichern
   - Anwendungen starten und stoppen:
     ```bash
     docker compose up -d
     docker compose down
     ```

7. **Sicherheitshinweise**:
   - Container regelmäßig aktualisieren
   - Offizielle Images bevorzugen
   - Images vor der Verwendung auf Sicherheitslücken scannen:
     ```bash
     docker scan [image]
     ```
   - Rootless-Modus für erhöhte Sicherheit in Betracht ziehen