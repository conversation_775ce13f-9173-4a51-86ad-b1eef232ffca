# Vorbereitende Maßnahmen für Ubuntu-Installation vom Hackintosh aus
wichtige Vorbereitungen direkt von deinem macOS-System aus treffen. Hier ist ein umfassender Plan:

## 1. Ubuntu-Installationsmedium erstellen















## 2. Zielfestplatte 

Volumetyp : Physisches Gerät
BSD-Geräteknoten : disk1
Verbindung : PCI-Express
Gerätebaumpfad : IODeviceTree:/PCI0@0/RP09@1D/PXSX@0/IONVMeController
Beschreibbar : Nein
Groß-/Kleinschreibung wird beachtet : Nein
Volumekapazität : 4.096.805.658.624
Verfügbarer Platz (Löschbar + Frei) : 0
Löschbarer Speicherplatz : 0
Frei : 0
Belegt : 4.096.805.658.624
Eigentümer:in aktiviert : Nein
Ist verschlüsselt : Nein
Überprüfbar : Nein
Reparierbar : Nein
Startfähig : Nein
Journaling : Nein
Volume-Nummer : 1
Medienname : Vi7000G Internal PCIe M.2 SSD Media
Medientyp : Allgemein
Auswerfbar : Nein
Solid State : Ja
S.M.A.R.T.-Status : Überprüft


## 3. OpenCore für Dual-Boot konfigurieren

Da du bereits Zugriff auf die EFI-Partition und config.plist hast:

1. Sichere deine aktuelle config.plist: [ERLEDIGT]
Gesamter Inhalt der Efi-Mount:
`/Volumes/Daten Sonoma/IT/Geräte/Hackintosh/Efi_Backups`

2. Bearbeite die config.plist, um Ubuntu-Boot zu unterstützen: [ERLEDIGT]
   - Öffne die config.plist mit einem Texteditor oder ProperTree
   - Unter `Misc` → `Security` → `ScanPolicy` auf `0` setzen (erlaubt Erkennung aller Bootoptionen)
   - Unter `Misc` → `Boot` → `HideAuxiliary` auf `false` setzen
   - Unter `Misc` → `Boot` → `Timeout` auf `5` oder höher setzen (gibt Zeit zum Auswählen)

3. Stelle sicher, dass dein Theme Ubuntu-Icons enthält: [ERLEDIGT]
   - Überprüfe `/Volumes/EFI/EFI/OC/Resources/Image/alkindivv/hack/`
   - Sollte Ubuntu-Icons enthalten (falls nicht, können diese hinzugefügt werden)

## 4. Hardware-Kompatibilität vorbereiten [ERLEDIGT]

Mit Hackintool kannst du wichtige Hardware-Informationen sammeln:

1. Exportiere deine Kext-Liste und ACPI-Tabellen:
   - In Hackintool auf "Kexts" klicken und "Exportieren" wählen
   - Auf "ACPI" klicken und "Exportieren" wählen

2. Notiere dir wichtige Hardware-Details für Ubuntu-Treiber:
   - WLAN-Karte: Broadcom BCM4360 (benötigt spezielle Treiber in Ubuntu)
   - Grafikkarte: AMD Radeon RX 570 (gut unterstützt in Ubuntu)
   - Audio: Realtek ALC882 (benötigt möglicherweise Konfiguration)

3. Erstelle eine Textdatei mit Hardware-Notizen für die Installation:
```
# Hardware-Notizen für Ubuntu-Installation
WLAN: Broadcom BCM4360 - Benötigt broadcom-wl-dkms Paket
Audio: Realtek ALC882 - Möglicherweise alsa-firmware-Konfiguration nötig
Grafik: AMD Radeon RX 570 - Gut unterstützt, amdgpu Treiber
Ethernet: Realtek RTL - Gut unterstützt
```

## 5. Partitionierungsplan

manuelle Partitionierung während der Ubuntu-Installation:
### Partitionierungsschema

1. **EFI-Partition**: [ERLEDIGT]
   - Größe: Standard
   - Dateisystem: FAT32
   - Mountpunkt: /boot/efi
   - Wichtig für den Boot-Prozess

2. **Boot-Partition (/boot)**: [ERLEDIGT]
    - Größe: 10 GB
    - Dateisystem: ext4
    - Mountpunkt: /boot
    - Nicht auf LVM

3. **LVM**:
**Swap-Partition**: [ERLEDIGT]
   - Größe: 256 GB (für Hibernate-Funktionalität)
   - Dateisystem: swap
   - Für Auslagerungsspeicher und Ruhezustand
    - **Swap-Partition mit höherer Priorität**: 
      - Erstelle eine Swap-Partition auf der M.2 SSD [ERLEDIGT]
    - konfiguriere sie mit höherer Priorität [OFFEN]
    ```bash
    # In /etc/fstab nach der Installation:
    /dev/nvme0n1pX none swap sw,pri=100 0 0
    ```
    - **Swappiness anpassen**: Reduziere den Swappiness-Wert, damit das System die SSD nur bei Bedarf als RAM-Erweiterung nutzt
    ```bash
    # In /etc/sysctl.conf hinzufügen:
    vm.swappiness=10
    ```

**Root-Partition (/)**: [ERLEDIGT]
   - Größe:  500 GB
   - Dateisystem: btrfs
   - Für das Betriebssystem und Programme

4. **Home-Partition (/home)**: [ERLEDIGT]
   - Größe: 100 GB
   - Dateisystem: btrfs
   - Für persönliche Dateien und Konfigurationen

5. **Docker-Partition (/var/lib/docker)**: [OFFEn]
   - Größe: 100 GB
   - Dateisystem: btrfs (btrfs mit Unterstützung für Snapshots kann für Docker vorteilhaft sein)
   - Speziell für Docker-Images, Container und Volumes

6. **LLM-Daten-Partition (/opt/llm )**: [ERLEDIGT]
   - Größe: 300 GB
   - Dateisystem: xfs (für große Dateien optimiert)
   - Für LLM-Modelle, Trainingsdaten und Ausgaben`

## 6. Post-Installation-Skript vorbereiten [ERLEDIGT]
### 2. ZRAM für effizientere RAM-Nutzung
- Komprimiert RAM im Arbeitsspeicher, bevor Swap genutzt wird
  ```bash
  sudo apt install zram-config
  ```

Erstelle ein Skript, das du nach der Installation ausführen kannst:

```bash
#!/bin/bash
# Ubuntu Post-Installation Setup

# System aktualisieren
sudo apt update && sudo apt upgrade -y

# Wichtige Pakete installieren
sudo apt install -y build-essential git curl wget htop neofetch \
  python3-pip python3-venv docker.io docker-compose

# Docker konfigurieren
sudo usermod -aG docker $USER
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "data-root": "/var/lib/docker",
  "storage-driver": "overlay2",
  "default-ulimits": {
    "nofile": {
      "Name": "nofile",
      "Hard": 64000,
      "Soft": 64000
    }
  },
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF

# Systemoptimierungen für LLMs
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
echo 'fs.file-max=100000' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# LLM-Verzeichnis erstellen
sudo mkdir -p /opt/llm
sudo chown $USER:$USER /opt/llm

# AMD-Treiber für GPU-Beschleunigung
sudo apt install -y mesa-opencl-icd

echo "Setup abgeschlossen! Bitte System neu starten."
```


