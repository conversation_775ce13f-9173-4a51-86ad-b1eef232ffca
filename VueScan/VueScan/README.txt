README.txt - instructions for installing vuescan from .tgz file

To install VueScan, do the following:

sudo cp vuescan.svg /usr/share/icons/hicolor/scalable/apps/
sudo cp vuescan.rul /lib/udev/rules.d/60-vuescan.rules
sudo cp vuescan     /usr/bin/

On newer versions of Debian Linux, you may also need to do one of these commands:

sudo apt purge ippusbxd
sudo apt purge ipp-usb

You might also need to reboot your system, but normally
you shouldn't need to.  Just wait a minute for the
udev rules to be automatically applied to your system. Or
use the command:

  sudo udevadm control --reload-rules

You can do all of this automatically by installing VueScan from
the .deb file on Debian distributions or the .rpm file, from:

  https://www.hamrick.com/alternate-versions.html
