# Acer/BenQ 310U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="1a20", MODE="0666"
# Acer/BenQ 620U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="1a2a", MODE="0666"
# Acer/BenQ 320U/340U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="2022", MODE="0666"
# Acer/BenQ 620UT
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="2040", MODE="0666"
# Acer/BenQ 640U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="2060", MODE="0666"
# Acer/BenQ 640BU
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="207e", MODE="0666"
# Acer/BenQ 3000U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="20ae", MODE="0666"
# Acer/BenQ 3300/4300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="20b0", MODE="0666"
# Acer/BenQ 640BT
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="20be", MODE="0666"
# Acer/BenQ 1240
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="20c0", MODE="0666"
# Acer/BenQ 3300/4300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="20de", MODE="0666"
# Acer/BenQ 5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="20f8", MODE="0666"
# Acer/BenQ 5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="20fc", MODE="0666"
# Acer/BenQ 5300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="20fe", MODE="0666"
# Acer/BenQ 5450
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="211e", MODE="0666"
# Acer/BenQ ScanWit 2750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="211f", MODE="0666"
# Acer/BenQ 5150/5250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="2137", MODE="0666"
# Acer/BenQ 5560
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="2311", MODE="0666"
# Acer/BenQ 5160/5260
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="2331", MODE="0666"
# Agfa DuoScan f40
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bd", ATTRS{idProduct}=="02bf", MODE="0666"
# Agfa SnapScan 1212U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bd", ATTRS{idProduct}=="0001", MODE="0666"
# Agfa SnapScan 1212U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bd", ATTRS{idProduct}=="2061", MODE="0666"
# Agfa SnapScan 1236U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bd", ATTRS{idProduct}=="0002", MODE="0666"
# Agfa SnapScan e10
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bd", ATTRS{idProduct}=="2093", MODE="0666"
# Agfa SnapScan e20
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bd", ATTRS{idProduct}=="2091", MODE="0666"
# Agfa SnapScan e25
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bd", ATTRS{idProduct}=="2095", MODE="0666"
# Agfa SnapScan e26
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bd", ATTRS{idProduct}=="2097", MODE="0666"
# Agfa SnapScan e40
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bd", ATTRS{idProduct}=="208d", MODE="0666"
# Agfa SnapScan e42
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bd", ATTRS{idProduct}=="20ff", MODE="0666"
# Agfa SnapScan e50
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bd", ATTRS{idProduct}=="208f", MODE="0666"
# Agfa SnapScan e52
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bd", ATTRS{idProduct}=="20fd", MODE="0666"
# Avision 1200U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0268", MODE="0666"
# Avision FB6280E
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0804", MODE="0666"
# Avision FB1600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a11", MODE="0666"
# Avision AV600U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a13", MODE="0666"
# Avision DS610CU
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a16", MODE="0666"
# Avision AV8000S
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a17", MODE="0666"
# Avision AV610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a18", MODE="0666"
# Avision AV610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a19", MODE="0666"
# Avision AV600U Plus
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a1d", MODE="0666"
# Avision AV500U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a1e", MODE="0666"
# Avision AVA6
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a22", MODE="0666"
# Avision AV220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a23", MODE="0666"
# Avision AV210
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a25", MODE="0666"
# Avision AV120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a27", MODE="0666"
# Avision AV220C2
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a2a", MODE="0666"
# Avision AV220D2
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a2b", MODE="0666"
# Avision AV220+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a2c", MODE="0666"
# Avision AV220C2-G
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a2d", MODE="0666"
# Avision AV220C2-B
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a2e", MODE="0666"
# Avision AV210C2-G
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a2f", MODE="0666"
# Avision AV122
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a33", MODE="0666"
# Avision AVA6
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a37", MODE="0666"
# Avision AV210C2
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a3a", MODE="0666"
# Avision AV121
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a3c", MODE="0666"
# Avision AV160
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a3d", MODE="0666"
# Avision AV160
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a3e", MODE="0666"
# Avision AV180
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a3f", MODE="0666"
# Avision AV8300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a40", MODE="0666"
# Avision AM3000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a41", MODE="0666"
# Avision @V5100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a45", MODE="0666"
# Avision AV180
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a4a", MODE="0666"
# Avision AV8050U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a4d", MODE="0666"
# Avision AV3200SU
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a4e", MODE="0666"
# Avision AV3730SU
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a4f", MODE="0666"
# Avision AM6220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a52", MODE="0666"
# Avision AV6200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a57", MODE="0666"
# Avision AV320
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a5c", MODE="0666"
# Avision AV610C2
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a5e", MODE="0666"
# Avision IT8300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a61", MODE="0666"
# Avision AV3750SU
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a65", MODE="0666"
# Avision AV3850SU
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a66", MODE="0666"
# Avision AV8350
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a68", MODE="0666"
# Avision AM6120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a6a", MODE="0666"
# Avision AM6124
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a6b", MODE="0666"
# Avision AVA6+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a7f", MODE="0666"
# Avision FB6080E
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a82", MODE="0666"
# Avision FB2080E
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a84", MODE="0666"
# Avision AV600G
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a88", MODE="0666"
# Avision AV122C2
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a93", MODE="0666"
# Avision AV220-G
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a94", MODE="0666"
# Avision AV320+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a96", MODE="0666"
# Avision AW210
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a9f", MODE="0666"
# Avision AM3000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0aa0", MODE="0666"
# Avision @V2500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0aa1", MODE="0666"
# Avision AV220C2+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0ab8", MODE="0666"
# Avision DSL3100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0ac0", MODE="0666"
# Avision DSL3300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0ac1", MODE="0666"
# Avision DSL8000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0ac2", MODE="0666"
# Avision DSL8100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0ac3", MODE="0666"
# Avision DSL2200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0ac4", MODE="0666"
# Avision DSL2600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0ac5", MODE="0666"
# Avision DSL3700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0ac6", MODE="0666"
# Avision FB1200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0ad0", MODE="0666"
# Avision DS-320F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0f03", MODE="0666"
# Avision AV220D2+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="1a31", MODE="0666"
# Avision AV186+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="1a33", MODE="0666"
# Avision FB5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="1a36", MODE="0666"
# Avision AV620C2+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="1a50", MODE="0666"
# Avision AV176+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="1a51", MODE="0666"
# Avision AV178+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="1a53", MODE="0666"
# Avision AV175+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="1a54", MODE="0666"
# Avision AV185+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="1a55", MODE="0666"
# Avision IS1000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="1a60", MODE="0666"
# Avision AI370
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a13", MODE="0666"
# Avision AI370
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a14", MODE="0666"
# Avision AI130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a15", MODE="0666"
# Avision AW6200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a18", MODE="0666"
# Avision AV8300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a19", MODE="0666"
# Avision AT300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a1b", MODE="0666"
# Avision FB6280E
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a1e", MODE="0666"
# Avision FB2280E
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a1f", MODE="0666"
# Avision AT500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a32", MODE="0666"
# Avision AI260
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a3c", MODE="0666"
# Avision AV3200U+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a3e", MODE="0666"
# Avision DSL3930
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a41", MODE="0666"
# Avision AW1620
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a45", MODE="0666"
# Avision AV3220U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a46", MODE="0666"
# Avision AV830S
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a47", MODE="0666"
# Avision D650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a4d", MODE="0666"
# Avision AI3000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a4f", MODE="0666"
# Avision AT580
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a52", MODE="0666"
# Avision AW1230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a53", MODE="0666"
# Avision AI180
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a54", MODE="0666"
# Avision AI380
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a56", MODE="0666"
# Avision AI280
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a57", MODE="0666"
# Avision AW1260
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a59", MODE="0666"
# Avision AT660
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a5c", MODE="0666"
# Avision AW1900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a5d", MODE="0666"
# Avision AW1820
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a5e", MODE="0666"
# Avision AW1810
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a5f", MODE="0666"
# Avision AW6280
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a61", MODE="0666"
# Avision AW6120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a62", MODE="0666"
# Avision AV750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a67", MODE="0666"
# Avision AT580U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a68", MODE="0666"
# Avision AT260
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a6a", MODE="0666"
# Avision AT320A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a6b", MODE="0666"
# Avision AW6300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a71", MODE="0666"
# Avision AT360
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a72", MODE="0666"
# Avision AT130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a74", MODE="0666"
# Avision AT313
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a75", MODE="0666"
# Avision AT510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a76", MODE="0666"
# Avision AI2010
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a7c", MODE="0666"
# Avision AI170
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a7d", MODE="0666"
# Avision AI160
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a7e", MODE="0666"
# Avision AI150
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a7f", MODE="0666"
# Avision AI140
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a80", MODE="0666"
# Avision FBZ360
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a8b", MODE="0666"
# Avision DSL8280
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a8d", MODE="0666"
# Avision AI550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a8e", MODE="0666"
# Avision DSL8160
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a97", MODE="0666"
# Avision AH125
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a99", MODE="0666"
# Avision AH360
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a9c", MODE="0666"
# Avision AH135
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a9e", MODE="0666"
# Avision AH640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2a9f", MODE="0666"
# Avision AH235
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2aa0", MODE="0666"
# Avision AV320E2+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2aa1", MODE="0666"
# Avision FBH6100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2aa4", MODE="0666"
# Avision AT160
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2aa5", MODE="0666"
# Avision AI530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2aa6", MODE="0666"
# Avision FBH5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2aa7", MODE="0666"
# Avision FB1200+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2aa9", MODE="0666"
# Avision FBH6300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2aab", MODE="0666"
# Avision FBH6350E
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2aac", MODE="0666"
# Avision AT185
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ab1", MODE="0666"
# Avision FBH2000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ab2", MODE="0666"
# Avision AV1860
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ab3", MODE="0666"
# Avision FBH6380E
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ab4", MODE="0666"
# Avision FBH2100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ab5", MODE="0666"
# Avision AH330
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ab8", MODE="0666"
# Avision AH130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ab9", MODE="0666"
# Avision AH240
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2aba", MODE="0666"
# Avision AH260
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2abb", MODE="0666"
# Avision AH380
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2abc", MODE="0666"
# Avision AT200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2abf", MODE="0666"
# Avision FBH1000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ac2", MODE="0666"
# Avision FBH2200E
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ac7", MODE="0666"
# Avision AH50
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ac9", MODE="0666"
# Avision AH610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2aca", MODE="0666"
# Avision AH617
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2acb", MODE="0666"
# Avision AH625
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2acc", MODE="0666"
# Avision AH620
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2acd", MODE="0666"
# Avision AV280
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2acf", MODE="0666"
# Avision AH225
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ad1", MODE="0666"
# Avision AH245
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ad2", MODE="0666"
# Avision AW2000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ad6", MODE="0666"
# Avision AW2860
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ad7", MODE="0666"
# Avision AH118
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ad8", MODE="0666"
# Avision AV188
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ad9", MODE="0666"
# Avision FBZ160
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ada", MODE="0666"
# Avision AW6100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ae0", MODE="0666"
# Avision AW6160
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2aed", MODE="0666"
# Avision DSL3200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2aef", MODE="0666"
# Avision AT270
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2af0", MODE="0666"
# Avision AH3400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2af7", MODE="0666"
# Avision AV5200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2af8", MODE="0666"
# Avision D6000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2afa", MODE="0666"
# Avision AV1750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2afd", MODE="0666"
# Avision AV1760
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2afe", MODE="0666"
# Avision AH2600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b00", MODE="0666"
# Avision AI620
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b06", MODE="0666"
# Avision AT650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b09", MODE="0666"
# Avision AV620N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b0a", MODE="0666"
# Avision FB6200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b0b", MODE="0666"
# Avision AW570
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b14", MODE="0666"
# Avision AV1880
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b1c", MODE="0666"
# Avision AI180+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b1e", MODE="0666"
# Avision AH255
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b1f", MODE="0666"
# Avision AT366
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b20", MODE="0666"
# Avision AV176U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b21", MODE="0666"
# Avision AV187
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b23", MODE="0666"
# Avision SC8800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b26", MODE="0666"
# Avision T510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b35", MODE="0666"
# Avision D520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b36", MODE="0666"
# Avision U315
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b3a", MODE="0666"
# Avision T110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b3d", MODE="0666"
# Avision C100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b3e", MODE="0666"
# Avision D120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b3f", MODE="0666"
# Avision F5300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b41", MODE="0666"
# Avision C500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b42", MODE="0666"
# Avision AT83
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b43", MODE="0666"
# Avision U350
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b45", MODE="0666"
# Avision U370
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b46", MODE="0666"
# Avision C600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b47", MODE="0666"
# Avision AI390
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b4d", MODE="0666"
# Avision U380
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b51", MODE="0666"
# Avision C700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b53", MODE="0666"
# Avision M1160
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b54", MODE="0666"
# Avision S125
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b55", MODE="0666"
# Avision E1000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b56", MODE="0666"
# Avision F1300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b57", MODE="0666"
# Avision G1100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b58", MODE="0666"
# Avision L1250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b59", MODE="0666"
# Avision S525
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b5a", MODE="0666"
# Avision E5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b5b", MODE="0666"
# Avision G5100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b5c", MODE="0666"
# Avision M1600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b5d", MODE="0666"
# Avision AT322
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b65", MODE="0666"
# Avision U390
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b68", MODE="0666"
# Avision M2200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b69", MODE="0666"
# Avision AV50F+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b6a", MODE="0666"
# Avision L5250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b6c", MODE="0666"
# Avision L7250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b6d", MODE="0666"
# Avision M1800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b6e", MODE="0666"
# Avision M2100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b6f", MODE="0666"
# Avision L7350
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b71", MODE="0666"
# Avision AD125
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b72", MODE="0666"
# Avision L6250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b73", MODE="0666"
# Avision M1200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b74", MODE="0666"
# Avision G7100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b79", MODE="0666"
# Avision AV5400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b7a", MODE="0666"
# Avision M1360
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b82", MODE="0666"
# Avision AV32
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b8d", MODE="0666"
# Avision AD230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b96", MODE="0666"
# Avision AD240
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b97", MODE="0666"
# Avision C200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b9d", MODE="0666"
# Avision T230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2b9e", MODE="0666"
# Avision M2480
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ba5", MODE="0666"
# Avision AN120FW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ba9", MODE="0666"
# Avision D220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2baa", MODE="0666"
# Avision E2000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bab", MODE="0666"
# Avision G2100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bad", MODE="0666"
# Avision L2230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bae", MODE="0666"
# Avision M110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2baf", MODE="0666"
# Avision AD260
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bb1", MODE="0666"
# Avision AD280
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bb2", MODE="0666"
# Avision M3000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bb6", MODE="0666"
# Avision AN230W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bb7", MODE="0666"
# Avision L7280
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bc1", MODE="0666"
# Avision L5100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bc3", MODE="0666"
# Avision M3200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bd7", MODE="0666"
# Avision C100+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bdb", MODE="0666"
# Avision Q7250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2be8", MODE="0666"
# Avision D120+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bf3", MODE="0666"
# Avision C500+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bf4", MODE="0666"
# Avision D520+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bf5", MODE="0666"
# Avision AD250F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bf6", MODE="0666"
# Avision AD240S
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bf9", MODE="0666"
# Avision E1000+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2bfd", MODE="0666"
# Avision S125+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c02", MODE="0666"
# Avision FZ90
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c08", MODE="0666"
# Avision AD125S
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c10", MODE="0666"
# Avision AD250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c1c", MODE="0666"
# Avision M2580E
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c21", MODE="0666"
# Avision AN240W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c3a", MODE="0666"
# Avision AN240
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c3c", MODE="0666"
# Avision G7120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c43", MODE="0666"
# Avision E5000+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c49", MODE="0666"
# Avision F5300+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c4a", MODE="0666"
# Avision AD280F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c59", MODE="0666"
# Avision AD215
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c5a", MODE="0666"
# Avision L7280+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c60", MODE="0666"
# Avision AD215W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c66", MODE="0666"
# Avision AD215L
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c6a", MODE="0666"
# Avision AD215U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c6b", MODE="0666"
# Avision Q520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c6f", MODE="0666"
# Avision Q2120E
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c7b", MODE="0666"
# Avision Q2200E
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c7d", MODE="0666"
# Avision Q7100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c7f", MODE="0666"
# Avision Q8150
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2c82", MODE="0666"
# Avision AD230U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2cde", MODE="0666"
# Avision AN240W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2d74", MODE="0666"
# Avision AD240U+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2de2", MODE="0666"
# Avision AD350
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2e45", MODE="0666"
# Avision AV332
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2e4f", MODE="0666"
# Avision AD345
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2e57", MODE="0666"
# Avision AD335
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2e89", MODE="0666"
# Avision AD370
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2eb2", MODE="0666"
# Avision AV332U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2ee8", MODE="0666"
# Avision AD335WN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="2f28", MODE="0666"
# Canon D660U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2208", MODE="0666"
# Canon FB1210U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2205", MODE="0666"
# Canon FB320U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2201", MODE="0666"
# Canon FB630U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2204", MODE="0666"
# Canon FB640U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2202", MODE="0666"
# Canon FS2720U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="3053", MODE="0666"
# Canon FS4000US
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="3042", MODE="0666"
# Canon LiDE 20
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="220d", MODE="0666"
# Canon LiDE 25
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2220", MODE="0666"
# Canon LiDE 30
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="220e", MODE="0666"
# Canon N1220U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2207", MODE="0666"
# Canon N650U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2206", MODE="0666"
# Canon 8800F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1901", MODE="0666"
# Canon 9000F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1908", MODE="0666"
# Canon 9000F Mark II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="190d", MODE="0666"
# Canon LiDE 400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1912", MODE="0666"
# Canon LiDE 300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1913", MODE="0666"
# Canon DR-2080C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1601", MODE="0666"
# Canon DR-9080C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1603", MODE="0666"
# Canon DR-7080C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1604", MODE="0666"
# Canon DR-5010C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1606", MODE="0666"
# Canon DR-6080
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1607", MODE="0666"
# Canon DR-2580C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1608", MODE="0666"
# Canon DR-3080CII
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1609", MODE="0666"
# Canon DR-2050C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="160a", MODE="0666"
# Canon DR-7580
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="160b", MODE="0666"
# Canon DR-4010C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1614", MODE="0666"
# Canon DR-2510C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1617", MODE="0666"
# Canon DR-X10C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1618", MODE="0666"
# Canon DR-2010C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="161b", MODE="0666"
# Canon DR-3010C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="161d", MODE="0666"
# Canon DR-7090C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1620", MODE="0666"
# Canon DR-9050C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1622", MODE="0666"
# Canon DR-7550C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1623", MODE="0666"
# Canon DR-6050C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1624", MODE="0666"
# Canon DR-6010C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1626", MODE="0666"
# Canon DR-150
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1627", MODE="0666"
# Canon P-150
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="162c", MODE="0666"
# Canon P-150M
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="162d", MODE="0666"
# Canon DR-6030C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1638", MODE="0666"
# Canon DR-M160
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="163e", MODE="0666"
# Canon DR-M140
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="163f", MODE="0666"
# Canon DR-C125
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1640", MODE="0666"
# Canon DR-P215
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1641", MODE="0666"
# Canon P-215
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1646", MODE="0666"
# Canon P-215
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1647", MODE="0666"
# Canon DR-C130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="164a", MODE="0666"
# Canon DR-P208
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="164b", MODE="0666"
# Canon P-208
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="164c", MODE="0666"
# Canon DR-G1130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="164f", MODE="0666"
# Canon DR-G1100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1650", MODE="0666"
# Canon DR-C120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1651", MODE="0666"
# Canon P-201
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1652", MODE="0666"
# Canon DR-F120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1654", MODE="0666"
# Canon DR-M1060
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1657", MODE="0666"
# Canon DR-C225
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1658", MODE="0666"
# Canon DR-P215II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1659", MODE="0666"
# Canon P-215II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="165b", MODE="0666"
# Canon DR-P208II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="165d", MODE="0666"
# Canon P-208II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="165f", MODE="0666"
# Canon DR-C240
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1661", MODE="0666"
# Canon DR-M260
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="166d", MODE="0666"
# Canon DR-G2140
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="166e", MODE="0666"
# Canon DR-G2110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="166f", MODE="0666"
# Canon DR-C230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1670", MODE="0666"
# Canon DR-G2090
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1672", MODE="0666"
# Canon DR-C340
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1674", MODE="0666"
# Canon R40
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1679", MODE="0666"
# Canon R50
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="167e", MODE="0666"
# Canon RS40
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="1682", MODE="0666"
# Canon DR-1210C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2222", MODE="0666"
# Canon MP110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1700", MODE="0666"
# Canon MP130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1701", MODE="0666"
# Canon D1230U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2209", MODE="0666"
# Canon D2400U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="220a", MODE="0666"
# Canon D1250U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="220c", MODE="0666"
# Canon 8000F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="220f", MODE="0666"
# Canon 9900F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2210", MODE="0666"
# Canon 5000F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2212", MODE="0666"
# Canon 3000F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2215", MODE="0666"
# Canon 3200F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2216", MODE="0666"
# Canon 5200F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2217", MODE="0666"
# Canon 8200F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2218", MODE="0666"
# Canon 9950F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2219", MODE="0666"
# Canon 4200F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="221b", MODE="0666"
# Canon LiDE 500F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="221f", MODE="0666"
# Canon LiDE 600F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2224", MODE="0666"
# Canon LiDE 70
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2225", MODE="0666"
# Epson Perfection636U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0101", MODE="0666"
# Epson GT-2200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0102", MODE="0666"
# Epson Perfection610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0103", MODE="0666"
# Epson Perfection1200U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0104", MODE="0666"
# Epson StylusScan2000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0105", MODE="0666"
# Epson StylusScan2500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0106", MODE="0666"
# Epson Expression1600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0107", MODE="0666"
# Epson CC-700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0108", MODE="0666"
# Epson Expression1640XL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0109", MODE="0666"
# Epson Perfection1640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="010a", MODE="0666"
# Epson Perfection1240
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="010b", MODE="0666"
# Epson Perfection640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="010c", MODE="0666"
# Epson CC-500L
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="010d", MODE="0666"
# Epson Expression1680
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="010e", MODE="0666"
# Epson Perfection1250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="010f", MODE="0666"
# Epson Perfection1650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0110", MODE="0666"
# Epson Perfection2450
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0111", MODE="0666"
# Epson Perfection2450
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0112", MODE="0666"
# Epson Perfection660
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0114", MODE="0666"
# Epson Perfection1260
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="011d", MODE="0666"
# Epson Perfection1670
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="011f", MODE="0666"
# Epson Perfection1270
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0120", MODE="0666"
# Epson Perfection2480
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0121", MODE="0666"
# Epson 1000 ICS
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="011a", MODE="0666"
# Epson Perfection2400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="011b", MODE="0666"
# Epson Perfection3200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="011c", MODE="0666"
# Epson Perfection1660
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="011e", MODE="0666"
# Epson GT-15000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0126", MODE="0666"
# Epson Perfection4870
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0128", MODE="0666"
# Epson Expression10000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0129", MODE="0666"
# Epson Perfection4990
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="012a", MODE="0666"
# Epson GT-2500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="012b", MODE="0666"
# Epson PerfectionV700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="012c", MODE="0666"
# Epson GT-X970
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0135", MODE="0666"
# Epson GT-20000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0138", MODE="0666"
# Epson DS-5500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0145", MODE="0666"
# Epson DS-50000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0146", MODE="0666"
# Epson Expression11000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="014b", MODE="0666"
# Epson DS-510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="014c", MODE="0666"
# Epson DS-760
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="014d", MODE="0666"
# Epson DS-560
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0150", MODE="0666"
# Epson PerfectionV800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0151", MODE="0666"
# Epson DS-40
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0152", MODE="0666"
# Epson DS-520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0154", MODE="0666"
# Epson DS-530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0155", MODE="0666"
# Epson ES-400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0156", MODE="0666"
# Epson ES-500W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0157", MODE="0666"
# Epson ES-200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0159", MODE="0666"
# Epson ES-300W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="015a", MODE="0666"
# Epson Expression12000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="015b", MODE="0666"
# Epson DS-1630
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="015c", MODE="0666"
# Epson DS-1610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="015d", MODE="0666"
# Epson DS-1660W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="015e", MODE="0666"
# Epson FF-640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="015f", MODE="0666"
# Epson DS-70
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0160", MODE="0666"
# Epson DS-320
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0162", MODE="0666"
# Epson DS-770
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0163", MODE="0666"
# Epson DS-780N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0164", MODE="0666"
# Epson DS-410
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0165", MODE="0666"
# Epson DS-80W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0166", MODE="0666"
# Epson DS-535
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0167", MODE="0666"
# Epson DS-775
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0168", MODE="0666"
# Epson DS-575W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0169", MODE="0666"
# Epson FF-680W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="016b", MODE="0666"
# Epson ES-50
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="016c", MODE="0666"
# Epson ES-55R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="016d", MODE="0666"
# Epson ES-60W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="016e", MODE="0666"
# Epson ES-65WR
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="016f", MODE="0666"
# Epson DS-870
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0170", MODE="0666"
# Epson DS-875
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0171", MODE="0666"
# Epson DS-970
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0172", MODE="0666"
# Epson DS-975
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0173", MODE="0666"
# Epson ES-500WR
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0176", MODE="0666"
# Epson ES-300WR
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0177", MODE="0666"
# Epson DS-30000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0178", MODE="0666"
# Epson DS-32000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0179", MODE="0666"
# Epson DS-535H
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="017a", MODE="0666"
# Epson DS-31100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="017b", MODE="0666"
# Epson DS-31200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="017c", MODE="0666"
# Epson ES-865
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="017d", MODE="0666"
# Epson DS-730N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="017e", MODE="0666"
# Epson ES-580W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="017f", MODE="0666"
# Epson RR-600W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0180", MODE="0666"
# Epson ES-400 II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0181", MODE="0666"
# Epson DS-530 II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0182", MODE="0666"
# Epson DS-535 II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0183", MODE="0666"
# Epson DS-531
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0184", MODE="0666"
# Epson ES-580W II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0185", MODE="0666"
# Epson DS-570W II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0186", MODE="0666"
# Epson DS-575W II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0187", MODE="0666"
# Epson DS-571W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0188", MODE="0666"
# Epson DS-770 II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0189", MODE="0666"
# Epson DS-775 II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="018a", MODE="0666"
# Epson RR-70W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="018b", MODE="0666"
# Epson RR-60
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="018c", MODE="0666"
# Epson DS-790WN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="018d", MODE="0666"
# Epson Expression13000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="018e", MODE="0666"
# Epson DS-C330
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="018f", MODE="0666"
# Epson ES-C320W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0190", MODE="0666"
# Epson DS-C490
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0191", MODE="0666"
# Epson ES-C380W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0192", MODE="0666"
# Epson ES-C220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0193", MODE="0666"
# Epson RR-400W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0194", MODE="0666"
# Epson DS-C420W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0195", MODE="0666"
# Epson DS-C480W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0197", MODE="0666"
# Epson CX5100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0801", MODE="0666"
# Epson CX3100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0802", MODE="0666"
# Epson CX6300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0805", MODE="0666"
# Epson RX600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0806", MODE="0666"
# Epson RX500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0807", MODE="0666"
# Epson CX5300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0808", MODE="0666"
# Epson F-3200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="080a", MODE="0666"
# Epson CX1500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="080c", MODE="0666"
# Epson CX4500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="080d", MODE="0666"
# Epson CX3500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="080e", MODE="0666"
# Epson RX420
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="080f", MODE="0666"
# Epson RX700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0810", MODE="0666"
# Epson RX620
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0811", MODE="0666"
# Epson CX6500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0813", MODE="0666"
# Epson PM-A700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0814", MODE="0666"
# Epson AL-CX11
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0815", MODE="0666"
# Epson LP-M5500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0817", MODE="0666"
# Epson CX3700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0818", MODE="0666"
# Epson CX4700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0819", MODE="0666"
# Epson CX5800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0821", MODE="0666"
# Epson RX520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="081a", MODE="0666"
# Epson RX640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="081c", MODE="0666"
# Epson PM-A950
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="081d", MODE="0666"
# Epson CX7700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="081f", MODE="0666"
# Epson CX4100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0820", MODE="0666"
# Epson RX-560
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0827", MODE="0666"
# Epson PM-A970
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0828", MODE="0666"
# Epson PM-T990
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0829", MODE="0666"
# Epson PM-A920
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="082a", MODE="0666"
# Epson CX4900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="082b", MODE="0666"
# Epson CX5900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="082e", MODE="0666"
# Epson DX3900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="082f", MODE="0666"
# Epson CX2800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0830", MODE="0666"
# Epson LP-M5600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0833", MODE="0666"
# Epson LP-M6000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0834", MODE="0666"
# Epson AL-CX21
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0835", MODE="0666"
# Epson PM-T960
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0836", MODE="0666"
# Epson RX680
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0837", MODE="0666"
# Epson CX7300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0838", MODE="0666"
# Epson CX8300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0839", MODE="0666"
# Epson CX9300F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="083a", MODE="0666"
# Epson RX595
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="083c", MODE="0666"
# Epson NX100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0841", MODE="0666"
# Epson LP-M5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0843", MODE="0666"
# Epson Artisan 800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0844", MODE="0666"
# Epson Artisan 700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0846", MODE="0666"
# Epson WorkForce 600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0847", MODE="0666"
# Epson NX300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0848", MODE="0666"
# Epson NX200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0849", MODE="0666"
# Epson NX400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="084a", MODE="0666"
# Epson WorkForce 500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="084c", MODE="0666"
# Epson SX110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="084d", MODE="0666"
# Epson NX210
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="084f", MODE="0666"
# Epson TX650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0850", MODE="0666"
# Epson NX410
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0851", MODE="0666"
# Epson Artisan 710
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0852", MODE="0666"
# Epson Artisan 810
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0853", MODE="0666"
# Epson WorkForce 310
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0854", MODE="0666"
# Epson WorkForce 610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0855", MODE="0666"
# Epson NX510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0856", MODE="0666"
# Epson NX120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="085c", MODE="0666"
# Epson WorkForce 630
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="085d", MODE="0666"
# Epson WorkForce 625
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="085e", MODE="0666"
# Epson WorkForce 520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="085f", MODE="0666"
# Epson Artisan 835
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0860", MODE="0666"
# Epson Artisan 725
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0861", MODE="0666"
# Epson PX660
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0862", MODE="0666"
# Epson WorkForce 320
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0863", MODE="0666"
# Epson NX420
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0864", MODE="0666"
# Epson TX220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0865", MODE="0666"
# Epson AL-MX20
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0866", MODE="0666"
# Epson WF-7510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0869", MODE="0666"
# Epson WorkForce 840
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="086a", MODE="0666"
# Epson WorkForce 435
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0870", MODE="0666"
# Epson K200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0871", MODE="0666"
# Epson K300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0872", MODE="0666"
# Epson L200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0873", MODE="0666"
# Epson Artisan 630
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0878", MODE="0666"
# Epson Artisan 837
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0879", MODE="0666"
# Epson Artisan 730
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="087b", MODE="0666"
# Epson WF-7520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="087c", MODE="0666"
# Epson WP-4510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="087d", MODE="0666"
# Epson WP-4590
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="087e", MODE="0666"
# Epson PX-403A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="087f", MODE="0666"
# Epson NX330
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0880", MODE="0666"
# Epson SX230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0881", MODE="0666"
# Epson NX130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0883", MODE="0666"
# Epson NX430
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0884", MODE="0666"
# Epson NX230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0885", MODE="0666"
# Epson ME350
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="088d", MODE="0666"
# Epson WorkForce 645
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="088f", MODE="0666"
# Epson WorkForce 545
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0890", MODE="0666"
# Epson NX530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0891", MODE="0666"
# Epson WorkForce 845
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0892", MODE="0666"
# Epson EP-774A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0893", MODE="0666"
# Epson LP-M5300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0894", MODE="0666"
# Epson XP-100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0895", MODE="0666"
# Epson XP-200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0896", MODE="0666"
# Epson PX-405A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0897", MODE="0666"
# Epson XP-300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0898", MODE="0666"
# Epson WF-3520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0899", MODE="0666"
# Epson XP-850
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="089a", MODE="0666"
# Epson XP-800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="089b", MODE="0666"
# Epson XP-750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="089c", MODE="0666"
# Epson XP-700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="089d", MODE="0666"
# Epson XP-600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="089e", MODE="0666"
# Epson EP-705A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="089f", MODE="0666"
# Epson ME-100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08a0", MODE="0666"
# Epson L210
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08a1", MODE="0666"
# Epson WF-2510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08a5", MODE="0666"
# Epson WF-2520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08a6", MODE="0666"
# Epson WP-M4520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08a7", MODE="0666"
# Epson L355
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08a8", MODE="0666"
# Epson L550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08a9", MODE="0666"
# Epson M200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08aa", MODE="0666"
# Epson WF-M1650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08ab", MODE="0666"
# Epson AL-MX300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08ac", MODE="0666"
# Epson LP-M8040
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08ad", MODE="0666"
# Epson XP-210
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08ae", MODE="0666"
# Epson XP-310
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08af", MODE="0666"
# Epson XP-410
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08b0", MODE="0666"
# Epson XP-950
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08b3", MODE="0666"
# Epson XP-810
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08b4", MODE="0666"
# Epson XP-710
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08b5", MODE="0666"
# Epson XP-610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08b6", MODE="0666"
# Epson XP-510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08b7", MODE="0666"
# Epson WF-3620
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08b8", MODE="0666"
# Epson WF-7610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08b9", MODE="0666"
# Epson WF-8510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08bc", MODE="0666"
# Epson WF-5620
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08bd", MODE="0666"
# Epson WF-4630
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08be", MODE="0666"
# Epson XP-320
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08bf", MODE="0666"
# Epson XP-220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08c0", MODE="0666"
# Epson XP-420
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08c1", MODE="0666"
# Epson L455
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08c2", MODE="0666"
# Epson WF-2650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08c3", MODE="0666"
# Epson WF-2630
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08c4", MODE="0666"
# Epson EP-977A3
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08c5", MODE="0666"
# Epson XP-820
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08c6", MODE="0666"
# Epson XP-720
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08c7", MODE="0666"
# Epson XP-520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08c8", MODE="0666"
# Epson EP-707A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08c9", MODE="0666"
# Epson L850
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08ca", MODE="0666"
# Epson WF-R8590
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08cc", MODE="0666"
# Epson WF-R4640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08cd", MODE="0666"
# Epson WF-6530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08ce", MODE="0666"
# Epson WF-6590
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08cf", MODE="0666"
# Epson WF-M5690
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08d0", MODE="0666"
# Epson L220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08d1", MODE="0666"
# Epson L365
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08d2", MODE="0666"
# Epson L565
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="08d3", MODE="0666"
# Epson ET-4550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1101", MODE="0666"
# Epson XP-230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1102", MODE="0666"
# Epson XP-330
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1103", MODE="0666"
# Epson XP-430
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1104", MODE="0666"
# Epson ET-2500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1105", MODE="0666"
# Epson ET-2550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1106", MODE="0666"
# Epson ET-4500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1107", MODE="0666"
# Epson EP-10VA
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1108", MODE="0666"
# Epson XP-960
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1109", MODE="0666"
# Epson EP-808A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="110a", MODE="0666"
# Epson XP-830
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="110b", MODE="0666"
# Epson XP-530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="110c", MODE="0666"
# Epson EP-708A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="110d", MODE="0666"
# Epson PX-M160T
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="110f", MODE="0666"
# Epson LP-M8170
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1111", MODE="0666"
# Epson WF-C869R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1112", MODE="0666"
# Epson ET-16500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1113", MODE="0666"
# Epson XP-440
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1114", MODE="0666"
# Epson XP-340
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1115", MODE="0666"
# Epson XP-240
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1116", MODE="0666"
# Epson XP-640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1117", MODE="0666"
# Epson EP-709A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1118", MODE="0666"
# Epson EP-979A3
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1119", MODE="0666"
# Epson XP-900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="111a", MODE="0666"
# Epson EP-879A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="111b", MODE="0666"
# Epson WF-2750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="111c", MODE="0666"
# Epson ET-3600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="111d", MODE="0666"
# Epson L486
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="111e", MODE="0666"
# Epson L386
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="111f", MODE="0666"
# Epson L380
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1120", MODE="0666"
# Epson ET-2650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1121", MODE="0666"
# Epson ET-2600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1122", MODE="0666"
# Epson EP-30VA
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1123", MODE="0666"
# Epson WF-4720
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1125", MODE="0666"
# Epson WF-4740
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1126", MODE="0666"
# Epson WF-3720
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1127", MODE="0666"
# Epson WF-C20590
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1128", MODE="0666"
# Epson ET-4750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1129", MODE="0666"
# Epson ET-2750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="112a", MODE="0666"
# Epson ET-7700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="112b", MODE="0666"
# Epson ET-7750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="112c", MODE="0666"
# Epson WF-7710
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="112d", MODE="0666"
# Epson PX-M884F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="112e", MODE="0666"
# Epson ET-8700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="112f", MODE="0666"
# Epson ET-3700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1130", MODE="0666"
# Epson XP-8500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1131", MODE="0666"
# Epson XP-6000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1132", MODE="0666"
# Epson EP-810A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1133", MODE="0666"
# Epson EP-710A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1134", MODE="0666"
# Epson ET-2700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1135", MODE="0666"
# Epson XP-3100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1136", MODE="0666"
# Epson XP-4100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1137", MODE="0666"
# Epson WF-2810
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1138", MODE="0666"
# Epson XP-5100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1139", MODE="0666"
# Epson WF-2860
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="113a", MODE="0666"
# Epson WF-C17590
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="113b", MODE="0666"
# Epson WF-C8690
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="113c", MODE="0666"
# Epson XP-250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="113d", MODE="0666"
# Epson XP-450
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="113e", MODE="0666"
# Epson XP-350
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="113f", MODE="0666"
# Epson WF-M5799
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1140", MODE="0666"
# Epson L3100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1141", MODE="0666"
# Epson L3110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1142", MODE="0666"
# Epson ET-2710
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1143", MODE="0666"
# Epson EP-811A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1145", MODE="0666"
# Epson EP-711A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1146", MODE="0666"
# Epson XP-7100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1147", MODE="0666"
# Epson WF-C579R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1148", MODE="0666"
# Epson WF-3730
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1149", MODE="0666"
# Epson ET-M2140
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="114a", MODE="0666"
# Epson EP-881A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="114b", MODE="0666"
# Epson XP-6100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="114c", MODE="0666"
# Epson ET-4700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="114d", MODE="0666"
# Epson XP-2100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="114e", MODE="0666"
# Epson ET-M3140
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="114f", MODE="0666"
# Epson ET-M3170
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1150", MODE="0666"
# Epson ET-M3180
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1151", MODE="0666"
# Epson EC-4030
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1152", MODE="0666"
# Epson EC-4040
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1153", MODE="0666"
# Epson ST-4000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1154", MODE="0666"
# Epson ST-3000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1155", MODE="0666"
# Epson ST-2000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1156", MODE="0666"
# Epson ET-2720
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1157", MODE="0666"
# Epson ET-M2170
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1158", MODE="0666"
# Epson PX-M885F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1159", MODE="0666"
# Epson XP-8600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="115a", MODE="0666"
# Epson EP-812A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="115b", MODE="0666"
# Epson EP-712A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="115c", MODE="0666"
# Epson XP-970
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="115d", MODE="0666"
# Epson LP-M8180F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="115e", MODE="0666"
# Epson LP-M8180A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="115f", MODE="0666"
# Epson ST-M3000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1160", MODE="0666"
# Epson WF-M20590
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1161", MODE="0666"
# Epson EW-M752T
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1162", MODE="0666"
# Epson EP-M552T
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1163", MODE="0666"
# Epson ET-4760
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1164", MODE="0666"
# Epson ET-3710
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1165", MODE="0666"
# Epson ET-2760
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1166", MODE="0666"
# Epson WF-7630
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1167", MODE="0666"
# Epson WF-7820
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1168", MODE="0666"
# Epson M2120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1169", MODE="0666"
# Epson M2110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="116a", MODE="0666"
# Epson WF-C878R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="116c", MODE="0666"
# Epson WF-C879R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="116d", MODE="0666"
# Epson ET-15000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="116e", MODE="0666"
# Epson ET-16600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="116f", MODE="0666"
# Epson ET-16650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1170", MODE="0666"
# Epson WF-C20600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1171", MODE="0666"
# Epson WF-C20750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1172", MODE="0666"
# Epson WF-C21000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1173", MODE="0666"
# Epson ET-5800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1174", MODE="0666"
# Epson ET-5850
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1175", MODE="0666"
# Epson ET-5880
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1176", MODE="0666"
# Epson ST-C8000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1177", MODE="0666"
# Epson WF-3820
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="117a", MODE="0666"
# Epson WF-4820
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="117b", MODE="0666"
# Epson WF-4830
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="117c", MODE="0666"
# Epson EC-C7000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="117d", MODE="0666"
# Epson M15140
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="117e", MODE="0666"
# Epson ET-4850
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="117f", MODE="0666"
# Epson ST-C4100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1180", MODE="0666"
# Epson ET-3850
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1181", MODE="0666"
# Epson ST-C3100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1182", MODE="0666"
# Epson ET-3800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1183", MODE="0666"
# Epson ET-3830
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1184", MODE="0666"
# Epson ET-4800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1185", MODE="0666"
# Epson ET-2820
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1186", MODE="0666"
# Epson ET-2800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1187", MODE="0666"
# Epson L3210
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1188", MODE="0666"
# Epson L3200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1189", MODE="0666"
# Epson ET-2810
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="118a", MODE="0666"
# Epson ET-2850
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="118b", MODE="0666"
# Epson ST-C2100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="118c", MODE="0666"
# Epson EP-883A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="118d", MODE="0666"
# Epson EP-813A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="118e", MODE="0666"
# Epson EP-713A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="118f", MODE="0666"
# Epson ET-5150
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1190", MODE="0666"
# Epson ET-5170
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1191", MODE="0666"
# Epson LX-10020MF
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1192", MODE="0666"
# Epson ET-8500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1193", MODE="0666"
# Epson ET-8550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1194", MODE="0666"
# Epson EP-M553T
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1195", MODE="0666"
# Epson ET-16680
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1196", MODE="0666"
# Epson ST-C8090
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1197", MODE="0666"
# Epson ET-M16680
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1198", MODE="0666"
# Epson XP-3150
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="1199", MODE="0666"
# Epson XP-4150
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="119a", MODE="0666"
# Epson XP-2150
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="119b", MODE="0666"
# Epson WF-2820
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="119c", MODE="0666"
# Epson XP-5150
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="119d", MODE="0666"
# Epson WF-2880
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="119e", MODE="0666"
# Epson ET-5180
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="119f", MODE="0666"
# Epson XP-8700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11a0", MODE="0666"
# Epson EP-814A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11a1", MODE="0666"
# Epson EP-714A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11a2", MODE="0666"
# Epson EW-M754T
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11a3", MODE="0666"
# Epson AM-C4000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11a4", MODE="0666"
# Epson AM-C5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11a5", MODE="0666"
# Epson AM-C6000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11a6", MODE="0666"
# Epson WF-C4810
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11a7", MODE="0666"
# Epson SC-P8500DM
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11a8", MODE="0666"
# Epson SC-T7700DM
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11a9", MODE="0666"
# Epson SC-T5700DM
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11aa", MODE="0666"
# Epson WF-2950
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11ae", MODE="0666"
# Epson XP-3200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11af", MODE="0666"
# Epson XP-4200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11b0", MODE="0666"
# Epson XP-2200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11b1", MODE="0666"
# Epson WF-2960
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11b2", MODE="0666"
# Epson XP-5200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11b3", MODE="0666"
# Epson ET-2400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11b5", MODE="0666"
# Epson WF-C5810
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11b6", MODE="0666"
# Epson ET-4810
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11b7", MODE="0666"
# Epson ET-2840
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11b8", MODE="0666"
# Epson ET-2830
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11b9", MODE="0666"
# Epson EP-885A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11ba", MODE="0666"
# Epson EP-815A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11bb", MODE="0666"
# Epson EP-715A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11bc", MODE="0666"
# Epson M2050
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11bd", MODE="0666"
# Epson WF-M4619
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11bf", MODE="0666"
# Epson WF-M5899
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11c0", MODE="0666"
# Epson EP-886A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11c1", MODE="0666"
# Epson EP-816A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11c2", MODE="0666"
# Epson EP-716A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11c3", MODE="0666"
# Epson ST-C5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11c4", MODE="0666"
# Epson ST-C5500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11c5", MODE="0666"
# Epson ET-2860
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11c8", MODE="0666"
# Epson ET-2870
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="11c9", MODE="0666"
# Epson Perfection3170
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0116", MODE="0666"
# Epson Perfection4180
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0118", MODE="0666"
# Epson Perfection4490
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0119", MODE="0666"
# Epson Perfection3490/3590
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0122", MODE="0666"
# Epson PerfectionV10/V100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="012d", MODE="0666"
# Epson PerfectionV200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="012e", MODE="0666"
# Epson PerfectionV350
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="012f", MODE="0666"
# Epson PerfectionV500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0130", MODE="0666"
# Epson PerfectionV30/V300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0131", MODE="0666"
# Epson GT-1500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0133", MODE="0666"
# Epson GT-S80
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0136", MODE="0666"
# Epson GT-S50
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0137", MODE="0666"
# Epson PerfectionV600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="013a", MODE="0666"
# Epson PerfectionV550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="013b", MODE="0666"
# Epson PerfectionV19
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="013c", MODE="0666"
# Epson PerfectionV39
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="013d", MODE="0666"
# Epson PerfectionV19II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="013e", MODE="0666"
# Epson PerfectionV39II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="013f", MODE="0666"
# Epson PerfectionV33/V330
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0142", MODE="0666"
# Epson GT-S55
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0143", MODE="0666"
# Epson GT-S85
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0144", MODE="0666"
# Epson DS-30
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0147", MODE="0666"
# Epson PerfectionV37/V370
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="014a", MODE="0666"
# Epson GT-X830
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b8", ATTRS{idProduct}=="0153", MODE="0666"
# Fujitsu fi-4010CU
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0f01", MODE="0666"
# Fujitsu fi-4010CU
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1029", MODE="0666"
# Fujitsu fi-4120C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1041", MODE="0666"
# Fujitsu fi-4220C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1042", MODE="0666"
# Fujitsu fi-4530C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1078", MODE="0666"
# Fujitsu fi-5750C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1095", MODE="0666"
# Fujitsu fi-5110EOX2
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1096", MODE="0666"
# Fujitsu fi-5110C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1097", MODE="0666"
# Fujitsu fi-5650C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="10ad", MODE="0666"
# Fujitsu fi-4120C2
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="10ae", MODE="0666"
# Fujitsu fi-4220C2
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="10af", MODE="0666"
# Fujitsu fi-4340C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="10cf", MODE="0666"
# Fujitsu fi-5120C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="10e0", MODE="0666"
# Fujitsu fi-5220C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="10e1", MODE="0666"
# Fujitsu fi-5530C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="10e2", MODE="0666"
# Fujitsu fi-5110EOX3
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="10e6", MODE="0666"
# Fujitsu fi-5900C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="10e7", MODE="0666"
# Fujitsu fi-5110EOXM
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="10f2", MODE="0666"
# Fujitsu ScanSnap S500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="10fe", MODE="0666"
# Fujitsu ScanSnap S500M
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1135", MODE="0666"
# Fujitsu fi-5530C2
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="114a", MODE="0666"
# Fujitsu fi-6140
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="114d", MODE="0666"
# Fujitsu fi-6240
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="114e", MODE="0666"
# Fujitsu fi-6130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="114f", MODE="0666"
# Fujitsu fi-6230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1150", MODE="0666"
# Fujitsu ScanSnap S510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1155", MODE="0666"
# Fujitsu ScanSnap S510M
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="116f", MODE="0666"
# Fujitsu fi-6770
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1174", MODE="0666"
# Fujitsu fi-6770A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1175", MODE="0666"
# Fujitsu fi-6670
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1176", MODE="0666"
# Fujitsu fi-6670A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1177", MODE="0666"
# Fujitsu fi-6750S
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1178", MODE="0666"
# Fujitsu fi-6130T
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="117b", MODE="0666"
# Fujitsu fi-6230T
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="117c", MODE="0666"
# Fujitsu fi-6800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="119d", MODE="0666"
# Fujitsu fi-6800-CGA
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="119e", MODE="0666"
# Fujitsu fi-6900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="119f", MODE="0666"
# Fujitsu fi-6900-CGA
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="11a0", MODE="0666"
# Fujitsu ScanSnap S1500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="11a2", MODE="0666"
# Fujitsu fi-6125
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="11ee", MODE="0666"
# Fujitsu fi-6225
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="11ef", MODE="0666"
# Fujitsu fi-6140Z
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="11f1", MODE="0666"
# Fujitsu fi-6240Z
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="11f2", MODE="0666"
# Fujitsu fi-6130Z
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="11f3", MODE="0666"
# Fujitsu fi-6230Z
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="11f4", MODE="0666"
# Fujitsu fi-6110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="11fc", MODE="0666"
# Fujitsu fi-5950
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1213", MODE="0666"
# Fujitsu ScanSnap iX500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="132b", MODE="0666"
# Fujitsu fi-7180
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="132c", MODE="0666"
# Fujitsu fi-7280
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="132d", MODE="0666"
# Fujitsu fi-7160
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="132e", MODE="0666"
# Fujitsu fi-7260
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="132f", MODE="0666"
# Fujitsu ScanSnap iX500EE
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="13f3", MODE="0666"
# Fujitsu ScanSnap iX100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="13f4", MODE="0666"
# Fujitsu SP-25
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1409", MODE="0666"
# Fujitsu fi-6140ZLA
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="145f", MODE="0666"
# Fujitsu fi-6240ZLA
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1460", MODE="0666"
# Fujitsu fi-6130ZLA
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1461", MODE="0666"
# Fujitsu fi-6230ZLA
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1462", MODE="0666"
# Fujitsu fi-6125ZLA
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1463", MODE="0666"
# Fujitsu fi-6225ZLA
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1464", MODE="0666"
# Fujitsu fi-6135ZLA
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="146b", MODE="0666"
# Fujitsu fi-6235ZLA
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="146c", MODE="0666"
# Fujitsu fi-6120ZLA
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="146d", MODE="0666"
# Fujitsu fi-6220ZLA
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="146e", MODE="0666"
# Fujitsu fi-6400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14ac", MODE="0666"
# Fujitsu fi-600F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14b7", MODE="0666"
# Fujitsu fi-7480
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14b8", MODE="0666"
# Fujitsu fi-6420
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14bd", MODE="0666"
# Fujitsu fi-7460
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14be", MODE="0666"
# Fujitsu fi-7140
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14df", MODE="0666"
# Fujitsu fi-7240
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14e0", MODE="0666"
# Fujitsu fi-7135
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14e1", MODE="0666"
# Fujitsu fi-7235
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14e2", MODE="0666"
# Fujitsu fi-7130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14e3", MODE="0666"
# Fujitsu fi-7230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14e4", MODE="0666"
# Fujitsu fi-7125
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14e5", MODE="0666"
# Fujitsu fi-7225
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14e6", MODE="0666"
# Fujitsu fi-7120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14e7", MODE="0666"
# Fujitsu fi-7220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="14e8", MODE="0666"
# Fujitsu fi-400F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="151e", MODE="0666"
# Fujitsu fi-7030
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="151f", MODE="0666"
# Fujitsu fi-7700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1520", MODE="0666"
# Fujitsu fi-7600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1521", MODE="0666"
# Fujitsu fi-7700S
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1522", MODE="0666"
# Fujitsu fi-7300NX
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1575", MODE="0666"
# Fujitsu iX1500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="159f", MODE="0666"
# Fujitsu fi-800R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="15fc", MODE="0666"
# Fujitsu fi-8190
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="15fd", MODE="0666"
# Fujitsu fi-8290
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="15fe", MODE="0666"
# Fujitsu fi-8170
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="15ff", MODE="0666"
# Fujitsu fi-8270
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1600", MODE="0666"
# Fujitsu fi-8150
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1601", MODE="0666"
# Fujitsu fi-8250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1602", MODE="0666"
# Fujitsu fi-7900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="160a", MODE="0666"
# Fujitsu fi-7800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="160b", MODE="0666"
# Fujitsu iX1300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="162c", MODE="0666"
# Fujitsu fi-8150U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="162d", MODE="0666"
# Fujitsu fi-8250U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="162e", MODE="0666"
# Fujitsu iX1400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1630", MODE="0666"
# Fujitsu iX1600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1632", MODE="0666"
# Ricoh fi-8040
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05ca", ATTRS{idProduct}=="0307", MODE="0666"
# Ricoh fi-70F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05ca", ATTRS{idProduct}=="0308", MODE="0666"
# Fujitsu fi-60F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="10c7", MODE="0666"
# Fujitsu S300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1156", MODE="0666"
# Fujitsu S300M
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="117f", MODE="0666"
# Fujitsu fi-65F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="11bd", MODE="0666"
# Fujitsu S1300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="11ed", MODE="0666"
# Fujitsu S1100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1200", MODE="0666"
# Fujitsu S1300i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="128d", MODE="0666"
# Fujitsu S1100i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04c5", ATTRS{idProduct}=="1447", MODE="0666"
# Genius ColorPage-HR6
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0458", ATTRS{idProduct}=="2007", MODE="0666"
# Genius Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0458", ATTRS{idProduct}=="2008", MODE="0666"
# Genius Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0458", ATTRS{idProduct}=="2009", MODE="0666"
# Genius Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0458", ATTRS{idProduct}=="2013", MODE="0666"
# Genius Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0458", ATTRS{idProduct}=="2015", MODE="0666"
# Genius Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0458", ATTRS{idProduct}=="2016", MODE="0666"
# HP ScanJet 2100C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0505", MODE="0666"
# HP ScanJet 2200C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0605", MODE="0666"
# HP ScanJet 4100C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0101", MODE="0666"
# HP ScanJet 5200C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0401", MODE="0666"
# HP ScanJet 5300C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0701", MODE="0666"
# HP ScanJet 6200C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0201", MODE="0666"
# HP ScanJet 6300C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0601", MODE="0666"
# HP ScanJet 7400C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0801", MODE="0666"
# HP ScanJet 8200C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0b01", MODE="0666"
# HP ScanJet 8200C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3905", MODE="0666"
# HP ScanJet 8300C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3805", MODE="0666"
# Kodak i1220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="040a", ATTRS{idProduct}=="6013", MODE="0666"
# Kodak i30
SUBSYSTEMS=="usb", ATTRS{idVendor}=="040a", ATTRS{idProduct}=="6001", MODE="0666"
# Kodak i40
SUBSYSTEMS=="usb", ATTRS{idVendor}=="040a", ATTRS{idProduct}=="6002", MODE="0666"
# Kodak i40T
SUBSYSTEMS=="usb", ATTRS{idVendor}=="040a", ATTRS{idProduct}=="6003", MODE="0666"
# Kodak i55
SUBSYSTEMS=="usb", ATTRS{idVendor}=="040a", ATTRS{idProduct}=="6004", MODE="0666"
# Kodak i65
SUBSYSTEMS=="usb", ATTRS{idVendor}=="040a", ATTRS{idProduct}=="6005", MODE="0666"
# Kodak i85
SUBSYSTEMS=="usb", ATTRS{idVendor}=="040a", ATTRS{idProduct}=="6006", MODE="0666"
# Microtek ArtixScan 1010
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="2095", MODE="0666"
# Microtek ArtixScan 1800f
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="20d2", MODE="0666"
# Microtek ArtixScan 4000tf
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="20d6", MODE="0666"
# Microtek ArtixScan DI 1210
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="2007", MODE="0666"
# Microtek ArtixScan DI 1610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="201d", MODE="0666"
# Microtek ArtixScan DI 2020
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="202e", MODE="0666"
# Microtek ArtixScan M1
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="2035", MODE="0666"
# Microtek FileScan 3000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="201a", MODE="0666"
# Microtek ScanMaker 1000XL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="201e", MODE="0666"
# Microtek ScanMaker 4700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="20b4", MODE="0666"
# Microtek ScanMaker 4700LP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="209e", MODE="0666"
# Microtek ScanMaker 5600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="20a7", MODE="0666"
# Microtek ScanMaker 5700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="20bd", MODE="0666"
# Microtek ScanMaker 6700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="20c9", MODE="0666"
# Microtek ScanMaker 6800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="208b", MODE="0666"
# Microtek ScanMaker 8700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="20b1", MODE="0666"
# Microtek ScanMaker 9600XL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="208a", MODE="0666"
# Microtek ScanMaker 9700XL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="20e0", MODE="0666"
# Microtek ScanMaker 9800XL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="20de", MODE="0666"
# Microtek ScanMaker 9800XL+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="2165", MODE="0666"
# Microtek ScanMaker i700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="2017", MODE="0666"
# Microtek ScanMaker i800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="201f", MODE="0666"
# Microtek ScanMaker i900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="200c", MODE="0666"
# Microtek ScanMaker s400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="201c", MODE="0666"
# Microtek ScanMaker V6UL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="00ac", MODE="0666"
# Microtek ScanMaker V6UPL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="00b6", MODE="0666"
# Microtek ScanMaker V6UPL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="00ef", MODE="0666"
# Microtek ScanMaker V6USL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="00a3", MODE="0666"
# Microtek ScanMaker X12USL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="20b0", MODE="0666"
# Microtek ScanMaker X12USL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="20ee", MODE="0666"
# Microtek ScanMaker X6
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="2091", MODE="0666"
# Microtek ScanMaker X6USB
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="0091", MODE="0666"
# Microtek SlimScan C3
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="0094", MODE="0666"
# Microtek SlimScan C3
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="00a0", MODE="0666"
# Microtek SlimScan C6
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="009a", MODE="0666"
# Microtek SlimScan X6USB
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="0099", MODE="0666"
# Minolta Scan Dual II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="026a", MODE="0666"
# Minolta Scan Dual III
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0686", ATTRS{idProduct}=="400d", MODE="0666"
# Minolta Scan Dual IV
SUBSYSTEMS=="usb", ATTRS{idVendor}=="132b", ATTRS{idProduct}=="000a", MODE="0666"
# Minolta Scan Elite 5400 2
SUBSYSTEMS=="usb", ATTRS{idVendor}=="132b", ATTRS{idProduct}=="0012", MODE="0666"
# Minolta Scan Elite 5400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0686", ATTRS{idProduct}=="400e", MODE="0666"
# Minolta Scan Elite II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0686", ATTRS{idProduct}=="4004", MODE="0666"
# Minolta SC-110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a15", MODE="0666"
# Mustek BearPaw 1200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0400", ATTRS{idProduct}=="1000", MODE="0666"
# Mustek BearPaw 2400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0400", ATTRS{idProduct}=="1001", MODE="0666"
# Nexx NX2200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="1a1a", MODE="0666"
# Nexx NX2400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="1a1b", MODE="0666"
# Nikon LS-40
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b0", ATTRS{idProduct}=="4000", MODE="0666"
# Nikon LS-50
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b0", ATTRS{idProduct}=="4001", MODE="0666"
# Nikon LS-5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04b0", ATTRS{idProduct}=="4002", MODE="0666"
# Plustek A3
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0014", MODE="0666"
# Plustek OpticPro U12
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0010", MODE="0666"
# Plustek OpticPro U24
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0011", MODE="0666"
# Plustek OpticPro U24
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0015", MODE="0666"
# Plustek OpticPro
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0017", MODE="0666"
# Plustek Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0005", MODE="0666"
# Plustek Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0007", MODE="0666"
# Plustek Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="000f", MODE="0666"
# Plustek Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0012", MODE="0666"
# Plustek Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0013", MODE="0666"
# Plustek Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0016", MODE="0666"
# Plustek OpticFilm 7200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c07", MODE="0666"
# Plustek OpticFilm 7200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0807", MODE="0666"
# Plustek OpticFilm 7200i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c04", MODE="0666"
# Plustek OpticFilm 7300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c12", MODE="0666"
# Plustek OpticFilm 7400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c3a", MODE="0666"
# Plustek OpticFilm 7500i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c13", MODE="0666"
# Plustek OpticFilm 7600i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c3b", MODE="0666"
# Plustek OpticFilm 8100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="130c", MODE="0666"
# Plustek OpticFilm 8200i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="130d", MODE="0666"
# Plustek OpticFilm 135
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1422", MODE="0666"
# Plustek OpticFilm 135i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1436", MODE="0666"
# Plustek OpticFilm 8300i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="181f", MODE="0666"
# Plustek OpticFilm 8100(new)
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1824", MODE="0666"
# Plustek OpticFilm 8200i(new)
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1825", MODE="0666"
# Quato Intelli Scan 5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="4155", ATTRS{idProduct}=="ef50", MODE="0666"
# Quato Intelli Scan 5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="4155", ATTRS{idProduct}=="ef51", MODE="0666"
# Quato Intelli Scan 5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="4155", ATTRS{idProduct}=="ef52", MODE="0666"
# Quato Intelli Scan 5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="4155", ATTRS{idProduct}=="ef53", MODE="0666"
# Quato x-finity ultra
SUBSYSTEMS=="usb", ATTRS{idVendor}=="103b", ATTRS{idProduct}=="20e2", MODE="0666"
# Umax Astra 3400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1606", ATTRS{idProduct}=="0060", MODE="0666"
# Umax Astra 5400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1606", ATTRS{idProduct}=="0160", MODE="0666"
# Visioneer 430
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0497", MODE="0666"
# Visioneer 780
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0499", MODE="0666"
# Visioneer 9320
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0420", MODE="0666"
# Visioneer 9450
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0421", MODE="0666"
# Visioneer 9450R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="047a", MODE="0666"
# Visioneer 9550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0422", MODE="0666"
# Visioneer 9650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0390", MODE="0666"
# Visioneer 9650R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="047b", MODE="0666"
# Visioneer 9750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0423", MODE="0666"
# Visioneer 9750R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0493", MODE="0666"
# Visioneer XP450
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0424", MODE="0666"
# Visioneer XP450R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0491", MODE="0666"
# Visioneer XP470
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0479", MODE="0666"
# Visioneer XP470R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="048f", MODE="0666"
# Xerox DocuMate 150
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="049c", MODE="0666"
# Xerox DocuMate 152
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0477", MODE="0666"
# Xerox DocuMate 162
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="049d", MODE="0666"
# Xerox DocuMate 250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0448", MODE="0666"
# Xerox DocuMate 250R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0490", MODE="0666"
# Xerox DocuMate 252
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0449", MODE="0666"
# Xerox DocuMate 252R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="048c", MODE="0666"
# Xerox DocuMate 262
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="044c", MODE="0666"
# Xerox DocuMate 262i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="04a7", MODE="0666"
# Xerox DocuMate 262R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="048d", MODE="0666"
# Xerox DocuMate 272
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0475", MODE="0666"
# Xerox DocuMate 272R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="048e", MODE="0666"
# Xerox DocuMate 510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0446", MODE="0666"
# Xerox DocuMate 510R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="047c", MODE="0666"
# Xerox DocuMate 512
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0495", MODE="0666"
# Xerox DocuMate 520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0447", MODE="0666"
# Xerox DocuMate 520R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0492", MODE="0666"
# Xerox DocuMate 632
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0476", MODE="0666"
# Xerox DocuMate 632R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0498", MODE="0666"
# Xerox DocuMate 752
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0478", MODE="0666"
# Xerox DocuMate 752R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="049a", MODE="0666"
# Xerox DocuMate 3220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="04bf", MODE="0666"
# Xerox DocuMate 3120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="04d7", MODE="0666"
# Xerox DocuMate 3125
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="04c2", MODE="0666"
# Xerox DocuMate 4440
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="04b6", MODE="0666"
# Xerox Patriot D40
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="04de", MODE="0666"
# OKI C5510MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bc", ATTRS{idProduct}=="00aa", MODE="0666"
# OKI C5540MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bc", ATTRS{idProduct}=="00c3", MODE="0666"
# OKI C5540MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bc", ATTRS{idProduct}=="00c5", MODE="0666"
# OKI C5540MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bc", ATTRS{idProduct}=="00c7", MODE="0666"
# OKI C5510MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bc", ATTRS{idProduct}=="00d3", MODE="0666"
# OKI ES1624MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bc", ATTRS{idProduct}=="00a9", MODE="0666"
# OKI ES1624MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bc", ATTRS{idProduct}=="00c4", MODE="0666"
# OKI C5550MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bc", ATTRS{idProduct}=="011c", MODE="0666"
# OKI C5550MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bc", ATTRS{idProduct}=="011d", MODE="0666"
# OKI C5550MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bc", ATTRS{idProduct}=="011e", MODE="0666"
# OKI ES2032MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bc", ATTRS{idProduct}=="011f", MODE="0666"
# OKI CX2032MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bc", ATTRS{idProduct}=="0120", MODE="0666"
# OKI CX2033MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bc", ATTRS{idProduct}=="0202", MODE="0666"
# OKI ES5460MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06bc", ATTRS{idProduct}=="0203", MODE="0666"
# Canon 4400F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2228", MODE="0666"
# Canon 5600F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1906", MODE="0666"
# Canon 8400F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="221e", MODE="0666"
# Canon 8600F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2229", MODE="0666"
# Canon imageFORMULA 101
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1083", ATTRS{idProduct}=="162e", MODE="0666"
# Canon LiDE 100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1904", MODE="0666"
# Canon LiDE 110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1909", MODE="0666"
# Canon LiDE 120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="190e", MODE="0666"
# Canon LiDE 200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1905", MODE="0666"
# Canon LiDE 210
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="190a", MODE="0666"
# Canon LiDE 220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="190f", MODE="0666"
# Canon LiDE 50
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2213", MODE="0666"
# Canon LiDE 60
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="221c", MODE="0666"
# Canon LiDE 80
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2214", MODE="0666"
# Canon LiDE 90
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1900", MODE="0666"
# Canon LiDE 700F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1907", MODE="0666"
# HP 2300C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0901", MODE="0666"
# HP 2400C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0a01", MODE="0666"
# HP 3670C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1405", MODE="0666"
# HP 4850C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1b05", MODE="0666"
# HP G4010
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4505", MODE="0666"
# HP G4050
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4605", MODE="0666"
# HP N6310
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4705", MODE="0666"
# Medion MD5345
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0461", ATTRS{idProduct}=="0377", MODE="0666"
# Panasonic KV-SS080
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04da", ATTRS{idProduct}=="100f", MODE="0666"
# Pentax DSMobile 600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="2038", MODE="0666"
# Pentax DSMobile 600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0a17", ATTRS{idProduct}=="3210", MODE="0666"
# Syscan Docketport 465
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0a82", ATTRS{idProduct}=="4802", MODE="0666"
# Syscan Docketport 467
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1dcc", ATTRS{idProduct}=="4812", MODE="0666"
# Syscan Docketport 485
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0a82", ATTRS{idProduct}=="4800", MODE="0666"
# Syscan Docketport 487
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1dcc", ATTRS{idProduct}=="4810", MODE="0666"
# Syscan Docketport 665
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0a82", ATTRS{idProduct}=="4803", MODE="0666"
# Syscan Docketport 685
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0a82", ATTRS{idProduct}=="480c", MODE="0666"
# UMAX Astra 4500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0638", ATTRS{idProduct}=="0a10", MODE="0666"
# Visioneer 7100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0229", MODE="0666"
# Visioneer Roadwarrior
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0494", MODE="0666"
# Visioneer XP100 R3
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="049b", MODE="0666"
# Visioneer XP200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0426", MODE="0666"
# Visioneer XP300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0474", MODE="0666"
# Xerox OneTouch 2400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0461", ATTRS{idProduct}=="038b", MODE="0666"
# Xerox Travelscanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="04ac", MODE="0666"
# Lexmark X70/X73
SUBSYSTEMS=="usb", ATTRS{idVendor}=="043d", ATTRS{idProduct}=="002d", MODE="0666"
# Genius Colorpage Vivid3
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0458", ATTRS{idProduct}=="2011", MODE="0666"
# Genius Colorpage Vivid4
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0458", ATTRS{idProduct}=="2014", MODE="0666"
# Genius Colorpage Vivid3xe
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0458", ATTRS{idProduct}=="2017", MODE="0666"
# Genius Colorpage Vivid4xe
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0458", ATTRS{idProduct}=="201a", MODE="0666"
# Genius Colorpage Vivid4x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0458", ATTRS{idProduct}=="201b", MODE="0666"
# Genius Colorpage Vivid 1200 X
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0458", ATTRS{idProduct}=="201d", MODE="0666"
# Genius Colorpage Vivid 1200 XE
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0458", ATTRS{idProduct}=="201f", MODE="0666"
# Genius Colorpage SF600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0458", ATTRS{idProduct}=="2021", MODE="0666"
# Visioneer OneTouch 7300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a7", ATTRS{idProduct}=="0444", MODE="0666"
# Mustek ScanExpress A3 USB
SUBSYSTEMS=="usb", ATTRS{idVendor}=="055f", ATTRS{idProduct}=="0210", MODE="0666"
# Mustek BearPaw 2400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="055f", ATTRS{idProduct}=="0218", MODE="0666"
# Mustek BearPaw 2400 Plus
SUBSYSTEMS=="usb", ATTRS{idVendor}=="055f", ATTRS{idProduct}=="0219", MODE="0666"
# Mustek BearPaw 2448 Plus
SUBSYSTEMS=="usb", ATTRS{idVendor}=="055f", ATTRS{idProduct}=="021a", MODE="0666"
# Mustek BearPaw 1200 Plus
SUBSYSTEMS=="usb", ATTRS{idVendor}=="055f", ATTRS{idProduct}=="021b", MODE="0666"
# Mustek BearPaw 1200 CU Plus
SUBSYSTEMS=="usb", ATTRS{idVendor}=="055f", ATTRS{idProduct}=="021c", MODE="0666"
# Mustek BearPaw 2400 CU Plus
SUBSYSTEMS=="usb", ATTRS{idVendor}=="055f", ATTRS{idProduct}=="021d", MODE="0666"
# Mustek BearPaw 1200 CS
SUBSYSTEMS=="usb", ATTRS{idVendor}=="055f", ATTRS{idProduct}=="021e", MODE="0666"
# Mustek ScanExpress 1248
SUBSYSTEMS=="usb", ATTRS{idVendor}=="055f", ATTRS{idProduct}=="021f", MODE="0666"
# Mustek BearPaw 1200 CU
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05d8", ATTRS{idProduct}=="4002", MODE="0666"
# Plustek OpticPro 1248U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0400", MODE="0666"
# Plustek OpticPro 1248U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0401", MODE="0666"
# Plustek OpticPro U16B
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0402", MODE="0666"
# Plustek OpticPro UT16B
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0403", MODE="0666"
# Plustek OpticPro S12
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="040b", MODE="0666"
# Plustek OpticPro S24
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="040e", MODE="0666"
# Plustek OpticSlim M12
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0412", MODE="0666"
# Plustek OpticSlim 1200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0413", MODE="0666"
# Plustek OpticSlim 2400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0422", MODE="0666"
# Plustek IRIScan 2
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="045f", MODE="0666"
# Plustek OpticSlim 2400+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0454", MODE="0666"
# Plustek OpticSlim 500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0458", MODE="0666"
# Plustek SmartPhoto P60
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0466", MODE="0666"
# Plustek OpticSlim 2600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0467", MODE="0666"
# Plustek OpticSlim 500+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="046e", MODE="0666"
# Plustek OpticSlim 2610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="047e", MODE="0666"
# Plustek OpticPro ST48
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0800", MODE="0666"
# Plustek OpticPro ST28
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0802", MODE="0666"
# Plustek OpticSlim 2420
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0806", MODE="0666"
# Plustek OpticBook 3600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0900", MODE="0666"
# Plustek OpticSlim 2420T
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0902", MODE="0666"
# Plustek OpticBook 3600+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="090c", MODE="0666"
# Plustek OpticSlim 2420+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0914", MODE="0666"
# Plustek OpticPro S48
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0924", MODE="0666"
# Plustek OpticPro ST64+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c03", MODE="0666"
# Plustek SmartOffice PL3000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c05", MODE="0666"
# Plustek SmartOffice PL1200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c06", MODE="0666"
# Plustek SmartOffice PS256
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c08", MODE="0666"
# Plustek SmartOffice PL806
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c0c", MODE="0666"
# Plustek SmartOffice PL812
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c0d", MODE="0666"
# Plustek SmartOffice PS252
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c0e", MODE="0666"
# Plustek SmartOffice PL1500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c1f", MODE="0666"
# Plustek OpticPro A320
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c20", MODE="0666"
# Plustek SmartOffice PS282
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c22", MODE="0666"
# Plustek SmartOffice PS286
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c23", MODE="0666"
# Plustek OpticBook 4600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c26", MODE="0666"
# Plustek SmartOffice PS281
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c28", MODE="0666"
# Plustek SmartOffice PS286+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c29", MODE="0666"
# Plustek SmartOffice PL2000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c2a", MODE="0666"
# Plustek OpticSlim 550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c33", MODE="0666"
# Plustek SmartOffice PL1530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c38", MODE="0666"
# Plustek SmartOffice PS288
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c43", MODE="0666"
# Plustek SmartOffice PL2550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c4e", MODE="0666"
# Plustek SmartOffice PN2040
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c4f", MODE="0666"
# Plustek SmartOffice PL2040D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c51", MODE="0666"
# Plustek SmartOffice PL1530D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c52", MODE="0666"
# Plustek SmartOffice PL2000S
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c53", MODE="0666"
# Plustek SmartOffice PL1500S
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c54", MODE="0666"
# Plustek OpticPro ST64
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c59", MODE="0666"
# Plustek SmartOffice PL1000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c5b", MODE="0666"
# Plustek SmartOffice PS283
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c60", MODE="0666"
# Plustek OpticPro A311
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0c64", MODE="0666"
# Plustek SmartOffice PL7000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e00", MODE="0666"
# Plustek SmartOffice PL7500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e04", MODE="0666"
# Plustek OpticPro A360
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e05", MODE="0666"
# Plustek OpticBook A300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e08", MODE="0666"
# Plustek SmartOffice PS406
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e0a", MODE="0666"
# Plustek SmartOffice PS281+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e0b", MODE="0666"
# Plustek OpticPro A380
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e0c", MODE="0666"
# Plustek SmartOffice PS406U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e0d", MODE="0666"
# Plustek SmartOffice PS306
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e0e", MODE="0666"
# Plustek SmartOffice SC8016U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e10", MODE="0666"
# Plustek SmartOffice SN8016U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e11", MODE="0666"
# Plustek SmartOffice PS406UP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e14", MODE="0666"
# Plustek SmartOffice PS356U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e16", MODE="0666"
# Plustek SmartOffice PS306U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e18", MODE="0666"
# Plustek SmartOffice PS506U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e1b", MODE="0666"
# Plustek SmartOffice PS606U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e1c", MODE="0666"
# Plustek EasyScan 400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e1e", MODE="0666"
# Plustek SmartOffice PS456U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e20", MODE="0666"
# Plustek OpticBook A300+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e3e", MODE="0666"
# Plustek OpticBook A380L
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0e3f", MODE="0666"
# Plustek SmartOffice PL2546
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="0f01", MODE="0666"
# Plustek OpticSlim 2380
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1008", MODE="0666"
# Plustek OpticBook 3800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1300", MODE="0666"
# Plustek OpticBook 4800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1301", MODE="0666"
# Plustek OpticBook 4800(new)
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1823", MODE="0666"
# Plustek OpticPro ST640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1302", MODE="0666"
# Plustek OpticPro S28+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1304", MODE="0666"
# Plustek OpticPro SW280
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1305", MODE="0666"
# Plustek OpticPro S28 Pro
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1306", MODE="0666"
# Plustek OpticPro ST480
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1307", MODE="0666"
# Plustek OpticPro S48+
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1308", MODE="0666"
# Plustek OpticPro S64
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1309", MODE="0666"
# Plustek OpticBook 3900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="131b", MODE="0666"
# Plustek ePhoto Z300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="131c", MODE="0666"
# Plustek OpticBook 4900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1320", MODE="0666"
# Plustek SmartOffice PS386
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1401", MODE="0666"
# Plustek OpticPro ST960
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1402", MODE="0666"
# Plustek OpticPro S96
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1403", MODE="0666"
# Plustek SmartOffice PS396
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1404", MODE="0666"
# Plustek OpticFilm 120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1406", MODE="0666"
# Plustek OpticSlim A3 Series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="140b", MODE="0666"
# Plustek OpticSlim A4 Series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="140c", MODE="0666"
# Plustek OpticSlim 2680
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="140d", MODE="0666"
# Plustek SmartOffice PS3060U
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="140e", MODE="0666"
# Plustek OpticSlim 1680H
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1411", MODE="0666"
# Plustek OpticFilm 135
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1422", MODE="0666"
# Plustek OpticFilm 135i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1436", MODE="0666"
# Plustek OpticBook 3800L
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1801", MODE="0666"
# Plustek OpticPro A320L
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1802", MODE="0666"
# Plustek OpticPro A320E
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="181b", MODE="0666"
# Plustek OpticFilm 8300i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="181f", MODE="0666"
# Plustek OpticFilm 120 Pro
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1b00", MODE="0666"
# Plustek OpticPro A380 Plus
SUBSYSTEMS=="usb", ATTRS{idVendor}=="07b3", ATTRS{idProduct}=="1b06", MODE="0666"
# HP ScanJet 3800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2605", MODE="0666"
# HP ScanJet 3970
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2305", MODE="0666"
# HP ScanJet 4070
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2405", MODE="0666"
# HP ScanJet 4370
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4105", MODE="0666"
# HP ScanJet G2710
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2805", MODE="0666"
# HP ScanJet G3010
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4205", MODE="0666"
# HP ScanJet G3110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4305", MODE="0666"
# UMAX Astra 4900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="06dc", ATTRS{idProduct}=="0020", MODE="0666"
# Acer/BenQ 5550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a5", ATTRS{idProduct}=="2211", MODE="0666"
# HP ScanJet 4400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0705", MODE="0666"
# HP ScanJet 4470
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0805", MODE="0666"
# UMAX Astra 4400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1606", ATTRS{idProduct}=="0070", MODE="0666"
# HP ScanJet 4500/5550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1205", MODE="0666"
# HP ScanJet 4570/5500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1305", MODE="0666"
# HP ScanJet 5590
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1705", MODE="0666"
# HP ScanJet 7650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1805", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05da", ATTRS{idProduct}=="00cd", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0120", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0129", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1606", ATTRS{idProduct}=="f000", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="1606", ATTRS{idProduct}=="1220", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="2770", ATTRS{idProduct}=="0119", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="2770", ATTRS{idProduct}=="011a", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="2770", ATTRS{idProduct}=="011b", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="2770", ATTRS{idProduct}=="011c", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0121", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0122", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0123", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0124", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0126", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0127", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0128", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="012a", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0142", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0143", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0144", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0145", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0146", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0147", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0148", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="0149", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="014a", MODE="0666"
# PIE Scanner
SUBSYSTEMS=="usb", ATTRS{idVendor}=="05e3", ATTRS{idProduct}=="014b", MODE="0666"
# Canon G3030
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1105", MODE="0666"
# Canon G2030
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1106", MODE="0666"
# Canon TS2600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1107", MODE="0666"
# Canon TS2400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1108", MODE="0666"
# Canon GX1000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="110b", MODE="0666"
# Canon GX2000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="110d", MODE="0666"
# Canon TS7700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="110f", MODE="0666"
# Canon TS7700A
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1111", MODE="0666"
# Canon TS8700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1118", MODE="0666"
# Canon XK120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1119", MODE="0666"
# Canon GX6500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1148", MODE="0666"
# Canon TS6630
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="114e", MODE="0666"
# Canon TS7600i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="114f", MODE="0666"
# Canon TS6730
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1150", MODE="0666"
# Canon TR7800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1151", MODE="0666"
# Canon TS7700i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1152", MODE="0666"
# Canon MP410
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1702", MODE="0666"
# Canon MP430
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1703", MODE="0666"
# Canon MP330
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1704", MODE="0666"
# Canon MP900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1705", MODE="0666"
# Canon MP750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1706", MODE="0666"
# Canon MP780/790
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1707", MODE="0666"
# Canon MP760/770
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1708", MODE="0666"
# Canon MP150
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1709", MODE="0666"
# Canon MP170
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="170a", MODE="0666"
# Canon MP450
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="170b", MODE="0666"
# Canon MP500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="170c", MODE="0666"
# Canon MP800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="170d", MODE="0666"
# Canon MP800R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="170e", MODE="0666"
# Canon MP950
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1710", MODE="0666"
# Canon MP530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1712", MODE="0666"
# Canon MP830
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1713", MODE="0666"
# Canon MP160
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1714", MODE="0666"
# Canon MP180
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1715", MODE="0666"
# Canon MP460
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1716", MODE="0666"
# Canon MP510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1717", MODE="0666"
# Canon MP600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1718", MODE="0666"
# Canon MP600R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1719", MODE="0666"
# Canon MP810
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="171a", MODE="0666"
# Canon MP960
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="171b", MODE="0666"
# Canon MX7600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="171c", MODE="0666"
# Canon MP210
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1721", MODE="0666"
# Canon MP220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1722", MODE="0666"
# Canon MP470
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1723", MODE="0666"
# Canon MP520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1724", MODE="0666"
# Canon MP610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1725", MODE="0666"
# Canon MP970
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1726", MODE="0666"
# Canon MX300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1727", MODE="0666"
# Canon MX310
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1728", MODE="0666"
# Canon MX700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1729", MODE="0666"
# Canon MP140
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="172b", MODE="0666"
# Canon MX850
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="172c", MODE="0666"
# Canon MP980
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="172d", MODE="0666"
# Canon MP630
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="172e", MODE="0666"
# Canon MP620
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="172f", MODE="0666"
# Canon MP540
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1730", MODE="0666"
# Canon MP480
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1731", MODE="0666"
# Canon MP240
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1732", MODE="0666"
# Canon MP260
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1733", MODE="0666"
# Canon MP190
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1734", MODE="0666"
# Canon MX860
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1735", MODE="0666"
# Canon MX320
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1736", MODE="0666"
# Canon MX330
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1737", MODE="0666"
# Canon MP250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="173a", MODE="0666"
# Canon MP270
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="173b", MODE="0666"
# Canon MP490
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="173c", MODE="0666"
# Canon MP550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="173d", MODE="0666"
# Canon MP560
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="173e", MODE="0666"
# Canon MP640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="173f", MODE="0666"
# Canon MP990
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1740", MODE="0666"
# Canon MX340
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1741", MODE="0666"
# Canon MX350
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1742", MODE="0666"
# Canon MX870
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1743", MODE="0666"
# Canon MP280
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1746", MODE="0666"
# Canon MP495
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1747", MODE="0666"
# Canon MG5100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1748", MODE="0666"
# Canon MG5200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1749", MODE="0666"
# Canon MG6100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="174a", MODE="0666"
# Canon MG8100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="174b", MODE="0666"
# Canon MX360
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="174d", MODE="0666"
# Canon MX410
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="174e", MODE="0666"
# Canon MX420
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="174f", MODE="0666"
# Canon MX880
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1750", MODE="0666"
# Canon MG2100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1751", MODE="0666"
# Canon MG3100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1752", MODE="0666"
# Canon MG4100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1753", MODE="0666"
# Canon MG5300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1754", MODE="0666"
# Canon MG6200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1755", MODE="0666"
# Canon MG8200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1756", MODE="0666"
# Canon MP493
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1757", MODE="0666"
# Canon E500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1758", MODE="0666"
# Canon MX370
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1759", MODE="0666"
# Canon E600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="175a", MODE="0666"
# Canon MX430
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="175b", MODE="0666"
# Canon MX510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="175c", MODE="0666"
# Canon MX710
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="175d", MODE="0666"
# Canon MX890
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="175e", MODE="0666"
# Canon MP230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="175f", MODE="0666"
# Canon MG2200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1760", MODE="0666"
# Canon E510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1761", MODE="0666"
# Canon MG3200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1762", MODE="0666"
# Canon MG4200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1763", MODE="0666"
# Canon MG5400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1764", MODE="0666"
# Canon MG6300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1765", MODE="0666"
# Canon MX390
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1766", MODE="0666"
# Canon E610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1767", MODE="0666"
# Canon MX450
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1768", MODE="0666"
# Canon MX520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1769", MODE="0666"
# Canon MX720
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="176a", MODE="0666"
# Canon MX920
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="176b", MODE="0666"
# Canon MG2400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="176c", MODE="0666"
# Canon MG2500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="176d", MODE="0666"
# Canon MG3500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="176e", MODE="0666"
# Canon MG6500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="176f", MODE="0666"
# Canon MG6400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1770", MODE="0666"
# Canon MG5500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1771", MODE="0666"
# Canon MG7100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1772", MODE="0666"
# Canon P200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1773", MODE="0666"
# Canon MX470
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1774", MODE="0666"
# Canon MX530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1775", MODE="0666"
# Canon MB5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1776", MODE="0666"
# Canon MB5300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1777", MODE="0666"
# Canon MB2000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1778", MODE="0666"
# Canon MB2300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1779", MODE="0666"
# Canon E400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="177a", MODE="0666"
# Canon E560
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="177b", MODE="0666"
# Canon MG7500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="177c", MODE="0666"
# Canon MG6700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="177d", MODE="0666"
# Canon MG6600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="177e", MODE="0666"
# Canon MG5600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="177f", MODE="0666"
# Canon MG2900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1780", MODE="0666"
# Canon MX490
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1787", MODE="0666"
# Canon E460
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1788", MODE="0666"
# Canon E480
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1789", MODE="0666"
# Canon MG3600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="178a", MODE="0666"
# Canon MG7700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="178b", MODE="0666"
# Canon MG6900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="178c", MODE="0666"
# Canon MG6800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="178d", MODE="0666"
# Canon MG5700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="178e", MODE="0666"
# Canon MB5400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="178f", MODE="0666"
# Canon MB5100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1790", MODE="0666"
# Canon MB2700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1792", MODE="0666"
# Canon MB2100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1793", MODE="0666"
# Canon G3000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1794", MODE="0666"
# Canon G2000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1795", MODE="0666"
# Canon TS9000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="179f", MODE="0666"
# Canon TS8000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1800", MODE="0666"
# Canon TS6000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1801", MODE="0666"
# Canon TS5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1802", MODE="0666"
# Canon MG3000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="180b", MODE="0666"
# Canon E470
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="180c", MODE="0666"
# Canon G4000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="181d", MODE="0666"
# Canon E410
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="181e", MODE="0666"
# Canon TS9100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1820", MODE="0666"
# Canon TS8100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1821", MODE="0666"
# Canon TS6100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1822", MODE="0666"
# Canon TR8500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1823", MODE="0666"
# Canon TR7500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1824", MODE="0666"
# Canon TS5100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1825", MODE="0666"
# Canon TS3100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1827", MODE="0666"
# Canon E3100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1828", MODE="0666"
# Canon G2010
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="183a", MODE="0666"
# Canon G3010
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="183b", MODE="0666"
# Canon G4010
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="183d", MODE="0666"
# Canon TS9180
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="183e", MODE="0666"
# Canon TS8180
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="183f", MODE="0666"
# Canon TS6180
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1840", MODE="0666"
# Canon TR8580
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1841", MODE="0666"
# Canon TS8130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1842", MODE="0666"
# Canon TS6130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1843", MODE="0666"
# Canon TR8530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1844", MODE="0666"
# Canon TR7530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1845", MODE="0666"
# Canon XK50
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1846", MODE="0666"
# Canon XK70
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1847", MODE="0666"
# Canon TR4500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1854", MODE="0666"
# Canon E4200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1855", MODE="0666"
# Canon TS6200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1856", MODE="0666"
# Canon TS6280
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1857", MODE="0666"
# Canon TS6230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1858", MODE="0666"
# Canon TS8200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1859", MODE="0666"
# Canon TS8280
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="185a", MODE="0666"
# Canon TS8230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="185b", MODE="0666"
# Canon TS9500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="185c", MODE="0666"
# Canon TS9580
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="185d", MODE="0666"
# Canon TR9530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="185e", MODE="0666"
# Canon WG7000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1860", MODE="0666"
# Canon G7000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1863", MODE="0666"
# Canon G7080
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1864", MODE="0666"
# Canon G6000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1865", MODE="0666"
# Canon G6080
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1866", MODE="0666"
# Canon GM4000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1869", MODE="0666"
# Canon GM4080
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="186a", MODE="0666"
# Canon XK80
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1873", MODE="0666"
# Canon TS5300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="188b", MODE="0666"
# Canon TS5380
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="188c", MODE="0666"
# Canon TS6300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="188d", MODE="0666"
# Canon TS6380
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="188e", MODE="0666"
# Canon TS7330
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="188f", MODE="0666"
# Canon TS8300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1890", MODE="0666"
# Canon TS8380
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1891", MODE="0666"
# Canon TS8330
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1892", MODE="0666"
# Canon XK60
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1893", MODE="0666"
# Canon TS6330
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="1894", MODE="0666"
# Canon TS3300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18a2", MODE="0666"
# Canon E3300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18a3", MODE="0666"
# Canon GX6000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18a6", MODE="0666"
# Canon GX7000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18a8", MODE="0666"
# Canon TS7430
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18a9", MODE="0666"
# Canon TR7600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18aa", MODE="0666"
# Canon TR8600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18ad", MODE="0666"
# Canon TR8630
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18af", MODE="0666"
# Canon TS8430
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18b5", MODE="0666"
# Canon XK90
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18b6", MODE="0666"
# Canon TS3400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18b7", MODE="0666"
# Canon E3400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18b8", MODE="0666"
# Canon TR7000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18b9", MODE="0666"
# Canon G2020
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18bd", MODE="0666"
# Canon G3020
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18bf", MODE="0666"
# Canon G2060
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18c1", MODE="0666"
# Canon G3060
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18c3", MODE="0666"
# Canon TS6400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18d3", MODE="0666"
# Canon TS3500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18d4", MODE="0666"
# Canon G600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18d5", MODE="0666"
# Canon TS7400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18d7", MODE="0666"
# Canon TS5420
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18d8", MODE="0666"
# Canon TS5350i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18d9", MODE="0666"
# Canon TR4600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18da", MODE="0666"
# Canon E4500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18db", MODE="0666"
# Canon TR4700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18dc", MODE="0666"
# Canon XK500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18df", MODE="0666"
# Canon TS8530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18e0", MODE="0666"
# Canon TS7530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18e1", MODE="0666"
# Canon XK100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18e2", MODE="0666"
# Canon GX3000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18f1", MODE="0666"
# Canon GX4000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18f2", MODE="0666"
# Canon G4070
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18f3", MODE="0666"
# Canon G3070
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18f4", MODE="0666"
# Canon G2070
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18f5", MODE="0666"
# Canon TS7450i
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18f7", MODE="0666"
# Canon TS8630
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18f8", MODE="0666"
# Canon XK110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18f9", MODE="0666"
# Canon TC-20M
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18fc", MODE="0666"
# Canon TC-5200M
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="18ff", MODE="0666"
# Canon MP5
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2635", MODE="0666"
# Canon MP10
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="261f", MODE="0666"
# Canon MP360
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="263c", MODE="0666"
# Canon MP370
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="263d", MODE="0666"
# Canon MP375R
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="263f", MODE="0666"
# Canon MP390
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="263e", MODE="0666"
# Canon MP700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2630", MODE="0666"
# Canon MP710
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="264d", MODE="0666"
# Canon MP730
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="262f", MODE="0666"
# Canon MP740
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="264c", MODE="0666"
# Canon MF5730
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="265d", MODE="0666"
# Canon MF5750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="265e", MODE="0666"
# Canon MF5770
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="265f", MODE="0666"
# Canon MF3110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2660", MODE="0666"
# Canon iR1018
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="269d", MODE="0666"
# Canon iR1020
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="26e6", MODE="0666"
# Canon MF4270
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="26b5", MODE="0666"
# Canon MF4100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="26a3", MODE="0666"
# Canon MF4690
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="26b0", MODE="0666"
# Canon D420
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="26ef", MODE="0666"
# Canon D460
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="26ed", MODE="0666"
# Canon MF4360
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="26ec", MODE="0666"
# Canon MF4320
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="26ee", MODE="0666"
# Canon MF4010
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="26b4", MODE="0666"
# Canon MF3240
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2684", MODE="0666"
# Canon MF6500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2686", MODE="0666"
# Canon MF4400w
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="275b", MODE="0666"
# Canon MF4400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2737", MODE="0666"
# Canon MF4500w
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="275a", MODE="0666"
# Canon MF4500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2736", MODE="0666"
# Canon MF3010
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2759", MODE="0666"
# Canon MF4700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2774", MODE="0666"
# Canon MF4800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2773", MODE="0666"
# Canon MF8200C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2779", MODE="0666"
# Canon MF8300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2708", MODE="0666"
# Canon D500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2738", MODE="0666"
# Canon D530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2775", MODE="0666"
# Canon MF5630
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="264e", MODE="0666"
# Canon MF5650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="264f", MODE="0666"
# Canon MF8170c
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2659", MODE="0666"
# Canon MF8000C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2753", MODE="0666"
# Canon MF8000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2707", MODE="0666"
# Canon MF5880
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="26f9", MODE="0666"
# Canon MF6680
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="26fa", MODE="0666"
# Canon iR1133
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2742", MODE="0666"
# Canon MF5900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2743", MODE="0666"
# Canon MF8500C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="277a", MODE="0666"
# Canon MF6100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="278e", MODE="0666"
# Canon MF810/820
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27a6", MODE="0666"
# Canon MF220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27a8", MODE="0666"
# Canon MF210
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27a9", MODE="0666"
# Canon D1300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2744", MODE="0666"
# Canon MF6700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2745", MODE="0666"
# Canon MF8300C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2754", MODE="0666"
# Canon LC600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2772", MODE="0666"
# Canon MF6800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="278f", MODE="0666"
# Canon MF620C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27b4", MODE="0666"
# Canon MF720C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27b5", MODE="0666"
# Canon iR2002
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="278b", MODE="0666"
# Canon iR C1325
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27a5", MODE="0666"
# Canon iR1435
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27aa", MODE="0666"
# Canon iR C1225
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27b2", MODE="0666"
# Canon MF410
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27c0", MODE="0666"
# Canon MF510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27c2", MODE="0666"
# Canon MF417
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27c3", MODE="0666"
# Canon iR2004
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27cb", MODE="0666"
# Canon MF230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27d1", MODE="0666"
# Canon MF240
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27d2", MODE="0666"
# Canon iR C3020
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27df", MODE="0666"
# Canon iR C3025
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27e0", MODE="0666"
# Canon MF633C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27e1", MODE="0666"
# Canon MF632C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27e2", MODE="0666"
# Canon MF631C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27e3", MODE="0666"
# Canon MF732C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27e4", MODE="0666"
# Canon MF731C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27e5", MODE="0666"
# Canon D570
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27e8", MODE="0666"
# Canon MF110/910
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27ed", MODE="0666"
# Canon MF520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27f0", MODE="0666"
# Canon MF420
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27f1", MODE="0666"
# Canon MF260
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27f4", MODE="0666"
# Canon iR2006/2206
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27f9", MODE="0666"
# Canon MF745/746
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27fa", MODE="0666"
# Canon MF742/744
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27fb", MODE="0666"
# Canon MF741/743
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27fc", MODE="0666"
# Canon MF645
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27fd", MODE="0666"
# Canon MF642/643/644
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27fe", MODE="0666"
# Canon MF641
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="27ff", MODE="0666"
# Canon MF745
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2800", MODE="0666"
# Canon MF642/644
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2801", MODE="0666"
# Canon D1600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2804", MODE="0666"
# Canon MF440
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2823", MODE="0666"
# Canon MF540
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2824", MODE="0666"
# Canon iR1643
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2826", MODE="0666"
# Canon iR C3125
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2829", MODE="0666"
# Canon iR C3120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2830", MODE="0666"
# Canon iR C3120L
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2831", MODE="0666"
# Canon MF1127C/C1127
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2854", MODE="0666"
# Canon MF1238/1238
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2856", MODE="0666"
# Canon MF650C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="287b", MODE="0666"
# Canon MF1643 II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="287d", MODE="0666"
# Canon iR1643 II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="287e", MODE="0666"
# Canon MF550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="287f", MODE="0666"
# Canon MF1238II/1238II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2880", MODE="0666"
# Canon MF450
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2881", MODE="0666"
# Canon MF1333
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2884", MODE="0666"
# Canon MF750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2885", MODE="0666"
# Canon MF270
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2892", MODE="0666"
# Canon MF755
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2894", MODE="0666"
# Canon MF751
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="2895", MODE="0666"
# Canon MF260II
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="289a", MODE="0666"
# Canon MF1440
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="28b4", MODE="0666"
# Canon MF460
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04a9", ATTRS{idProduct}=="28b5", MODE="0666"
# Brother MFC-8600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0100", MODE="0666"
# Brother MFC-9600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0101", MODE="0666"
# Brother MFC-9750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0102", MODE="0666"
# Brother MFC-8300J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0104", MODE="0666"
# Brother MFC-9600J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0105", MODE="0666"
# Brother MFC-7300C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0106", MODE="0666"
# Brother MFC-7400C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0107", MODE="0666"
# Brother MFC-9200C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0108", MODE="0666"
# Brother MFC-830
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0109", MODE="0666"
# Brother MFC-840
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="010a", MODE="0666"
# Brother MFC-860
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="010b", MODE="0666"
# Brother MFC-7400J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="010c", MODE="0666"
# Brother MFC-9200J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="010d", MODE="0666"
# Brother MFC-3100C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="010e", MODE="0666"
# Brother MFC-5100C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="010f", MODE="0666"
# Brother MFC-4800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0110", MODE="0666"
# Brother MFC-6800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0111", MODE="0666"
# Brother DCP-1000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0112", MODE="0666"
# Brother MFC-8500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0113", MODE="0666"
# Brother MFC-9700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0114", MODE="0666"
# Brother MFC-9800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0115", MODE="0666"
# Brother DCP-1400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0116", MODE="0666"
# Brother MFC-9660
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0119", MODE="0666"
# Brother MFC-9860
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="011a", MODE="0666"
# Brother MFC-9880
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="011b", MODE="0666"
# Brother MFC-9760
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="011c", MODE="0666"
# Brother MFC-9070
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="011d", MODE="0666"
# Brother MFC-9180
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="011e", MODE="0666"
# Brother MFC-9160
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="011f", MODE="0666"
# Brother MFC-580
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0120", MODE="0666"
# Brother MFC-590
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0121", MODE="0666"
# Brother MFC-5100J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0122", MODE="0666"
# Brother MFC-4800J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0124", MODE="0666"
# Brother MFC-6800J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0125", MODE="0666"
# Brother MFC-9800J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0127", MODE="0666"
# Brother MFC-8500J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0128", MODE="0666"
# Brother MFC-9030
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="012b", MODE="0666"
# Brother FAX-4100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="012e", MODE="0666"
# Brother FAX-4750e
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="012f", MODE="0666"
# Brother FAX-5750e
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0130", MODE="0666"
# Brother MFC-5200C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0132", MODE="0666"
# Brother MFC-100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0135", MODE="0666"
# Brother MFC-150CL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0136", MODE="0666"
# Brother MFC-3200C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="013a", MODE="0666"
# Brother MFC-9880
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="013b", MODE="0666"
# Brother MFC-890
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="013c", MODE="0666"
# Brother MFC-5200J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="013d", MODE="0666"
# Brother MFC-4420C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="013e", MODE="0666"
# Brother MFC-4820C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="013f", MODE="0666"
# Brother DCP-8020
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0140", MODE="0666"
# Brother DCP-8025D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0141", MODE="0666"
# Brother MFC-8420
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0142", MODE="0666"
# Brother MFC-8820D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0143", MODE="0666"
# Brother DCP-4020C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0144", MODE="0666"
# Brother MFC-3220C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0146", MODE="0666"
# Brother MFC-3320CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0148", MODE="0666"
# Brother MFC-3420C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="014a", MODE="0666"
# Brother MFC-3820CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="014b", MODE="0666"
# Brother DCP-3020C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="014c", MODE="0666"
# Brother MFC-8820J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="014e", MODE="0666"
# Brother DCP-8025J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="014f", MODE="0666"
# Brother MFC-8220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0150", MODE="0666"
# Brother MFC-8210J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0151", MODE="0666"
# Brother DCP-1000J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0153", MODE="0666"
# Brother MFC-3420J
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0157", MODE="0666"
# Brother MFC-3820JN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0158", MODE="0666"
# Brother DCP-8040
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="015d", MODE="0666"
# Brother DCP-8045D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="015e", MODE="0666"
# Brother MFC-8440
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="015f", MODE="0666"
# Brother MFC-8840D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0160", MODE="0666"
# Brother MFC-210C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0161", MODE="0666"
# Brother MFC-420CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0162", MODE="0666"
# Brother MFC-410CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0163", MODE="0666"
# Brother MFC-620CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0165", MODE="0666"
# Brother MFC-610CLN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0166", MODE="0666"
# Brother MFC-620CLN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0168", MODE="0666"
# Brother DCP-110C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0169", MODE="0666"
# Brother DCP-310CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="016b", MODE="0666"
# Brother MFC-5440CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="016d", MODE="0666"
# Brother MFC-5840CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="016e", MODE="0666"
# Brother MFC-3240C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0173", MODE="0666"
# Brother MFC-3340CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0174", MODE="0666"
# Brother MFC-7420
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0180", MODE="0666"
# Brother MFC-7820N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0181", MODE="0666"
# Brother DCP-7010
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0182", MODE="0666"
# Brother DCP-7020
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0183", MODE="0666"
# Brother DCP-7025
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0184", MODE="0666"
# Brother MFC-7220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0185", MODE="0666"
# Brother MFC-7225N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0186", MODE="0666"
# Brother MFC-9420CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="018a", MODE="0666"
# Brother DCP-115C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="018c", MODE="0666"
# Brother DCP-116C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="018d", MODE="0666"
# Brother DCP-117C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="018e", MODE="0666"
# Brother DCP-118C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="018f", MODE="0666"
# Brother DCP-120C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0190", MODE="0666"
# Brother DCP-315CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0191", MODE="0666"
# Brother DCP-340CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0192", MODE="0666"
# Brother MFC-215C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0193", MODE="0666"
# Brother MFC-425CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0194", MODE="0666"
# Brother MFC-820CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0195", MODE="0666"
# Brother MFC-820CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0196", MODE="0666"
# Brother MFC-640CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0197", MODE="0666"
# Brother MFC-615CL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0198", MODE="0666"
# Brother MFC-830CLN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0199", MODE="0666"
# Brother MFC-840CLN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="019a", MODE="0666"
# Brother MFC-8640D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01a2", MODE="0666"
# Brother DCP-8060
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01a3", MODE="0666"
# Brother DCP-8065DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01a4", MODE="0666"
# Brother MFC-8460N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01a5", MODE="0666"
# Brother MFC-8860DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01a6", MODE="0666"
# Brother MFC-8870DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01a7", MODE="0666"
# Brother DCP-130C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01a8", MODE="0666"
# Brother DCP-330C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01a9", MODE="0666"
# Brother DCP-540CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01aa", MODE="0666"
# Brother MFC-240C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01ab", MODE="0666"
# Brother DCP-750CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01ae", MODE="0666"
# Brother MFC-440CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01af", MODE="0666"
# Brother MFC-660CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01b0", MODE="0666"
# Brother MFC-665CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01b1", MODE="0666"
# Brother MFC-845CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01b2", MODE="0666"
# Brother MFC-460CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01b4", MODE="0666"
# Brother MFC-630CD
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01b5", MODE="0666"
# Brother MFC-850CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01b6", MODE="0666"
# Brother MFC-5460CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01b7", MODE="0666"
# Brother MFC-5860CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01b8", MODE="0666"
# Brother MFC-3360C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01ba", MODE="0666"
# Brother MFC-8660DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01bd", MODE="0666"
# Brother DCP-750CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01be", MODE="0666"
# Brother MFC-860CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01bf", MODE="0666"
# Brother DCP-128C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01c0", MODE="0666"
# Brother DCP-129C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01c1", MODE="0666"
# Brother DCP-131C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01c2", MODE="0666"
# Brother DCP-329C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01c3", MODE="0666"
# Brother DCP-331C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01c4", MODE="0666"
# Brother MFC-239C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01c5", MODE="0666"
# Brother DCP-9040CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01c9", MODE="0666"
# Brother MFC-9440CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01ca", MODE="0666"
# Brother DCP-9045CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01cb", MODE="0666"
# Brother MFC-9840CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01cc", MODE="0666"
# Brother DCP-135C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01ce", MODE="0666"
# Brother DCP-150C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01cf", MODE="0666"
# Brother DCP-350C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01d0", MODE="0666"
# Brother DCP-560CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01d1", MODE="0666"
# Brother DCP-770CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01d2", MODE="0666"
# Brother DCP-770CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01d3", MODE="0666"
# Brother MFC-230C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01d4", MODE="0666"
# Brother MFC-235C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01d5", MODE="0666"
# Brother MFC-260C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01d6", MODE="0666"
# Brother MFC-465CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01d7", MODE="0666"
# Brother MFC-680CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01d8", MODE="0666"
# Brother MFC-685CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01d9", MODE="0666"
# Brother MFC-885CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01da", MODE="0666"
# Brother MFC-480CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01db", MODE="0666"
# Brother MFC-650CD
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01dc", MODE="0666"
# Brother MFC-870CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01dd", MODE="0666"
# Brother MFC-880CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01de", MODE="0666"
# Brother DCP-155C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01df", MODE="0666"
# Brother MFC-265C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01e0", MODE="0666"
# Brother DCP-153C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01e1", MODE="0666"
# Brother DCP-157C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01e2", MODE="0666"
# Brother DCP-353C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01e3", MODE="0666"
# Brother DCP-357C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01e4", MODE="0666"
# Brother MFC-7840W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01e5", MODE="0666"
# Brother MFC-7440N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01e6", MODE="0666"
# Brother MFC-7340
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01e7", MODE="0666"
# Brother DCP-7045N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01e8", MODE="0666"
# Brother DCP-7040
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01e9", MODE="0666"
# Brother DCP-7030
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01ea", MODE="0666"
# Brother MFC-7320
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01eb", MODE="0666"
# Brother MFC-9640CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01ec", MODE="0666"
# Brother MFC-7840N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01ed", MODE="0666"
# Brother MFC-7450
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01ee", MODE="0666"
# Brother MFC-6890CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01ef", MODE="0666"
# Brother MFC-6890CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01f0", MODE="0666"
# Brother DCP-6690CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01f1", MODE="0666"
# Brother MFC-6490CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01f2", MODE="0666"
# Brother MFC-6490CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01f3", MODE="0666"
# Brother MFC-5890CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01f4", MODE="0666"
# Brother MFC-5490CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01f5", MODE="0666"
# Brother MFC-930CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01f6", MODE="0666"
# Brother MFC-670CD
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01f7", MODE="0666"
# Brother MFC-990CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01f8", MODE="0666"
# Brother MFC-790CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01f9", MODE="0666"
# Brother MFC-490CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01fa", MODE="0666"
# Brother MFC-490CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01fb", MODE="0666"
# Brother MFC-297C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01fc", MODE="0666"
# Brother MFC-290C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01fd", MODE="0666"
# Brother MFC-250C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01fe", MODE="0666"
# Brother DCP-535CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="01ff", MODE="0666"
# Brother DCP-585CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0200", MODE="0666"
# Brother DCP-385C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0201", MODE="0666"
# Brother DCP-387C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0202", MODE="0666"
# Brother DCP-383C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0203", MODE="0666"
# Brother DCP-165C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0204", MODE="0666"
# Brother DCP-185C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0205", MODE="0666"
# Brother DCP-145C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0206", MODE="0666"
# Brother DCP-163C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0207", MODE="0666"
# Brother DCP-167C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0208", MODE="0666"
# Brother MFC-8670DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="020a", MODE="0666"
# Brother DCP-9042CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="020c", MODE="0666"
# Brother MFC-9450CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="020d", MODE="0666"
# Brother MFC-7345N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0214", MODE="0666"
# Brother MFC-8890DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0215", MODE="0666"
# Brother MFC-8880DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0216", MODE="0666"
# Brother MFC-8480DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0217", MODE="0666"
# Brother DCP-8080DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0218", MODE="0666"
# Brother MFC-8380DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0219", MODE="0666"
# Brother MFC-8370DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="021a", MODE="0666"
# Brother DCP-8070D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="021b", MODE="0666"
# Brother MFC-9320CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="021c", MODE="0666"
# Brother MFC-9120CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="021d", MODE="0666"
# Brother DCP-9010CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="021e", MODE="0666"
# Brother DCP-8085DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="021f", MODE="0666"
# Brother MFC-9010CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0220", MODE="0666"
# Brother DCP-175C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0221", MODE="0666"
# Brother DCP-195C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0222", MODE="0666"
# Brother DCP-365CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0223", MODE="0666"
# Brother DCP-375CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0224", MODE="0666"
# Brother DCP-395CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0225", MODE="0666"
# Brother DCP-595CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0226", MODE="0666"
# Brother DCP-595CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0227", MODE="0666"
# Brother MFC-255CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0228", MODE="0666"
# Brother MFC-295CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0229", MODE="0666"
# Brother MFC-495CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="022a", MODE="0666"
# Brother MFC-495CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="022b", MODE="0666"
# Brother MFC-795CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="022c", MODE="0666"
# Brother MFC-675CD
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="022d", MODE="0666"
# Brother MFC-695CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="022e", MODE="0666"
# Brother MFC-735CD
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="022f", MODE="0666"
# Brother MFC-935CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0230", MODE="0666"
# Brother DCP-173C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0231", MODE="0666"
# Brother DCP-177C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0232", MODE="0666"
# Brother DCP-190C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0233", MODE="0666"
# Brother DCP-373CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0234", MODE="0666"
# Brother DCP-377CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0235", MODE="0666"
# Brother DCP-390CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0236", MODE="0666"
# Brother DCP-593CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0237", MODE="0666"
# Brother DCP-597CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0238", MODE="0666"
# Brother MFC-253CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0239", MODE="0666"
# Brother MFC-257CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="023a", MODE="0666"
# Brother DCP-191C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="023b", MODE="0666"
# Brother DCP-391CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="023c", MODE="0666"
# Brother DCP-193C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="023d", MODE="0666"
# Brother DCP-197C
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="023e", MODE="0666"
# Brother MFC-8680DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="023f", MODE="0666"
# Brother MFC-J950DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0240", MODE="0666"
# Brother DCP-9055CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0241", MODE="0666"
# Brother DCP-9270CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0242", MODE="0666"
# Brother MFC-9460CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0243", MODE="0666"
# Brother MFC-9465CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0244", MODE="0666"
# Brother MFC-9560CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0245", MODE="0666"
# Brother MFC-9970CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0246", MODE="0666"
# Brother DCP-7055
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0248", MODE="0666"
# Brother DCP-7060D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0249", MODE="0666"
# Brother DCP-7065DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="024a", MODE="0666"
# Brother MFC-7560D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="024b", MODE="0666"
# Brother MFC-7860DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="024c", MODE="0666"
# Brother MFC-7360
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="024d", MODE="0666"
# Brother MFC-7460DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="024e", MODE="0666"
# Brother MFC-7860DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="024f", MODE="0666"
# Brother MFC-7055
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0250", MODE="0666"
# Brother MFC-7060D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0251", MODE="0666"
# Brother MFC-7065DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0252", MODE="0666"
# Brother DCP-J125
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0253", MODE="0666"
# Brother DCP-J315W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0254", MODE="0666"
# Brother DCP-J515W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0255", MODE="0666"
# Brother DCP-J515N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0256", MODE="0666"
# Brother DCP-J715W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0257", MODE="0666"
# Brother DCP-J715N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0258", MODE="0666"
# Brother MFC-J220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0259", MODE="0666"
# Brother MFC-J410
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="025a", MODE="0666"
# Brother MFC-J265W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="025b", MODE="0666"
# Brother MFC-J415W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="025c", MODE="0666"
# Brother MFC-J615W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="025d", MODE="0666"
# Brother MFC-J615N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="025e", MODE="0666"
# Brother MFC-J700D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="025f", MODE="0666"
# Brother MFC-J800D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0260", MODE="0666"
# Brother MFC-J850DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0261", MODE="0666"
# Brother MFC-J6510DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0262", MODE="0666"
# Brother MFC-J6710DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0263", MODE="0666"
# Brother MFC-J6310W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0264", MODE="0666"
# Brother MFC-J6510CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0265", MODE="0666"
# Brother MFC-J6710CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0266", MODE="0666"
# Brother MFC-J6910DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0267", MODE="0666"
# Brother MFC-J6910CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0268", MODE="0666"
# Brother MFC-J630W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="026b", MODE="0666"
# Brother MFC-J705D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="026c", MODE="0666"
# Brother MFC-J805D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="026d", MODE="0666"
# Brother MFC-J855DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="026e", MODE="0666"
# Brother MFC-J270W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="026f", MODE="0666"
# Brother MFC-7360N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0270", MODE="0666"
# Brother MFC-7470D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0271", MODE="0666"
# Brother HL-2280DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0272", MODE="0666"
# Brother DCP-7057
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0273", MODE="0666"
# Brother MFC-7362
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0274", MODE="0666"
# Brother FAX-7860DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0275", MODE="0666"
# Brother MFC-5895CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0276", MODE="0666"
# Brother DCP-7070DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0277", MODE="0666"
# Brother MFC-J410W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0278", MODE="0666"
# Brother DCP-J525W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0279", MODE="0666"
# Brother DCP-J525N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="027a", MODE="0666"
# Brother DCP-J725DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="027b", MODE="0666"
# Brother DCP-J725N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="027c", MODE="0666"
# Brother DCP-J925DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="027d", MODE="0666"
# Brother MFC-J955DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="027e", MODE="0666"
# Brother MFC-J280W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="027f", MODE="0666"
# Brother MFC-J435W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0280", MODE="0666"
# Brother MFC-J430W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0281", MODE="0666"
# Brother MFC-J625DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0282", MODE="0666"
# Brother MFC-J825DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0283", MODE="0666"
# Brother MFC-J825N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0284", MODE="0666"
# Brother MFC-J705D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0285", MODE="0666"
# Brother MFC-J810D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0286", MODE="0666"
# Brother MFC-J860DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0287", MODE="0666"
# Brother MFC-J5910DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0288", MODE="0666"
# Brother MFC-J5910CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0289", MODE="0666"
# Brother DCP-J925N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="028a", MODE="0666"
# Brother MFC-7362N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="028b", MODE="0666"
# Brother MFC-J635DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="028c", MODE="0666"
# Brother MFC-J835DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="028d", MODE="0666"
# Brother MFC-J275W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="028e", MODE="0666"
# Brother MFC-J425W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="028f", MODE="0666"
# Brother MFC-J432W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0290", MODE="0666"
# Brother DCP-8110DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0291", MODE="0666"
# Brother DCP-8150DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0292", MODE="0666"
# Brother DCP-8155DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0293", MODE="0666"
# Brother DCP-8250DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0294", MODE="0666"
# Brother MFC-8510DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0295", MODE="0666"
# Brother MFC-8520DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0296", MODE="0666"
# Brother MFC-8710DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0297", MODE="0666"
# Brother MFC-8910DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0298", MODE="0666"
# Brother MFC-8950DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0299", MODE="0666"
# Brother MFC-8690DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="029a", MODE="0666"
# Brother FAX-2810N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="029b", MODE="0666"
# Brother MFC-8515DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="029c", MODE="0666"
# Brother MFC-9125CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="029e", MODE="0666"
# Brother MFC-9325CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="029f", MODE="0666"
# Brother DCP-J140W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02a0", MODE="0666"
# Brother DCP-9015CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02a1", MODE="0666"
# Brother MFC-9015CN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02a2", MODE="0666"
# Brother FAX-2840
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02a3", MODE="0666"
# Brother FAX-2845
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02a4", MODE="0666"
# Brother MFC-7240
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02a5", MODE="0666"
# Brother FAX-2940
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02a6", MODE="0666"
# Brother FAX-2950
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02a7", MODE="0666"
# Brother MFC-7290
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02a8", MODE="0666"
# Brother FAX-2890
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02aa", MODE="0666"
# Brother FAX-2990
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02ab", MODE="0666"
# Brother DCP-8110D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02ac", MODE="0666"
# Brother MFC-9130CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02ad", MODE="0666"
# Brother MFC-9140CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02ae", MODE="0666"
# Brother MFC-9330CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02af", MODE="0666"
# Brother MFC-9340CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02b0", MODE="0666"
# Brother DCP-9020CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02b1", MODE="0666"
# Brother MFC-J810DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02b2", MODE="0666"
# Brother MFC-J4510DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02b3", MODE="0666"
# Brother MFC-J4710DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02b4", MODE="0666"
# Brother DCP-8112DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02b5", MODE="0666"
# Brother DCP-8152DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02b6", MODE="0666"
# Brother DCP-8157DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02b7", MODE="0666"
# Brother MFC-8512DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02b8", MODE="0666"
# Brother MFC-8712DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02b9", MODE="0666"
# Brother MFC-8912DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02ba", MODE="0666"
# Brother MFC-8952DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02bb", MODE="0666"
# Brother DCP-J540N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02bc", MODE="0666"
# Brother DCP-J740N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02bd", MODE="0666"
# Brother MFC-J710D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02be", MODE="0666"
# Brother MFC-J840N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02bf", MODE="0666"
# Brother DCP-J940N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02c0", MODE="0666"
# Brother MFC-J960DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02c1", MODE="0666"
# Brother DCP-J4110DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02c2", MODE="0666"
# Brother MFC-J4310DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02c3", MODE="0666"
# Brother MFC-J4410DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02c4", MODE="0666"
# Brother MFC-J4610DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02c5", MODE="0666"
# Brother DCP-J4210N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02c6", MODE="0666"
# Brother MFC-J4510N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02c7", MODE="0666"
# Brother MFC-J4910CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02c8", MODE="0666"
# Brother MFC-J4810DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02c9", MODE="0666"
# Brother MFC-8712DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02ca", MODE="0666"
# Brother MFC-8710DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02cb", MODE="0666"
# Brother MFC-J2310
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02cc", MODE="0666"
# Brother MFC-J2510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02cd", MODE="0666"
# Brother DCP-7055W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02ce", MODE="0666"
# Brother DCP-7057W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02cf", MODE="0666"
# Brother DCP-1510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02d0", MODE="0666"
# Brother MFC-1810
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02d1", MODE="0666"
# Brother DCP-9020CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02d3", MODE="0666"
# Brother MFC-8810DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02d4", MODE="0666"
# Brother MFC-J4305DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02d5", MODE="0666"
# Brother MFC-J4315DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02d6", MODE="0666"
# Brother MFC-J4405DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02d7", MODE="0666"
# Brother MFC-J4415DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02d8", MODE="0666"
# Brother MFC-J4505DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02d9", MODE="0666"
# Brother MFC-J4515DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02da", MODE="0666"
# Brother MFC-J4605DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02db", MODE="0666"
# Brother MFC-J4615DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02dc", MODE="0666"
# Brother DCP-J4215N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02dd", MODE="0666"
# Brother DCP-J132W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02de", MODE="0666"
# Brother DCP-J152W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02df", MODE="0666"
# Brother DCP-J152N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02e0", MODE="0666"
# Brother DCP-J172W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02e1", MODE="0666"
# Brother DCP-J552DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02e2", MODE="0666"
# Brother DCP-J552N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02e3", MODE="0666"
# Brother DCP-J752DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02e4", MODE="0666"
# Brother DCP-J752N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02e5", MODE="0666"
# Brother DCP-J952N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02e6", MODE="0666"
# Brother MFC-J245
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02e7", MODE="0666"
# Brother MFC-J470DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02e8", MODE="0666"
# Brother MFC-J475DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02e9", MODE="0666"
# Brother MFC-J285DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02ea", MODE="0666"
# Brother MFC-J650DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02eb", MODE="0666"
# Brother MFC-J870DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02ec", MODE="0666"
# Brother MFC-J870N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02ed", MODE="0666"
# Brother MFC-J720D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02ee", MODE="0666"
# Brother MFC-J820DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02ef", MODE="0666"
# Brother MFC-J980DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02f0", MODE="0666"
# Brother MFC-J890DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02f1", MODE="0666"
# Brother MFC-J6520DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02f2", MODE="0666"
# Brother MFC-J6570CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02f3", MODE="0666"
# Brother MFC-J6720DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02f4", MODE="0666"
# Brother MFC-J6920DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02f5", MODE="0666"
# Brother MFC-J6970CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02f6", MODE="0666"
# Brother MFC-J6975CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02f7", MODE="0666"
# Brother MFC-J6770CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02f8", MODE="0666"
# Brother DCP-J132N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02f9", MODE="0666"
# Brother MFC-J450DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02fa", MODE="0666"
# Brother MFC-J875DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02fb", MODE="0666"
# Brother DCP-J100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02fc", MODE="0666"
# Brother DCP-J105
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02fd", MODE="0666"
# Brother MFC-J200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02fe", MODE="0666"
# Brother MFC-J3520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="02ff", MODE="0666"
# Brother MFC-J3720
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0300", MODE="0666"
# Brother ADS-1050W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="030a", MODE="0666"
# Brother ADS-1150W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="030b", MODE="0666"
# Brother ADS-1550W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="030c", MODE="0666"
# Brother ADS-1650W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="030d", MODE="0666"
# Brother DCP-J112
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="030e", MODE="0666"
# Brother DCP-L8400CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="030f", MODE="0666"
# Brother DCP-L8450CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0310", MODE="0666"
# Brother MFC-L8600CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0311", MODE="0666"
# Brother MFC-L8650CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0312", MODE="0666"
# Brother MFC-L8850CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0313", MODE="0666"
# Brother MFC-L9550CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0314", MODE="0666"
# Brother MFC-7365DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0318", MODE="0666"
# Brother MFC-L2740DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0320", MODE="0666"
# Brother DCP-L2500D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0321", MODE="0666"
# Brother DCP-L2520DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0322", MODE="0666"
# Brother DCP-L2520D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0324", MODE="0666"
# Brother DCP-L2540DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0326", MODE="0666"
# Brother DCP-L2540DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0328", MODE="0666"
# Brother DCP-L2560DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0329", MODE="0666"
# Brother HL-L2380DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0330", MODE="0666"
# Brother MFC-L2700DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0331", MODE="0666"
# Brother FAX-L2700DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0335", MODE="0666"
# Brother MFC-L2720DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0337", MODE="0666"
# Brother MFC-L2720DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0338", MODE="0666"
# Brother DCP-J4120DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0339", MODE="0666"
# Brother MFC-J4320DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="033a", MODE="0666"
# Brother MFC-J2320
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="033c", MODE="0666"
# Brother MFC-J4420DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="033d", MODE="0666"
# Brother MFC-J4620DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0340", MODE="0666"
# Brother MFC-J2720
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0341", MODE="0666"
# Brother MFC-J4625DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0342", MODE="0666"
# Brother MFC-J5320DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0343", MODE="0666"
# Brother MFC-J5620DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0346", MODE="0666"
# Brother MFC-J5720DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0347", MODE="0666"
# Brother DCP-J4220N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0349", MODE="0666"
# Brother DCP-J4225N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="034a", MODE="0666"
# Brother MFC-J4720N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="034b", MODE="0666"
# Brother MFC-J4725N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="034c", MODE="0666"
# Brother MFC-J5720CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="034e", MODE="0666"
# Brother MFC-J5820DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="034f", MODE="0666"
# Brother MFC-J5620CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0350", MODE="0666"
# Brother DCP-J137N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0351", MODE="0666"
# Brother DCP-J157N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0352", MODE="0666"
# Brother DCP-J557N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0353", MODE="0666"
# Brother DCP-J757N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0354", MODE="0666"
# Brother DCP-J957N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0355", MODE="0666"
# Brother MFC-J877N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0356", MODE="0666"
# Brother MFC-J727D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0357", MODE="0666"
# Brother MFC-J987DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0358", MODE="0666"
# Brother MFC-J827DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0359", MODE="0666"
# Brother MFC-J897DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="035a", MODE="0666"
# Brother DCP-1610W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="035b", MODE="0666"
# Brother DCP-1610NW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="035c", MODE="0666"
# Brother MFC-1910W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="035d", MODE="0666"
# Brother MFC-1910NW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="035e", MODE="0666"
# Brother DCP-1618W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0360", MODE="0666"
# Brother MFC-1919NW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0361", MODE="0666"
# Brother MFC-J5625DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0364", MODE="0666"
# Brother MFC-J4520DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0365", MODE="0666"
# Brother MFC-J5520DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0366", MODE="0666"
# Brother DCP-7080D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0367", MODE="0666"
# Brother DCP-7080
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0368", MODE="0666"
# Brother DCP-7180DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0369", MODE="0666"
# Brother DCP-7189DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="036a", MODE="0666"
# Brother MFC-7380
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="036b", MODE="0666"
# Brother MFC-7480D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="036c", MODE="0666"
# Brother MFC-7880DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="036d", MODE="0666"
# Brother MFC-7889DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="036e", MODE="0666"
# Brother DCP-9022CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="036f", MODE="0666"
# Brother MFC-9142CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0370", MODE="0666"
# Brother MFC-9332CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0371", MODE="0666"
# Brother MFC-9342CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0372", MODE="0666"
# Brother MFC-L2700D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0373", MODE="0666"
# Brother PDS-5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0374", MODE="0666"
# Brother PDS-6000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0375", MODE="0666"
# Brother DCP-1600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0376", MODE="0666"
# Brother MFC-1900
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0377", MODE="0666"
# Brother DCP-1608
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0378", MODE="0666"
# Brother DCP-1619
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0379", MODE="0666"
# Brother MFC-1906
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="037a", MODE="0666"
# Brother MFC-1908
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="037b", MODE="0666"
# Brother ADS-2000e
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="037c", MODE="0666"
# Brother ADS-2100e
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="037d", MODE="0666"
# Brother ADS-2500We
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="037e", MODE="0666"
# Brother ADS-2600We
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="037f", MODE="0666"
# Brother DCP-J562DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0380", MODE="0666"
# Brother DCP-J562N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0381", MODE="0666"
# Brother DCP-J962N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0383", MODE="0666"
# Brother MFC-J480DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0384", MODE="0666"
# Brother MFC-J485DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0385", MODE="0666"
# Brother MFC-J460DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0386", MODE="0666"
# Brother MFC-J465DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0387", MODE="0666"
# Brother MFC-J680DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0388", MODE="0666"
# Brother MFC-J880DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0389", MODE="0666"
# Brother MFC-J885DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="038a", MODE="0666"
# Brother MFC-J880N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="038b", MODE="0666"
# Brother MFC-J730DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="038c", MODE="0666"
# Brother MFC-J990DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="038d", MODE="0666"
# Brother MFC-J830DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="038e", MODE="0666"
# Brother MFC-J900DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="038f", MODE="0666"
# Brother MFC-J5920DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0390", MODE="0666"
# Brother MFC-L2705DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0392", MODE="0666"
# Brother DCP-T300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0393", MODE="0666"
# Brother DCP-T500W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0394", MODE="0666"
# Brother DCP-T700W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0395", MODE="0666"
# Brother MFC-T800W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0396", MODE="0666"
# Brother DCP-J963N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0397", MODE="0666"
# Brother DCP-L5500D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0398", MODE="0666"
# Brother DCP-L5500DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0399", MODE="0666"
# Brother DCP-L5502DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="039a", MODE="0666"
# Brother DCP-L5600DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="039b", MODE="0666"
# Brother DCP-L5602DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="039c", MODE="0666"
# Brother DCP-L5650DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="039d", MODE="0666"
# Brother DCP-L5652DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="039e", MODE="0666"
# Brother DCP-L6600DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="039f", MODE="0666"
# Brother MFC-L5700DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03a0", MODE="0666"
# Brother MFC-8530DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03a2", MODE="0666"
# Brother MFC-8535DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03a3", MODE="0666"
# Brother MFC-L5750DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03a5", MODE="0666"
# Brother MFC-8540DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03a6", MODE="0666"
# Brother MFC-L5800DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03a7", MODE="0666"
# Brother MFC-L5802DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03a8", MODE="0666"
# Brother MFC-L5850DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03a9", MODE="0666"
# Brother MFC-L5900DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03aa", MODE="0666"
# Brother MFC-L5902DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03ab", MODE="0666"
# Brother MFC-L6700DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03ac", MODE="0666"
# Brother MFC-L6702DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03ad", MODE="0666"
# Brother MFC-L6750DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03ae", MODE="0666"
# Brother MFC-L6800DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03af", MODE="0666"
# Brother MFC-L6900DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03b0", MODE="0666"
# Brother MFC-L6902DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03b1", MODE="0666"
# Brother MFC-L5755DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03b2", MODE="0666"
# Brother MFC-J6925DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03b3", MODE="0666"
# Brother MFC-J6573CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03b4", MODE="0666"
# Brother MFC-J6973CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03b5", MODE="0666"
# Brother MFC-J6990CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03b6", MODE="0666"
# Brother ADS-2400N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03b7", MODE="0666"
# Brother ADS-3000N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03b8", MODE="0666"
# Brother ADS-2800W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03b9", MODE="0666"
# Brother ADS-3600W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03ba", MODE="0666"
# Brother MFC-L2680W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03bb", MODE="0666"
# Brother MFC-L2700DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03bc", MODE="0666"
# Brother DCP-J762N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03bd", MODE="0666"
# Brother DCP-9017CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03bf", MODE="0666"
# Brother DCP-9015CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03c0", MODE="0666"
# Brother HL-3180CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03c1", MODE="0666"
# Brother PDS-5000F
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03c2", MODE="0666"
# Brother MFC-9335CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03c5", MODE="0666"
# Brother MFC-L5700DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03c6", MODE="0666"
# Brother MFC-L5702DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03c7", MODE="0666"
# Brother DCP-J785DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03c8", MODE="0666"
# Brother MFC-J985DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03c9", MODE="0666"
# Brother DCP-J983N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03ca", MODE="0666"
# Brother MFC-J6930DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03cb", MODE="0666"
# Brother MFC-J6935DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03cc", MODE="0666"
# Brother MFC-J6730DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03cd", MODE="0666"
# Brother MFC-J6580CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03cf", MODE="0666"
# Brother MFC-J6980CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03d0", MODE="0666"
# Brother MFC-J6995CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03d1", MODE="0666"
# Brother MFC-J5330DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03d3", MODE="0666"
# Brother MFC-J5730DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03d5", MODE="0666"
# Brother MFC-J5930DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03d6", MODE="0666"
# Brother MFC-J6530DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03e0", MODE="0666"
# Brother MFC-L2707DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03e1", MODE="0666"
# Brother MFC-J3530DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03e2", MODE="0666"
# Brother MFC-J3930DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03e3", MODE="0666"
# Brother MFC-J6535DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03e4", MODE="0666"
# Brother MFC-J5335DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03e5", MODE="0666"
# Brother MFC-J2330DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03e6", MODE="0666"
# Brother MFC-J2730DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03e7", MODE="0666"
# Brother MFC-J5830DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03e8", MODE="0666"
# Brother DCP-J567N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03ea", MODE="0666"
# Brother DCP-J767N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03eb", MODE="0666"
# Brother DCP-J968N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03ed", MODE="0666"
# Brother MFC-J737DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03ee", MODE="0666"
# Brother MFC-J837DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03ef", MODE="0666"
# Brother MFC-J887N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03f0", MODE="0666"
# Brother MFC-J907DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03f1", MODE="0666"
# Brother MFC-J997DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03f2", MODE="0666"
# Brother MFC-L9570CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03f3", MODE="0666"
# Brother MFC-L8900CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03f4", MODE="0666"
# Brother MFC-L8690CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03f5", MODE="0666"
# Brother MFC-L8610CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03f6", MODE="0666"
# Brother DCP-L8410CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03f7", MODE="0666"
# Brother MFC-J895DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03f8", MODE="0666"
# Brother MFC-J491DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03f9", MODE="0666"
# Brother MFC-L2685DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03fa", MODE="0666"
# Brother ADS-2200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03fb", MODE="0666"
# Brother ADS-2700W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03fd", MODE="0666"
# Brother DCP-J572DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03fe", MODE="0666"
# Brother DCP-J772DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="03ff", MODE="0666"
# Brother DCP-J774DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0400", MODE="0666"
# Brother MFC-J497DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0402", MODE="0666"
# Brother MFC-J890DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0403", MODE="0666"
# Brother MFC-J690DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0404", MODE="0666"
# Brother DCP-J572N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0405", MODE="0666"
# Brother DCP-J972N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0406", MODE="0666"
# Brother DCP-J973N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0407", MODE="0666"
# Brother MFC-J893N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0408", MODE="0666"
# Brother DCP-J1100DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0409", MODE="0666"
# Brother MFC-J995DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="040a", MODE="0666"
# Brother MFC-J1300DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="040b", MODE="0666"
# Brother DCP-J988N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="040d", MODE="0666"
# Brother MFC-J1500N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="040e", MODE="0666"
# Brother DCP-T310
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="040f", MODE="0666"
# Brother DCP-T510W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0410", MODE="0666"
# Brother DCP-T710W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0411", MODE="0666"
# Brother MFC-T810W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0412", MODE="0666"
# Brother MFC-T910DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0413", MODE="0666"
# Brother DCP-7090
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0417", MODE="0666"
# Brother DCP-7095D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0418", MODE="0666"
# Brother DCP-7190DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0419", MODE="0666"
# Brother DCP-7195DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="041a", MODE="0666"
# Brother DCP-B7500D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="041b", MODE="0666"
# Brother DCP-B7520DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="041c", MODE="0666"
# Brother DCP-B7530DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="041d", MODE="0666"
# Brother DCP-B7535DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="041e", MODE="0666"
# Brother DCP-L2510D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="041f", MODE="0666"
# Brother DCP-L2530DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0420", MODE="0666"
# Brother DCP-L2535D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0421", MODE="0666"
# Brother DCP-L2537DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0422", MODE="0666"
# Brother DCP-L2550DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0423", MODE="0666"
# Brother DCP-L2550DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0424", MODE="0666"
# Brother DCP-L2551DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0425", MODE="0666"
# Brother FAX-L2710DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0427", MODE="0666"
# Brother HL-L2390DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0428", MODE="0666"
# Brother HL-L2395DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0429", MODE="0666"
# Brother MFC-7390
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="042a", MODE="0666"
# Brother MFC-7490D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="042b", MODE="0666"
# Brother MFC-7890DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="042c", MODE="0666"
# Brother MFC-7895DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="042d", MODE="0666"
# Brother MFC-B7700D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="042e", MODE="0666"
# Brother MFC-B7715DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0430", MODE="0666"
# Brother MFC-B7720DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0431", MODE="0666"
# Brother MFC-L2690DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0432", MODE="0666"
# Brother MFC-L2710DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0433", MODE="0666"
# Brother MFC-L2710DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0434", MODE="0666"
# Brother MFC-L2713DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0435", MODE="0666"
# Brother MFC-L2715D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0436", MODE="0666"
# Brother MFC-L2717DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0437", MODE="0666"
# Brother MFC-L2730DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0438", MODE="0666"
# Brother MFC-L2730DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0439", MODE="0666"
# Brother MFC-L2750DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="043a", MODE="0666"
# Brother MFC-L2770DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="043b", MODE="0666"
# Brother MFC-L2715DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="043c", MODE="0666"
# Brother DCP-L2535DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="043d", MODE="0666"
# Brother MFC-J775DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="043e", MODE="0666"
# Brother MFC-L3770CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="043f", MODE="0666"
# Brother MFC-9350CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0440", MODE="0666"
# Brother MFC-L3750CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0441", MODE="0666"
# Brother MFC-L3745CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0442", MODE="0666"
# Brother MFC-L3735CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0443", MODE="0666"
# Brother MFC-9150CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0444", MODE="0666"
# Brother MFC-L3730CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0445", MODE="0666"
# Brother MFC-L3710CW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0446", MODE="0666"
# Brother DCP-9030CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0447", MODE="0666"
# Brother DCP-L3550CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0448", MODE="0666"
# Brother HL-L3290CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="044a", MODE="0666"
# Brother DCP-L3510CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="044b", MODE="0666"
# Brother DCP-L3551CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="044c", MODE="0666"
# Brother MFC-J5845DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="044d", MODE="0666"
# Brother MFC-J5945DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="044e", MODE="0666"
# Brother MFC-J6545DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="044f", MODE="0666"
# Brother MFC-J6945DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0450", MODE="0666"
# Brother MFC-J6947DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0451", MODE="0666"
# Brother MFC-T4500DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0454", MODE="0666"
# Brother MFC-J6997CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0456", MODE="0666"
# Brother MFC-J6999CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0457", MODE="0666"
# Brother ADS-1200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0459", MODE="0666"
# Brother ADS-1250W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="045a", MODE="0666"
# Brother ADS-1700W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="045b", MODE="0666"
# Brother MFC-J5630CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="045c", MODE="0666"
# Brother MFC-J6583CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="045d", MODE="0666"
# Brother MFC-J6983CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="045e", MODE="0666"
# Brother DCP-J577N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0460", MODE="0666"
# Brother DCP-J978N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0461", MODE="0666"
# Brother MFC-J898N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0462", MODE="0666"
# Brother MFC-J378DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0463", MODE="0666"
# Brother MFC-J998DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0464", MODE="0666"
# Brother MFC-J1605DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0465", MODE="0666"
# Brother MFC-J815DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0466", MODE="0666"
# Brother MFC-J805DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0467", MODE="0666"
# Brother MFC-L9670CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="046c", MODE="0666"
# Brother DCP-J582N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="046d", MODE="0666"
# Brother DCP-J981N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="046e", MODE="0666"
# Brother DCP-J982N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="046f", MODE="0666"
# Brother MFC-J903N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0470", MODE="0666"
# Brother DCP-J587N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0471", MODE="0666"
# Brother DCP-J987N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0472", MODE="0666"
# Brother DCP-T220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0474", MODE="0666"
# Brother DCP-T420W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0475", MODE="0666"
# Brother DCP-T520W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0476", MODE="0666"
# Brother DCP-T720DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0477", MODE="0666"
# Brother DCP-T820DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0478", MODE="0666"
# Brother MFC-T920DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0479", MODE="0666"
# Brother MFC-L9610CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="047f", MODE="0666"
# Brother MFC-L9630CDN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0480", MODE="0666"
# Brother MFC-EX670
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0482", MODE="0666"
# Brother MFC-J4740DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0484", MODE="0666"
# Brother MFC-J4535DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0485", MODE="0666"
# Brother MFC-J4540DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0486", MODE="0666"
# Brother MFC-J4335DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0487", MODE="0666"
# Brother MFC-J4340DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0488", MODE="0666"
# Brother MFC-J4345DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0489", MODE="0666"
# Brother DCP-J4140N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="048a", MODE="0666"
# Brother MFC-J4440N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="048c", MODE="0666"
# Brother MFC-J4540N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="048d", MODE="0666"
# Brother MFC-J4940DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0490", MODE="0666"
# Brother MFC-J1205W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0492", MODE="0666"
# Brother DCP-J1200W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0493", MODE="0666"
# Brother DCP-J1200N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0494", MODE="0666"
# Brother DCP-T225
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0496", MODE="0666"
# Brother DCP-T425W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0497", MODE="0666"
# Brother DCP-T525W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0498", MODE="0666"
# Brother DCP-T725DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0499", MODE="0666"
# Brother DCP-T825DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="049a", MODE="0666"
# Brother MFC-T925DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="049b", MODE="0666"
# Brother ADS-1190
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="049c", MODE="0666"
# Brother DCP-T428W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04a2", MODE="0666"
# Brother MFC-J4440DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04a3", MODE="0666"
# Brother MFC-J1215W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04a4", MODE="0666"
# Brother DCP-7090DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04a5", MODE="0666"
# Brother DCP-7190DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04a6", MODE="0666"
# Brother MFC-J1010DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04a7", MODE="0666"
# Brother MFC-J1012DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04a8", MODE="0666"
# Brother DCP-J1050DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04a9", MODE="0666"
# Brother MFC-J1170DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04ab", MODE="0666"
# Brother DCP-J1140DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04ac", MODE="0666"
# Brother DCP-J526N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04ad", MODE="0666"
# Brother DCP-J914N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04b1", MODE="0666"
# Brother DCP-J926N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04b2", MODE="0666"
# Brother MFC-J904N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04b7", MODE="0666"
# Brother MFC-J739DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04b9", MODE="0666"
# Brother MFC-J939DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04ba", MODE="0666"
# Brother MFC-J5340DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04bb", MODE="0666"
# Brother MFC-J5345DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04bc", MODE="0666"
# Brother MFC-J2340DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04bd", MODE="0666"
# Brother MFC-J5740DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04be", MODE="0666"
# Brother MFC-J2740DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04bf", MODE="0666"
# Brother MFC-J5855DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04c0", MODE="0666"
# Brother MFC-J5955DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04c1", MODE="0666"
# Brother MFC-J6540DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04c2", MODE="0666"
# Brother MFC-J3540DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04c3", MODE="0666"
# Brother MFC-J6740DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04c4", MODE="0666"
# Brother MFC-J6940DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04c5", MODE="0666"
# Brother MFC-J3940DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04c6", MODE="0666"
# Brother MFC-J6555DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04c7", MODE="0666"
# Brother MFC-J6955DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04c8", MODE="0666"
# Brother MFC-J6957DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04c9", MODE="0666"
# Brother MFC-J7100CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04cd", MODE="0666"
# Brother MFC-J7500CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04cf", MODE="0666"
# Brother MFC-J7600CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04d0", MODE="0666"
# Brother ADS-4900W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04d4", MODE="0666"
# Brother ADS-4700W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04d5", MODE="0666"
# Brother ADS-4300N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04d6", MODE="0666"
# Brother ADS-3300W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04d7", MODE="0666"
# Brother ADS-4500W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04d8", MODE="0666"
# Brother ADS-3100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04d9", MODE="0666"
# Brother ADS-4100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04da", MODE="0666"
# Brother MFC-L5705DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04e2", MODE="0666"
# Brother MFC-J5800CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04e3", MODE="0666"
# Brother MFC-B7710DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04e7", MODE="0666"
# Brother MFC-L5710DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04ec", MODE="0666"
# Brother MFC-L5915DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04ed", MODE="0666"
# Brother MFC-L6710DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04ee", MODE="0666"
# Brother MFC-L6910DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04ef", MODE="0666"
# Brother MFC-L6915DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04f0", MODE="0666"
# Brother DCP-L5660DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04f1", MODE="0666"
# Brother HL-L3300CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04f9", MODE="0666"
# Brother DCP-L3520CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04fa", MODE="0666"
# Brother DCP-L3560CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04fb", MODE="0666"
# Brother MFC-L3720CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04fc", MODE="0666"
# Brother MFC-L3740CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04fd", MODE="0666"
# Brother MFC-L3760CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04fe", MODE="0666"
# Brother MFC-L8340CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="04ff", MODE="0666"
# Brother MFC-L3780CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0500", MODE="0666"
# Brother MFC-L8390CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0501", MODE="0666"
# Brother DCP-L5510DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0509", MODE="0666"
# Brother DCP-L5510DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="050a", MODE="0666"
# Brother DCP-L5610DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="050b", MODE="0666"
# Brother DCP-L5518DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="050c", MODE="0666"
# Brother MFC-L5710DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="050d", MODE="0666"
# Brother MFC-L6810DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="050e", MODE="0666"
# Brother MFC-EX910
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="050f", MODE="0666"
# Brother MFC-EX915DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0510", MODE="0666"
# Brother MFC-L5718DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0511", MODE="0666"
# Brother MFC-L5728DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0512", MODE="0666"
# Brother DCP-L5512DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0513", MODE="0666"
# Brother MFC-J7300CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0515", MODE="0666"
# Brother MFC-L3755CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0516", MODE="0666"
# Brother DCP-L3528CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0519", MODE="0666"
# Brother DCP-L3568CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="051a", MODE="0666"
# Brother MFC-L3768CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="051b", MODE="0666"
# Brother MFC-L6912DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="051f", MODE="0666"
# Brother MFC-L5912DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0520", MODE="0666"
# Brother DCP-L5662DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0521", MODE="0666"
# Brother MFC-L6720DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0522", MODE="0666"
# Brother MFC-L6820DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0523", MODE="0666"
# Brother DCP-J1800N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0524", MODE="0666"
# Brother DCP-J1800DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0525", MODE="0666"
# Brother MFC-L2759DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0526", MODE="0666"
# Brother MFC-L5717DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="052b", MODE="0666"
# Brother MFC-L5715DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="052c", MODE="0666"
# Brother ADS-2700We
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="052e", MODE="0666"
# Brother ADS-2200e
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="052f", MODE="0666"
# Brother DCP-J1700DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0533", MODE="0666"
# Brother DCP-L3555CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0534", MODE="0666"
# Brother DCP-L3515CDW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0535", MODE="0666"
# Brother MFC-J1800DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="053b", MODE="0666"
# Brother FAX-L2800DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="053c", MODE="0666"
# Brother MFC-B7800DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="053d", MODE="0666"
# Brother MFC-B7810DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="053e", MODE="0666"
# Brother MFC-B7811DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="053f", MODE="0666"
# Brother MFC-L2760DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0540", MODE="0666"
# Brother MFC-L2800DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0542", MODE="0666"
# Brother MFC-L2802DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0543", MODE="0666"
# Brother MFC-L2802DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0544", MODE="0666"
# Brother MFC-L2805DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0545", MODE="0666"
# Brother MFC-L2806DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0546", MODE="0666"
# Brother MFC-L2807DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0547", MODE="0666"
# Brother MFC-L2820DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0549", MODE="0666"
# Brother MFC-L2820DWXL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="054a", MODE="0666"
# Brother MFC-L2860DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="054b", MODE="0666"
# Brother MFC-L2861DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="054c", MODE="0666"
# Brother MFC-L2862DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="054d", MODE="0666"
# Brother MFC-L2880DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0550", MODE="0666"
# Brother MFC-L2880DWXL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0551", MODE="0666"
# Brother MFC-L2885DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0553", MODE="0666"
# Brother MFC-L2900DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0555", MODE="0666"
# Brother MFC-L2920DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0556", MODE="0666"
# Brother MFC-L2922DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0557", MODE="0666"
# Brother DCP-B7548W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0558", MODE="0666"
# Brother DCP-B7558W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0559", MODE="0666"
# Brother DCP-B7578DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="055a", MODE="0666"
# Brother DCP-B7600D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="055b", MODE="0666"
# Brother DCP-B7608W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="055c", MODE="0666"
# Brother DCP-B7620DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="055d", MODE="0666"
# Brother DCP-B7628DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="055e", MODE="0666"
# Brother DCP-B7638DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="055f", MODE="0666"
# Brother DCP-B7640DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0560", MODE="0666"
# Brother DCP-B7648DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0561", MODE="0666"
# Brother DCP-B7650DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0562", MODE="0666"
# Brother DCP-B7658DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0563", MODE="0666"
# Brother DCP-L2508DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0564", MODE="0666"
# Brother DCP-L2518DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0565", MODE="0666"
# Brother DCP-L2548DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0566", MODE="0666"
# Brother DCP-L2600D
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0567", MODE="0666"
# Brother DCP-L2600DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0568", MODE="0666"
# Brother DCP-L2605DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="056a", MODE="0666"
# Brother DCP-L2620DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="056b", MODE="0666"
# Brother DCP-L2622DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="056c", MODE="0666"
# Brother DCP-L2625DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="056d", MODE="0666"
# Brother DCP-L2628DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="056e", MODE="0666"
# Brother DCP-L2640DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="056f", MODE="0666"
# Brother DCP-L2648DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0573", MODE="0666"
# Brother DCP-L2660DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0574", MODE="0666"
# Brother DCP-L2680DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0576", MODE="0666"
# Brother HL-L2464DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0577", MODE="0666"
# Brother HL-L2465DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0578", MODE="0666"
# Brother HL-L2480DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="057a", MODE="0666"
# Brother MFC-L2827DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0594", MODE="0666"
# Brother MFC-L2835DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0595", MODE="0666"
# Brother MFC-L2860DWE
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0596", MODE="0666"
# Brother MFC-L2900DWXL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0597", MODE="0666"
# Brother MFC-L2960DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0599", MODE="0666"
# Brother MFC-L2980DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="059a", MODE="0666"
# Brother DCP-L2665DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="059b", MODE="0666"
# Brother ADS-1300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0706", MODE="0666"
# Brother ADS-1350W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0707", MODE="0666"
# Brother ADS-1800W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0708", MODE="0666"
# Brother MFC-L2827DWXL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="070c", MODE="0666"
# Brother DCP-L2627DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="070d", MODE="0666"
# Brother DCP-L2627DWXL
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="070e", MODE="0666"
# Brother DCP-L2627DWE
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="070f", MODE="0666"
# Brother DCP-L2640DN
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0711", MODE="0666"
# Brother MFC-L2886DW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="0712", MODE="0666"
# Brother ADS-2000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="60a0", MODE="0666"
# Brother ADS-2100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="60a1", MODE="0666"
# Brother ADS-2500N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="60a2", MODE="0666"
# Brother ADS-2600N
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="60a3", MODE="0666"
# Brother ADS-2500W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="60a4", MODE="0666"
# Brother ADS-2600W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="60a5", MODE="0666"
# Brother ADS-1000W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="60a6", MODE="0666"
# Brother ADS-1100W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="60a7", MODE="0666"
# Brother ADS-1500W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="60a8", MODE="0666"
# Brother ADS-1600W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04f9", ATTRS{idProduct}=="60a9", MODE="0666"
# Samsung SCX-5315
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="340c", MODE="0666"
# Samsung SCX-6X20
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="340d", MODE="0666"
# Samsung SCX-4X20
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3412", MODE="0666"
# Samsung SCX-4100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3413", MODE="0666"
# Samsung SCX-4X21
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3419", MODE="0666"
# Samsung SCX-5x30
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="341a", MODE="0666"
# Samsung SCX-4200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="341b", MODE="0666"
# Samsung CLX-3160
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="341c", MODE="0666"
# Samsung SCX-6X22
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="341d", MODE="0666"
# Samsung SCX-4725
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="341f", MODE="0666"
# Samsung SCX-6X45
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3420", MODE="0666"
# Samsung CLX-8380
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3421", MODE="0666"
# Samsung CLX-216x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3425", MODE="0666"
# Samsung SCX-4500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3426", MODE="0666"
# Samsung CLX-6200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3427", MODE="0666"
# Samsung CLX-6240
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3428", MODE="0666"
# Samsung SCX-6X55
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3429", MODE="0666"
# Samsung CLX-3170
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="342a", MODE="0666"
# Samsung SCX-4500W
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="342b", MODE="0666"
# Samsung SCX-4x24
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="342c", MODE="0666"
# Samsung SCX-4x28
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="342d", MODE="0666"
# Samsung SCX-4300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="342e", MODE="0666"
# Samsung SCX-5835
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="342f", MODE="0666"
# Samsung SCX-5635
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3430", MODE="0666"
# Samsung CLX-9250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3431", MODE="0666"
# Samsung SCX-4x26
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3432", MODE="0666"
# Samsung SCX-4600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3433", MODE="0666"
# Samsung SCX-4623
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3434", MODE="0666"
# Samsung MFP-65X
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3435", MODE="0666"
# Samsung SCX-6545
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3437", MODE="0666"
# Samsung SCX-8030
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3438", MODE="0666"
# Samsung CLX-8385
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3439", MODE="0666"
# Samsung CLX-6220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="343a", MODE="0666"
# Samsung CLX-6250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="343b", MODE="0666"
# Samsung SCX-4x25
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="343c", MODE="0666"
# Samsung CLX-3180
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="343d", MODE="0666"
# Samsung CLX-8540
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="343f", MODE="0666"
# Samsung SCX-4623FW
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3440", MODE="0666"
# Samsung SCX-3200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3441", MODE="0666"
# Samsung SCX-6545X
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3442", MODE="0666"
# Samsung SCX-6X55X
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3443", MODE="0666"
# Samsung CLX-8385X
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3444", MODE="0666"
# Samsung CLX-8540X
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3445", MODE="0666"
# Samsung SCX-5835X
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3446", MODE="0666"
# Samsung SCX-3300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3449", MODE="0666"
# Samsung SCX-483x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="344b", MODE="0666"
# Samsung SCX-8123
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="344c", MODE="0666"
# Samsung CLX-92x1
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="344d", MODE="0666"
# Samsung CLX-8640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="344e", MODE="0666"
# Samsung SCX-3400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="344f", MODE="0666"
# Samsung SF-760
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3450", MODE="0666"
# Samsung SCX-472x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3453", MODE="0666"
# Samsung CLX-6260
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3455", MODE="0666"
# Samsung CLX-3300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3456", MODE="0666"
# Samsung SCX-470x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3457", MODE="0666"
# Samsung CLX-4190
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="345a", MODE="0666"
# Samsung SCX-4650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="345b", MODE="0666"
# Samsung CLX-9252
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="345c", MODE="0666"
# Samsung SCX-8230
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="345d", MODE="0666"
# Samsung SL-M337x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3460", MODE="0666"
# Samsung SL-M267x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3461", MODE="0666"
# Samsung SL-K2200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3467", MODE="0666"
# Samsung SL-C460
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3468", MODE="0666"
# Samsung SL-M2070
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3469", MODE="0666"
# Samsung SL-M288x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="346a", MODE="0666"
# Samsung SL-C1860
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="346b", MODE="0666"
# Samsung SL-C2670
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="346e", MODE="0666"
# Samsung SL-C145x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3471", MODE="0666"
# Samsung SL-C470
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3472", MODE="0666"
# Samsung SL-C268x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="347d", MODE="0666"
# Samsung SL-C48x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="347e", MODE="0666"
# Samsung SL-C3060
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3483", MODE="0666"
# Samsung SL-M306x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3484", MODE="0666"
# Samsung SCX-1450
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="392c", MODE="0666"
# Samsung SCX-1470
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3931", MODE="0666"
# Samsung SCX-1650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3932", MODE="0666"
# Samsung SCX-1455
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="3937", MODE="0666"
# Samsung SL-X4300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="04e8", ATTRS{idProduct}=="39ff", MODE="0666"
# Dell 1815dn
SUBSYSTEMS=="usb", ATTRS{idVendor}=="413c", ATTRS{idProduct}=="5124", MODE="0666"
# Dell 1600n
SUBSYSTEMS=="usb", ATTRS{idVendor}=="413c", ATTRS{idProduct}=="5220", MODE="0666"
# Dell B1265dfw
SUBSYSTEMS=="usb", ATTRS{idVendor}=="413c", ATTRS{idProduct}=="523b", MODE="0666"
# Dell B1165nfw
SUBSYSTEMS=="usb", ATTRS{idVendor}=="413c", ATTRS{idProduct}=="523c", MODE="0666"
# Dell B1163w
SUBSYSTEMS=="usb", ATTRS{idVendor}=="413c", ATTRS{idProduct}=="523d", MODE="0666"
# Dell B1163
SUBSYSTEMS=="usb", ATTRS{idVendor}=="413c", ATTRS{idProduct}=="523e", MODE="0666"
# Dell 1125
SUBSYSTEMS=="usb", ATTRS{idVendor}=="413c", ATTRS{idProduct}=="5302", MODE="0666"
# Dell 1235cn
SUBSYSTEMS=="usb", ATTRS{idVendor}=="413c", ATTRS{idProduct}=="5310", MODE="0666"
# Dell 2145cn
SUBSYSTEMS=="usb", ATTRS{idVendor}=="413c", ATTRS{idProduct}=="5311", MODE="0666"
# Dell 1135n
SUBSYSTEMS=="usb", ATTRS{idVendor}=="413c", ATTRS{idProduct}=="5318", MODE="0666"
# Dell 3333dn
SUBSYSTEMS=="usb", ATTRS{idVendor}=="413c", ATTRS{idProduct}=="5319", MODE="0666"
# Dell 1133
SUBSYSTEMS=="usb", ATTRS{idVendor}=="413c", ATTRS{idProduct}=="5321", MODE="0666"
# Dell 3335dn
SUBSYSTEMS=="usb", ATTRS{idVendor}=="413c", ATTRS{idProduct}=="5322", MODE="0666"
# Dell 2355dn
SUBSYSTEMS=="usb", ATTRS{idVendor}=="413c", ATTRS{idProduct}=="5323", MODE="0666"
# Xerox 3300MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="3cf1", MODE="0666"
# Xerox WC3315
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="3cf7", MODE="0666"
# Xerox WC3325
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="3cf8", MODE="0666"
# Xerox 6110MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="3d5d", MODE="0666"
# Xerox 6121MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="3d64", MODE="0666"
# Xerox 6121MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="3d66", MODE="0666"
# Xerox 3200MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="3da4", MODE="0666"
# Xerox WC4118
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="420c", MODE="0666"
# Xerox WCPE220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="420f", MODE="0666"
# Xerox WCPE120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="4237", MODE="0666"
# Xerox WCPE114E
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="423b", MODE="0666"
# Xerox WC3119
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="4265", MODE="0666"
# Xerox WC5016
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="4271", MODE="0666"
# Xerox WC3210
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="4293", MODE="0666"
# Xerox WC3220
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="4294", MODE="0666"
# Xerox WC3550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="4295", MODE="0666"
# Xerox WC3615
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="42c4", MODE="0666"
# Xerox WC5019
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="42c5", MODE="0666"
# Xerox WC3025
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="42da", MODE="0666"
# Xerox WC3215
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="42db", MODE="0666"
# Xerox WC3225
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0924", ATTRS{idProduct}=="42dc", MODE="0666"
# HP Deskjet 2050 J510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="8711", MODE="0666"
# HP Deskjet 1050 J410
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="8911", MODE="0666"
# HP Photosmart Plus B210
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="8e11", MODE="0666"
# HP Deskjet 3050 J610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="9311", MODE="0666"
# HP Envy 100 D410
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="9911", MODE="0666"
# HP Deskjet Ink Adv 2060 K110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="9a11", MODE="0666"
# HP Deskjet 3050A J611
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="a011", MODE="0666"
# HP Photosmart 5510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="a111", MODE="0666"
# HP Deskjet 3070 B611
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="a211", MODE="0666"
# HP Photosmart 6510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="a511", MODE="0666"
# HP Photosmart 7510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="a611", MODE="0666"
# HP ENVY 110
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="a711", MODE="0666"
# HP Officejet Pro X576dw
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ab11", MODE="0666"
# HP Deskjet 2510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ac11", MODE="0666"
# HP Deskjet 3510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ad11", MODE="0666"
# HP Photosmart 6520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="af11", MODE="0666"
# HP Deskjet 3520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="b011", MODE="0666"
# HP Photosmart 5520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="b111", MODE="0666"
# HP Photosmart 5510d
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="b411", MODE="0666"
# HP Deskjet 5520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="b611", MODE="0666"
# HP Deskjet 6520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ba11", MODE="0666"
# HP ENVY 120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="bb11", MODE="0666"
# HP Photosmart 7520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="bc11", MODE="0666"
# HP Deskjet 2520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="be11", MODE="0666"
# HP Officejet Pro X476dn
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="bf11", MODE="0666"
# HP Officejet Pro X476dw
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c011", MODE="0666"
# HP Deskjet 1510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c111", MODE="0666"
# HP Deskjet 2540
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c211", MODE="0666"
# HP ENVY 5530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c311", MODE="0666"
# HP Deskjet 4510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c411", MODE="0666"
# HP ENVY 4500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c511", MODE="0666"
# HP Officejet 4630
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c611", MODE="0666"
# HP Deskjet 3540
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c711", MODE="0666"
# HP Deskjet 4640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c811", MODE="0666"
# HP Officejet 2620
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c911", MODE="0666"
# HP Deskjet 2640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ca11", MODE="0666"
# HP ENVY 5640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="cc11", MODE="0666"
# HP Officejet 5740
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="cd11", MODE="0666"
# HP ENVY 5540
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ce11", MODE="0666"
# HP ENVY 8000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="d011", MODE="0666"
# HP PageWide Pro 477dn
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="d411", MODE="0666"
# HP PageWide Pro 477dw
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="d511", MODE="0666"
# HP PageWide Pro 577dw
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="d611", MODE="0666"
# HP ENVY 4520
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="d711", MODE="0666"
# HP Deskjet 4530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="d811", MODE="0666"
# HP Officejet 4650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="d911", MODE="0666"
# HP Deskjet 4670
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="da11", MODE="0666"
# HP Deskjet 5640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="db11", MODE="0666"
# HP ENVY 7640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="dc11", MODE="0666"
# HP ENVY 5660
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="dd11", MODE="0666"
# HP Officejet 8040
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="de11", MODE="0666"
# HP Deskjet 2130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="e111", MODE="0666"
# HP Deskjet 4720
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="e211", MODE="0666"
# HP Deskjet 3630
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="e311", MODE="0666"
# HP Officejet 3830
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="e511", MODE="0666"
# HP Deskjet 3830
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="e611", MODE="0666"
# HP ENVY 4510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="e811", MODE="0666"
# HP Officejet 250
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="e911", MODE="0666"
# HP PageWide Pro 772-777 MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="eb11", MODE="0666"
# HP PageWide Pro 772-777z MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ec11", MODE="0666"
# HP Deskjet 5810
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ed11", MODE="0666"
# HP Deskjet 5820
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ee11", MODE="0666"
# HP PageWide 377dw
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="f211", MODE="0666"
# HP Deskjet 5730
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="f511", MODE="0666"
# HP PageWide P77740-60 MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="f711", MODE="0666"
# HP PageWide P77740-60z MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="f811", MODE="0666"
# HP PageWide P57750
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="fa11", MODE="0666"
# HP PageWide P77740-60zs MFP
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="fc11", MODE="0666"
# HP Officejet 7500 E910
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4812", MODE="0666"
# HP Officejet 150 L511
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5012", MODE="0666"
# HP Officejet Pro 8500 A910
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5312", MODE="0666"
# HP Officejet 6500 E710n-z
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5412", MODE="0666"
# HP Officejet 6500 E710a-f
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5512", MODE="0666"
# HP Officejet 4610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5812", MODE="0666"
# HP Officejet Pro 8600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5912", MODE="0666"
# HP Officejet 6700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5c12", MODE="0666"
# HP Officejet 6600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5d12", MODE="0666"
# HP Officejet Pro 276dw
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="6212", MODE="0666"
# HP Officejet Pro 8740
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="6312", MODE="0666"
# HP Officejet 4620
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="6412", MODE="0666"
# HP Deskjet 4610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="6512", MODE="0666"
# HP Deskjet 4620
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="6612", MODE="0666"
# HP Officejet Pro 3610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="6812", MODE="0666"
# HP Officejet Pro 3620
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="6d12", MODE="0666"
# HP Officejet 7610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="6e12", MODE="0666"
# HP Officejet Pro 8630
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="6f12", MODE="0666"
# HP Officejet Pro 8620
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="7012", MODE="0666"
# HP Officejet Pro 8610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="7112", MODE="0666"
# HP Officejet Pro 6830
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="7212", MODE="0666"
# HP Officejet 6800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="7412", MODE="0666"
# HP Officejet Pro 8660
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="7612", MODE="0666"
# HP Officejet Pro 8640
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="7712", MODE="0666"
# HP Officejet Pro 8650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="7812", MODE="0666"
# HP Officejet Pro 8730
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="7a12", MODE="0666"
# HP Officejet Pro 8720
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="7b12", MODE="0666"
# HP Officejet Pro 8710
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="7c12", MODE="0666"
# HP Officejet 7510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="7f12", MODE="0666"
# HP Deskjet 2600
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0053", MODE="0666"
# HP Deskjet 3700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0653", MODE="0666"
# HP Deskjet 2200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0753", MODE="0666"
# HP ENVY 5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0853", MODE="0666"
# HP Officejet 5200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0953", MODE="0666"
# HP Deskjet 5000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0a53", MODE="0666"
# HP Deskjet 5200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0b53", MODE="0666"
# HP Smart Tank 350
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0f53", MODE="0666"
# HP Ink Tank 310
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1053", MODE="0666"
# HP Smart Tank 450
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1253", MODE="0666"
# HP Ink Tank 410
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1353", MODE="0666"
# HP Deskjet 2700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1853", MODE="0666"
# HP Deskjet Plus 4100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1953", MODE="0666"
# HP Deskjet 4100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1a53", MODE="0666"
# HP Officejet Pro 7740
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0154", MODE="0666"
# HP Officejet 8700
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0954", MODE="0666"
# HP Officejet Pro 6970
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0c54", MODE="0666"
# HP Officejet Pro 6960
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0d54", MODE="0666"
# HP Officejet 6960
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0e54", MODE="0666"
# HP Officejet 6950
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0f54", MODE="0666"
# HP ENVY Photo 7800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1054", MODE="0666"
# HP ENVY Photo 7100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1154", MODE="0666"
# HP ENVY Photo 6200
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1254", MODE="0666"
# HP Officejet Pro 8732
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1554", MODE="0666"
# HP Officejet Pro 7720
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1654", MODE="0666"
# HP Officejet Pro 7730
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1754", MODE="0666"
# HP Smart Tank 510
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1954", MODE="0666"
# HP Smart Tank Plus 550
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1a54", MODE="0666"
# HP Smart Tank 610
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1b54", MODE="0666"
# HP Smart Tank Plus 650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1c54", MODE="0666"
# HP ENVY Pro 6400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2154", MODE="0666"
# HP Deskjet Plus 6400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2254", MODE="0666"
# HP Officejet Pro 9020
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2354", MODE="0666"
# HP Officejet Pro 9010
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2454", MODE="0666"
# HP Officejet 9010
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2554", MODE="0666"
# HP Officejet Pro 8030
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2654", MODE="0666"
# HP Officejet Pro 8020
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2754", MODE="0666"
# HP Officejet 8020
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2854", MODE="0666"
# HP Officejet 8010
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2954", MODE="0666"
# HP Smart Tank 530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2a54", MODE="0666"
# HP Smart Tank 500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2b54", MODE="0666"
# HP Smart Tank Plus 570
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2d54", MODE="0666"
# HP ENVY 6000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3454", MODE="0666"
# HP Deskjet Plus 6000
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3554", MODE="0666"
# HP Deskjet 2300
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3654", MODE="0666"
# HP ENVY Pro 6400
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4054", MODE="0666"
# HP LaserJet CM1015
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4217", MODE="0666"
# HP LaserJet CM1017
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4317", MODE="0666"
# HP LaserJet M1522
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4517", MODE="0666"
# HP LaserJet M1522n
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4c17", MODE="0666"
# HP LaserJet M2727nf
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4d17", MODE="0666"
# HP LaserJet CM1312
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4e17", MODE="0666"
# HP LaserJet CM1312nfi
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4f17", MODE="0666"
# HP LaserJet CM2320n
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5917", MODE="0666"
# HP LaserJet CM2320nf
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5a17", MODE="0666"
# HP LaserJet CM2320fxi
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5b17", MODE="0666"
# HP LaserJet M1530
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="012a", MODE="0666"
# HP LaserJet M175
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="062a", MODE="0666"
# HP LaserJet CM1410
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="072a", MODE="0666"
# HP LaserJet M375
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="082a", MODE="0666"
# HP LaserJet M275
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0c2a", MODE="0666"
# HP LaserJet M276
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="122a", MODE="0666"
# HP LaserJet M425
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="142a", MODE="0666"
# HP LaserJet M125
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="222a", MODE="0666"
# HP LaserJet M176
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="242a", MODE="0666"
# HP LaserJet M570
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="252a", MODE="0666"
# HP LaserJet M521
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="272a", MODE="0666"
# HP LaserJet M225
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2d2a", MODE="0666"
# HP LaserJet M435
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="2e2a", MODE="0666"
# HP LaserJet M127
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="322a", MODE="0666"
# HP LaserJet M177
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="332a", MODE="0666"
# HP LaserJet M476
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="342a", MODE="0666"
# HP LaserJet M277
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3b2a", MODE="0666"
# HP LaserJet M477
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="512a", MODE="0666"
# HP LaserJet M426
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="532a", MODE="0666"
# HP LaserJet M427
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5a2a", MODE="0666"
# HP LaserJet M130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="622a", MODE="0666"
# HP LaserJet M227
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="642a", MODE="0666"
# HP LaserJet M274
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="842a", MODE="0666"
# HP LaserJet M377
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="862a", MODE="0666"
# HP LaserJet M26
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="932a", MODE="0666"
# HP LaserJet M182
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0870", MODE="0666"
# HP LaserJet M282
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0970", MODE="0666"
# HP Color LaserJet Pro MFP 3301-3304 3388
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1b01", MODE="0666"
# HP LaserJet Pro MFP M126 plus
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1b10", MODE="0666"
# HP ScanJet 5000 s2
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5105", MODE="0666"
# HP ScanJet 5000 s3
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5205", MODE="0666"
# HP ScanJet 3500 f1
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5305", MODE="0666"
# HP ScanJet 4500 fn1
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5405", MODE="0666"
# HP ScanJet 3000 s3
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5605", MODE="0666"
# HP ScanJet 5000 s4
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5705", MODE="0666"
# HP ScanJet 7000 s3
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5805", MODE="0666"
# HP ScanJet 2000 s1
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5905", MODE="0666"
# HP ScanJet 2000 s2
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5a05", MODE="0666"
# HP ScanJet 3000 s4
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5b05", MODE="0666"
# HP ScanJet n4000 snw1
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5c05", MODE="0666"
# HP ScanJet 5000 s5
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5d05", MODE="0666"
# HP ScanJet n7000 snw1
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5e05", MODE="0666"
# HP ScanJet Pro 2600 f1
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="6105", MODE="0666"
# HP ScanJet Pro 3600 f1
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="6205", MODE="0666"
# HP ScanJet Pro N4600 fnw1
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="6305", MODE="0666"
# HP ScanJet Enterprise Flow N6600 fnw1
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="6405", MODE="0666"
# HP DeskJet 4800 series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="740b", MODE="0666"
# HP DeskJet 2800 series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="031b", MODE="0666"
# HP DeskJet 4900 series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="041b", MODE="0666"
# HP DeskJet 4200 series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="051b", MODE="0666"
# HP Color LaserJet MFP X580
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="1b1b", MODE="0666"
# HP Color LaserJet MFP M577
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3a2a", MODE="0666"
# HP LaserJet MFP M527
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="402a", MODE="0666"
# HP LaserJet Flow MFP M527
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="412a", MODE="0666"
# HP Color LaserJet Flow MFP M577
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4a2a", MODE="0666"
# HP LaserJet MFP M631 M632 M633
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="672a", MODE="0666"
# HP LaserJet E62555-E62575
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="682a", MODE="0666"
# HP PageWide Color MFP 586
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="812a", MODE="0666"
# HP PageWide Color Flow MFP 586
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="822a", MODE="0666"
# HP PageWide Color MFP E58650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="832a", MODE="0666"
# HP PageWide Color Flow E58650
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="852a", MODE="0666"
# HP LaserJet M278
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ad2a", MODE="0666"
# HP LaserJet M177
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="af2a", MODE="0666"
# HP LaserJet E725
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="b02a", MODE="0666"
# HP LaserJet E825
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="b12a", MODE="0666"
# HP LaserJet E778
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="b22a", MODE="0666"
# HP LaserJet E876
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="b32a", MODE="0666"
# HP PageWide Color MFP 780-785
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="b42a", MODE="0666"
# HP PageWide Color E77650-E77660
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="b52a", MODE="0666"
# HP Digital Sender 8500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="b92a", MODE="0666"
# HP ScanJet N9120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ba2a", MODE="0666"
# HP LaserJet M28w
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="bf2a", MODE="0666"
# HP LaserJet M428-M429
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c22a", MODE="0666"
# HP LaserJet M428f-M429f
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c32a", MODE="0666"
# HP LaserJet M478f-M479f
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c52a", MODE="0666"
# HP LaserJet M479
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c62a", MODE="0666"
# HP PageWide Color P77440 P77940-60
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="c92a", MODE="0666"
# HP LaserJet M528
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="cc2a", MODE="0666"
# HP LaserJet MFP E52545
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="cd2a", MODE="0666"
# HP Color LaserJet MFP E57540
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="d02a", MODE="0666"
# HP LaserJet e52645
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="d32a", MODE="0666"
# HP LaserJet M430
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="d52a", MODE="0666"
# HP LaserJet E42540
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="d72a", MODE="0666"
# HP LaserJet M480
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="d92a", MODE="0666"
# HP LaserJet E47528
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="db2a", MODE="0666"
# HP LaserJet M329
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="df2a", MODE="0666"
# HP LaserJet E676
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="e22a", MODE="0666"
# HP LaserJet E626
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="e32a", MODE="0666"
# HP LaserJet M148dw
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ec2a", MODE="0666"
# HP LaserJet M148fdw
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ed2a", MODE="0666"
# HP LaserJet E774
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ee2a", MODE="0666"
# HP LaserJet E724
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="ef2a", MODE="0666"
# HP LaserJet MFP 1005
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="f12a", MODE="0666"
# HP LaserJet MFP 120x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="f42a", MODE="0666"
# HP Color MFP E877-40-50-60-70
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="f52a", MODE="0666"
# HP LaserJet Managed MFP E826-50-60-70
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="f62a", MODE="0666"
# HP CLJ E786-25-30-35
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="f72a", MODE="0666"
# HP LaserJet MFP E731-30-35-40
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="f82a", MODE="0666"
# HP CLJ  E785 E78523-28
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="f92a", MODE="0666"
# HP LJE730_E73025-30
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="fa2a", MODE="0666"
# HP LaserJet MFP E72825-30-35
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="fb2a", MODE="0666"
# HP Color MFP E78323-25-30
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="fc2a", MODE="0666"
# HP Color LJ E78223 E78228
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="fe2a", MODE="0666"
# HP Smart Tank 790 series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3854", MODE="0666"
# HP Smart Tank 750 series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3954", MODE="0666"
# HP Smart Tank 710-720 series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3a54", MODE="0666"
# HP Smart Tank 660-670 series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3b54", MODE="0666"
# HP Smart Tank 7600 series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3c54", MODE="0666"
# HP Smart Tank 7300 series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3d54", MODE="0666"
# HP Smart Tank 7000 series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3e54", MODE="0666"
# HP Smart Tank 6000 series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="3f54", MODE="0666"
# HP Smart Tank 710i series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4354", MODE="0666"
# HP Smart Tank 580-590
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4454", MODE="0666"
# HP Smart Tank 520_540
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4554", MODE="0666"
# HP OfficeJet Pro 9120b
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4754", MODE="0666"
# HP OfficeJet Pro 9130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4854", MODE="0666"
# HP OfficeJet Pro 9120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4954", MODE="0666"
# HP OfficeJet Pro 9130b
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4a54", MODE="0666"
# HP OfficeJet Pro 8130
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4b54", MODE="0666"
# HP OfficeJet Pro 8120e
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4c54", MODE="0666"
# HP OfficeJet 8120e
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4d54", MODE="0666"
# HP DeskJet Plus 6000i series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="4f54", MODE="0666"
# HP Smart Tank 5100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5054", MODE="0666"
# HP OfficeJet Pro 9730
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5354", MODE="0666"
# HP OfficeJet Pro 9720
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5454", MODE="0666"
# HP OfficeJet 9120
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5554", MODE="0666"
# HP OfficeJet 8130e
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="5654", MODE="0666"
# HP LJ M139M142
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0372", MODE="0666"
# HP LaserJet M232
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0972", MODE="0666"
# HP LaserJet MFP 4301
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0a72", MODE="0666"
# HP LaserJet MFP M634 M635 M636
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0673", MODE="0666"
# HP Color LaserJet MFP M578
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0773", MODE="0666"
# HP LJ Pro 4101 4102 4103 4104
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0374", MODE="0666"
# HP Color LaserJet MFP 6800 6801
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0175", MODE="0666"
# HP Color LaserJet MFP X677_X67755-65
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0275", MODE="0666"
# HP Color LaserJet MFP 5800
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0475", MODE="0666"
# HP LaserJet Tank MFP 160x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0574", MODE="0666"
# HP LaserJet Tank MFP 260x
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0774", MODE="0666"
# HP LaserJet Tank MFP 1005
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0974", MODE="0666"
# HP ENVY Inspire 7200 series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0c74", MODE="0666"
# HP ENVY Inspire 7900 series
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0d74", MODE="0666"
# HP Color LaserJet MFP X579
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0675", MODE="0666"
# HP LJ Pro MFP 31013108
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0f81", MODE="0666"
# HP Envy 6100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0db6", MODE="0666"
# HP DeskJet Plus Ink Advantage 6100
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0eb6", MODE="0666"
# HP Envy 6500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="0fb6", MODE="0666"
# HP DeskJet Plus Ink Advantage 6500
SUBSYSTEMS=="usb", ATTRS{idVendor}=="03f0", ATTRS{idProduct}=="01b7", MODE="0666"
# Ratoc 1
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0584", ATTRS{idProduct}=="0220", MODE="0666"
# Ratoc 2
SUBSYSTEMS=="usb", ATTRS{idVendor}=="0584", ATTRS{idProduct}=="0222", MODE="0666"
