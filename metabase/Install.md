Running Metabase on Docker
To get fast, reliable, and secure deployment with none of the work or hidden costs that come with self-hosting, check out Metabase Cloud.

Metabase provides an official Docker image via Dockerhub that can be used for deployments on any system that is running Docker.

If you’re trying to upgrade your Metabase version on Docker, check out these upgrading instructions.

Open Source quick start
Use this quick start to run the Open Source version of Metabase locally. See below for instructions on running Metabase in production.

Assuming you have Docker installed and running, get the latest Docker image:

docker pull metabase/metabase:latest
    
Copy

    
Then start the Metabase container:

docker run -d -p 3000:3000 --name metabase metabase/metabase
    
Copy

    
This will launch an Metabase server on port 3000 by default.

Optional: to view the logs as your Open Source Metabase initializes, run:

docker logs -f metabase
    
Copy

    
Once startup completes, you can access your Open Source Metabase at http://localhost:3000.

To run your Open Source Metabase on a different port, say port 12345:

docker run -d -p 12345:3000 --name metabase metabase/metabase