# Erklärung: Wayland deaktivieren und X11 verwenden

## Was sind Wayland und X11?

**X11** (auch X Window System oder X.Org genannt) ist das traditionelle Display-Server-Protokoll, das seit Jahrzehnten in Linux-Systemen verwendet wird. Es ist bewährt, aber hat einige Einschränkungen in Bezug auf Sicherheit und moderne Grafikfunktionen.

**Wayland** ist der neuere Display-Server, der als Ersatz für X11 entwickelt wurde. Er bietet bessere Sicherheit, Leistung und moderne Grafikfunktionen, ist aber noch nicht so ausgereift und kann Kompatibilitätsprobleme mit einigen Anwendungen haben.

## Was bedeutet "Wayland deaktivieren"?

In Ubuntu wird die Auswahl zwischen Wayland und X11 in der Datei `/etc/gdm3/custom.conf` konfiguriert. Standardmäßig sieht ein Teil dieser Datei etwa so aus:

```
# Uncomment the line below to force the login screen to use Xorg
#WaylandEnable=false
```

Die Zeile `#WaylandEnable=false` ist **auskommentiert** (durch das # am Anfang), was bedeutet, dass sie ignoriert wird. In diesem Zustand ist Wayland aktiviert, da die Einstellung zum Deaktivieren nicht wirksam ist.

## Was bedeutet "auskommentieren (# entfernen)"?

"Auskommentieren" bedeutet in diesem Kontext, das #-Zeichen am Anfang der Zeile zu entfernen, sodass die Zeile zu `WaylandEnable=false` wird. Dadurch wird die Einstellung aktiv und Wayland wird deaktiviert.

Vorher (Wayland aktiviert):
```
#WaylandEnable=false
```

Nachher (Wayland deaktiviert):
```
WaylandEnable=false
```

## Wie führt man diese Änderung durch?

1. Öffnen Sie die Datei mit Root-Rechten:
   ```bash
   sudo nano /etc/gdm3/custom.conf
   ```

2. Suchen Sie die Zeile `#WaylandEnable=false`

3. Entfernen Sie das #-Zeichen am Anfang der Zeile

4. Speichern Sie die Datei (in nano: Strg+O, dann Enter) und beenden Sie den Editor (Strg+X)

5. Starten Sie den GDM-Dienst neu oder starten Sie das System neu:
   ```bash
   sudo systemctl restart gdm3
   ```
   oder
   ```bash
   sudo reboot
   ```

## Warum könnte dies die GNOME Shell-Probleme beheben?

In den Logs wurde folgender Fehler gefunden:
```
gnome-shell[2116]: JS ERROR: Gio.IOErrorEnum: Xwayland exited unexpectedly
```

Dieser Fehler zeigt, dass Xwayland (die Komponente, die X11-Anwendungen unter Wayland ausführt) unerwartet beendet wurde. Dies kann zu Instabilität der GNOME Shell führen.

Durch die Deaktivierung von Wayland und die Verwendung von X11 direkt:

1. Umgehen Sie die Xwayland-Komponente vollständig
2. Nutzen Sie das stabilere und besser getestete X11-System
3. Vermeiden Sie Kompatibilitätsprobleme zwischen Wayland und bestimmten Anwendungen oder Treibern

Diese Änderung ist besonders hilfreich, wenn Sie Probleme mit der Grafikdarstellung, Abstürze der GNOME Shell oder Probleme mit bestimmten Anwendungen haben, die nicht gut mit Wayland funktionieren.

## Wie kann man überprüfen, ob man Wayland oder X11 verwendet?

Nach dem Einloggen können Sie überprüfen, welches Display-System Sie verwenden:

```bash
echo $XDG_SESSION_TYPE
```

Wenn die Ausgabe `x11` ist, verwenden Sie X11. Wenn die Ausgabe `wayland` ist, verwenden Sie Wayland.

## Kann man zurückwechseln?

Ja, Sie können jederzeit zu Wayland zurückkehren, indem Sie das #-Zeichen wieder vor die Zeile `WaylandEnable=false` setzen, sodass sie wieder zu `#WaylandEnable=false` wird, und dann das System neu starten.