
freez@iMacUbuntu:~/System$ journalctl -b -u gdm.service
Mär 24 11:52:51 iMacUbuntu systemd[1]: Starting gdm.service - GNOME Display Manager...
Mär 24 11:52:51 iMacUbuntu systemd[1]: Started gdm.service - GNOME Display Manager.
Mär 24 11:52:51 iMacUbuntu gdm-launch-environment][1953]: pam_unix(gdm-launch-environment:session): session opened for user gdm(uid=120) by (uid=0)
<PERSON>är 24 11:53:06 iMacUbuntu gdm-password][2956]: gkr-pam: unable to locate daemon control file
Mär 24 11:53:06 iMacUbuntu gdm-password][2956]: gkr-pam: stashed password to try later in open session
Mär 24 11:53:06 iMacUbuntu gdm-password][2956]: pam_unix(gdm-password:session): session opened for user freez(uid=1001) by freez(uid=0)
Mär 24 11:53:06 iMacUbuntu gdm-password][2956]: gkr-pam: unlocked login keyring
Mär 24 11:53:06 iMacUbuntu gdm3[1933]: Gdm: on_display_added: assertion 'GDM_IS_REMOTE_DISPLAY (display)' failed
Mär 24 11:53:32 iMacUbuntu gdm3[1933]: Gdm: Child process -1989 was already dead.
Mär 24 11:53:32 iMacUbuntu gdm3[1933]: Gdm: on_display_removed: assertion 'GDM_IS_REMOTE_DISPLAY (display)' failed

freez@iMacUbuntu:~/System$ journalctl -b | grep "gkr-pam"
Mär 24 11:53:06 iMacUbuntu gdm-password][2956]: gkr-pam: unable to locate daemon control file
Mär 24 11:53:06 iMacUbuntu gdm-password][2956]: gkr-pam: stashed password to try later in open session
Mär 24 11:53:06 iMacUbuntu gdm-password][2956]: gkr-pam: unlocked login keyring

freez@iMacUbuntu:~/System$ journalctl -b -u gnome-session-*.service
Failed to add filter for units: Keine Daten verfügbar
freez@iMacUbuntu:~/System$ journalctl -b | grep "gnome-session-binary" | grep "Failed"
Mär 24 11:53:06 iMacUbuntu systemd[2976]: Failed to start app-gnome-gnome\x2dkeyring\x2dpkcs11-3319.scope - Application launched by gnome-session-binary.
Mär 24 11:53:06 iMacUbuntu systemd[2976]: Failed to start app-gnome-gnome\x2dkeyring\x2dssh-3318.scope - Application launched by gnome-session-binary.
Mär 24 11:53:06 iMacUbuntu gnome-session[3303]: gnome-session-binary[3303]: GnomeDesktop-WARNING: Could not create transient scope for PID 3325: GDBus.Error:org.freedesktop.DBus.Error.UnixProcessIdUnknown: Failed to set unit properties: No such process
Mär 24 11:53:06 iMacUbuntu gnome-session-binary[3303]: GnomeDesktop-WARNING: Could not create transient scope for PID 3325: GDBus.Error:org.freedesktop.DBus.Error.UnixProcessIdUnknown: Failed to set unit properties: No such process
Mär 24 11:53:06 iMacUbuntu gnome-session[3303]: gnome-session-binary[3303]: GnomeDesktop-WARNING: Could not create transient scope for PID 3338: GDBus.Error:org.freedesktop.DBus.Error.UnixProcessIdUnknown: Failed to set unit properties: No such process
Mär 24 11:53:06 iMacUbuntu gnome-session-binary[3303]: GnomeDesktop-WARNING: Could not create transient scope for PID 3338: GDBus.Error:org.freedesktop.DBus.Error.UnixProcessIdUnknown: Failed to set unit properties: No such process
Mär 24 11:53:15 iMacUbuntu gnome-session[3303]: gnome-session-binary[3303]: GnomeDesktop-WARNING: Could not create transient scope for PID 3808: GDBus.Error:org.freedesktop.DBus.Error.UnixProcessIdUnknown: Failed to set unit properties: No such process
Mär 24 11:53:15 iMacUbuntu gnome-session-binary[3303]: GnomeDesktop-WARNING: Could not create transient scope for PID 3808: GDBus.Error:org.freedesktop.DBus.Error.UnixProcessIdUnknown: Failed to set unit properties: No such process
Mär 24 11:53:15 iMacUbuntu gnome-session[3303]: gnome-session-binary[3303]: GnomeDesktop-WARNING: Could not create transient scope for PID 3902: GDBus.Error:org.freedesktop.DBus.Error.UnixProcessIdUnknown: Failed to set unit properties: No such process
Mär 24 11:53:15 iMacUbuntu gnome-session-binary[3303]: GnomeDesktop-WARNING: Could not create transient scope for PID 3902: GDBus.Error:org.freedesktop.DBus.Error.UnixProcessIdUnknown: Failed to set unit properties: No such process
Mär 24 11:53:15 iMacUbuntu systemd[2976]: Failed to start app-gnome-im\x2dlaunch-3839.scope - Application launched by gnome-session-binary.
Mär 24 11:53:15 iMacUbuntu systemd[2976]: Failed to start app-gnome-snap\x2duserd\x2dautostart-3853.scope - Application launched by gnome-session-binary.
Mär 24 11:53:15 iMacUbuntu systemd[2976]: Failed to start app-gnome-ubuntu\x2dreport\x2don\x2dupgrade-3842.scope - Application launched by gnome-session-binary.
freez@iMacUbuntu:~/System$ journalctl -b | grep "org.gnome.SettingsDaemon.XSettings"
Mär 24 11:53:15 iMacUbuntu systemd[2976]: Dependency failed for org.gnome.SettingsDaemon.XSettings.service - GNOME XSettings service.
Mär 24 11:53:15 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Job org.gnome.SettingsDaemon.XSettings.service/start failed with result 'dependency'.
Mär 24 11:53:15 iMacUbuntu systemd[2976]: Reached target org.gnome.SettingsDaemon.XSettings.target - GNOME XSettings target.
Mär 24 11:53:35 iMacUbuntu systemd[2976]: Starting org.gnome.SettingsDaemon.XSettings.service - GNOME XSettings service...
Mär 24 11:53:36 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Main process exited, code=dumped, status=5/TRAP
Mär 24 11:53:36 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Failed with result 'core-dump'.
Mär 24 11:53:36 iMacUbuntu systemd[2976]: Failed to start org.gnome.SettingsDaemon.XSettings.service - GNOME XSettings service.
Mär 24 11:53:37 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Scheduled restart job, restart counter is at 1.
Mär 24 11:53:37 iMacUbuntu systemd[2976]: Starting org.gnome.SettingsDaemon.XSettings.service - GNOME XSettings service...
Mär 24 11:53:38 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Main process exited, code=dumped, status=5/TRAP
Mär 24 11:53:38 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Failed with result 'core-dump'.
Mär 24 11:53:38 iMacUbuntu systemd[2976]: Failed to start org.gnome.SettingsDaemon.XSettings.service - GNOME XSettings service.
Mär 24 11:53:38 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Scheduled restart job, restart counter is at 2.
Mär 24 11:53:38 iMacUbuntu systemd[2976]: Starting org.gnome.SettingsDaemon.XSettings.service - GNOME XSettings service...
Mär 24 11:53:39 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Main process exited, code=dumped, status=5/TRAP
Mär 24 11:53:39 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Failed with result 'core-dump'.
Mär 24 11:53:39 iMacUbuntu systemd[2976]: Failed to start org.gnome.SettingsDaemon.XSettings.service - GNOME XSettings service.
Mär 24 11:53:40 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Scheduled restart job, restart counter is at 3.
Mär 24 11:53:40 iMacUbuntu systemd[2976]: Starting org.gnome.SettingsDaemon.XSettings.service - GNOME XSettings service...
Mär 24 11:53:41 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Main process exited, code=dumped, status=5/TRAP
Mär 24 11:53:41 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Failed with result 'core-dump'.
Mär 24 11:53:41 iMacUbuntu systemd[2976]: Failed to start org.gnome.SettingsDaemon.XSettings.service - GNOME XSettings service.
Mär 24 11:53:41 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Scheduled restart job, restart counter is at 4.
Mär 24 11:53:41 iMacUbuntu systemd[2976]: Starting org.gnome.SettingsDaemon.XSettings.service - GNOME XSettings service...
Mär 24 11:53:42 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Main process exited, code=dumped, status=5/TRAP
Mär 24 11:53:42 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Failed with result 'core-dump'.
Mär 24 11:53:42 iMacUbuntu systemd[2976]: Failed to start org.gnome.SettingsDaemon.XSettings.service - GNOME XSettings service.
Mär 24 11:53:43 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Scheduled restart job, restart counter is at 5.
Mär 24 11:53:43 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Start request repeated too quickly.
Mär 24 11:53:43 iMacUbuntu systemd[2976]: org.gnome.SettingsDaemon.XSettings.service: Failed with result 'core-dump'.
Mär 24 11:53:43 iMacUbuntu systemd[2976]: Failed to start org.gnome.SettingsDaemon.XSettings.service - GNOME XSettings service.
freez@iMacUbuntu:~/System$ 
