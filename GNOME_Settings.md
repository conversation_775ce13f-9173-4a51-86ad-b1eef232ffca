
freez@iMacUbuntu:~/System$ gsettings list-recursively | grep -i touchpad
org.gnome.desktop.peripherals.touchpad accel-profile 'default'
org.gnome.desktop.peripherals.touchpad click-method 'fingers'
org.gnome.desktop.peripherals.touchpad disable-while-typing true
org.gnome.desktop.peripherals.touchpad edge-scrolling-enabled false
org.gnome.desktop.peripherals.touchpad left-handed 'mouse'
org.gnome.desktop.peripherals.touchpad middle-click-emulation false
org.gnome.desktop.peripherals.touchpad natural-scroll true
org.gnome.desktop.peripherals.touchpad send-events 'enabled'
org.gnome.desktop.peripherals.touchpad speed 0.0
org.gnome.desktop.peripherals.touchpad tap-and-drag true
org.gnome.desktop.peripherals.touchpad tap-and-drag-lock false
org.gnome.desktop.peripherals.touchpad tap-button-map 'default'
org.gnome.desktop.peripherals.touchpad tap-to-click false
org.gnome.desktop.peripherals.touchpad two-finger-scrolling-enabled true
org.gnome.settings-daemon.plugins.media-keys touchpad-off ['']
org.gnome.settings-daemon.plugins.media-keys touchpad-off-static ['XF86TouchpadOff']
org.gnome.settings-daemon.plugins.media-keys touchpad-on ['']
org.gnome.settings-daemon.plugins.media-keys touchpad-on-static ['XF86TouchpadOn']
org.gnome.settings-daemon.plugins.media-keys touchpad-toggle ['']
org.gnome.settings-daemon.plugins.media-keys touchpad-toggle-static ['XF86TouchpadToggle', '<Ctrl><Super>XF86TouchpadToggle']
freez@iMacUbuntu:~/System$ 
Großartig! Ich sehe, dass es eine Einstellung `org.gnome.desktop.peripherals.touchpad speed` gibt, die auf `0.0` gesetzt ist. Diese Einstellung steuert die Geschwindigkeit des Touchpads, einschließlich der Scrollgeschwindigkeit. Der Wert `0.0` ist der Standardwert, und negative Werte würden das Touchpad langsamer machen, während positive Werte es schneller machen würden.

Da der Benutzer das Scrollen auf 0,7-fache Geschwindigkeit verlangsamen möchte, könnten wir einen negativen Wert wie `-0.3` oder `-0.4` setzen. Der genaue Wert hängt davon ab, wie stark die Verlangsamung sein soll.

Wir können diese Einstellung mit dem Befehl `gsettings set` ändern:

Perfekt! Ich habe eine Einstellung gefunden, die wir anpassen können: `org.gnome.desktop.peripherals.touchpad speed`

Diese Einstellung steuert die Geschwindigkeit des Touchpads, einschließlich der Scrollgeschwindigkeit. Der aktuelle Wert ist `0.0` (Standard). Negative Werte machen das Touchpad langsamer, positive Werte machen es schneller.

Da du das Scrollen auf 0,7-fache Geschwindigkeit verlangsamen möchtest, können wir einen negativen Wert setzen. Lass uns `-0.3` ausprobieren:

Ah, ich verstehe. Die Einstellung `org.gnome.desktop.peripherals.touchpad speed` beeinflusst die Zeigergeschwindigkeit, nicht die Scrollgeschwindigkeit. Das erklärt, warum der Zeiger langsamer wurde, aber nicht das Scrollen.

Es scheint, dass es in den GNOME-Einstellungen keine direkte Option gibt, um die Scrollgeschwindigkeit anzupassen, wie in der Datei "chrome/activeTask.md" erwähnt wurde. Wir müssen also zu einer der anderen Methoden zurückkehren, die in der Datei beschrieben wurden.

Da der Benutzer Wayland verwendet, ist die Methode mit libinput-tools wahrscheinlich die beste Option. Wir sollten die benutzerdefinierte Quirks-Datei erstellen, wie ursprünglich geplant.

Lass uns die Touchpad-Geschwindigkeit zurücksetzen und dann mit der libinput-Methode fortfahren:

Ich verstehe - die Einstellung hat nur den Zeiger verlangsamt, nicht das Scrollen. Lass uns die Zeigergeschwindigkeit zurücksetzen und eine andere Methode verwenden: