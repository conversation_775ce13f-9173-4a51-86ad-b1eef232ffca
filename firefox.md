Installation von Firefox über das .deb Paket für Debian-basierte Distributionen (empfohlen)
Mit diesen Schritten installieren Sie das .deb-Paket über das APT-Repository:

Erstellen Sie ein Verzeichnis zum Speichern der APT-Repository-Schlüssel, falls es noch nicht vorhanden ist:
sudo install -d -m 0755 /etc/apt/keyrings
Importieren Sie den signierten Schlüssel des Mozilla-APT-Repositories:
wget -q https://packages.mozilla.org/apt/repo-signing-key.gpg -O- | sudo tee /etc/apt/keyrings/packages.mozilla.org.asc > /dev/null
(Wenn wget nicht installiert ist, können Sie es mit sudo apt-get install wget installieren.)
Der Fingerabdruck sollte 35BAA0B33E9EB396F59CA838C0BA5CE6DC6315A3 lauten. <PERSON><PERSON> <PERSON><PERSON><PERSON> dies mit folgendem Befehl prüfen:
gpg -n -q --import --import-options import-show /etc/apt/keyrings/packages.mozilla.org.asc | awk '/pub/{getline; gsub(/^ +| +$/,""); if($0 == "35BAA0B33E9EB396F59CA838C0BA5CE6DC6315A3") print "\nThe key fingerprint matches ("$0").\n"; else print "\nVerification failed: the fingerprint ("$0") does not match the expected one.\n"}'
Fügen Sie als nächstes das Mozilla-APT-Repository zu Ihrem Quellenverzeichnis hinzu:
echo "deb [signed-by=/etc/apt/keyrings/packages.mozilla.org.asc] https://packages.mozilla.org/apt mozilla main" | sudo tee -a /etc/apt/sources.list.d/mozilla.list > /dev/null
Konfigurieren Sie APT so, dass Pakete aus dem Mozilla Repository priorisiert werden:
echo '
Package: *
Pin: origin packages.mozilla.org
Pin-Priority: 1000
' | sudo tee /etc/apt/preferences.d/mozilla
Aktualisieren Sie Ihre Paketliste und installieren Sie das Firefox .deb-Paket:
sudo apt-get update && sudo apt-get install firefox
Einrichten verschiedener Sprachen in Firefox mit .deb-Paketen
Für Nutzer, die Firefox in einer anderen Sprache als amerikanischem Englisch verwenden möchten, wurden .deb-Pakete erstellt, die Sprachpakete für Firefox enthalten. Um ein bestimmtes Sprachpaket zu installieren, ersetzen Sie den Sprachcode (wie im folgenden Beispiel durch de für das deutsche Sprachpaket:
sudo apt-get install firefox-l10n-de

Um alle verfügbaren Sprachpakete aufzulisten, können Sie den unten stehenden Befehl verwenden, nachdem Sie das Mozilla-APT-Repository hinzugefügt und sudo apt-get update ausgeführt haben:
apt-cache search firefox-l10n

Migration von Daten
Wenn Sie vorher Snap oder Flatpak verwendet haben, müssen Sie Ihr Profil importieren. Dazu gibt es zwei Möglichkeiten. Wählen Sie eine dieser Methoden:

Methode 1: Richten Sie Sync ein. Die Anleitung dazu finden Sie unter Sync auf Ihrem Computer einrichten
Methode 2: Kopieren Sie die vorhandenen Dateien auf Ihrem Computer. Stellen Sie sicher, dass alle aktiven Firefox-Instanzen auf Ihrem Computer vollständig geschlossen sind, bevor Sie Folgendes tun:
Flatpak:
mkdir -p ~/.mozilla/firefox/ && cp -a ~/.var/app/org.mozilla.firefox/.mozilla/firefox/* ~/.mozilla/firefox/
Snap:
mkdir -p ~/.mozilla/firefox/ && cp -a ~/snap/firefox/common/.mozilla/firefox/* ~/.mozilla/firefox/
In beiden Fällen starten Sie Firefox nach dem Verschieben der Profile vom Terminal aus mit dem Befehl firefox -P und wählen Sie das gewünschte Profil. Nach dieser Erstkonfiguration ist der Befehl -P nicht mehr erforderlich.

Installation über die Paketverwaltung Ihrer Linux-Distribution
Zur Installation von Firefox über die Paketverwaltung Ihrer Linux-Distribution lesen Sie bitte die Dokumentation der von Ihnen verwendeten Distribution.

Diese Methode wird empfohlen, da sie sicherstellt, dass Firefox und alle benötigten Bibliotheken optimal für Ihre Distribution installiert und konfiguriert werden. Es kann jedoch eine kleine Verzögerung zwischen der offiziellen Veröffentlichung einer neuen Firefox-Version und dem Zeitpunkt geben, an dem Ihre Distribution die von ihr vertriebene Version aktualisiert. Ihre Distribution kann Firefox auch ohne das Firefox-Markenzeichen verteilen oder nur die Version Firefox ESR (Extended Support Release).

Installation von Firefox über Flatpak
Um Firefox über Flatpak zu installieren, müssen Sie zuerst Flatpak auf Ihrem Computer installieren und konfigurieren. Nachdem Sie Flatpak installiert haben, gehen Sie zur englischsprachigen Seite Firefox auf Flathub und klicken Sie auf die Schaltfläche Install. Alternativ können Sie auch diesen Befehl auf dem Terminal ausführen:
flatpak install flathub org.mozilla.firefox

Standardmäßig wird Firefox von Flatpak in der Sprache Ihres Betriebssystems installiert. Wenn Sie eine andere Sprache wünschen, folgen Sie bitte dieser Anleitung zur Verwendung von Firefox in einer anderen Sprache.

Installation von Firefox über Snap
Um Firefox über Snap zu installieren, müssen Sie zuerst Snap auf Ihrem Computer installieren. Nachdem Sie Snap installiert haben, gehen Sie zur englischsprachigen Seite Firefox im Snapcraft Store, klicken Sie auf die Schaltfläche Install und folgen Sie der Anleitung. Alternativ können Sie auch diesen Befehl auf dem Terminal ausführen:
sudo snap install firefox

Sofern verfügbar, wird Firefox von Snap in der Sprache Ihres Betriebssystems installiert. Wenn Sie eine andere Sprache wünschen, folgen Sie bitte dieser Anleitung zur Verwendung von Firefox in einer anderen Sprache.

Installation von Firefox über die Mozilla-Dateien (Builds)
Stellen Sie vor der Installation von Firefox über ein Mozilla-Build sicher, dass die benötigten Bibliotheken auf Ihrem Computer vorhanden sind. Fehlen diese oder sind unvollständig, ist Firefox nicht funktionsfähig.

Installation von Firefox über das System (für erfahrene Nutzer)
Um Firefox mit dieser Methode installieren zu können, müssen Sie sich als Root anmelden oder sudo-Befehle ausführen können.

Ein auf diese Weise installierter Firefox hat Vorrang vor der Firefox-Version, die über Ihre Paketverwaltung installiert wurde. Um die von Ihrer Paketverwaltung installierte Version auszuführen, müssen Sie die Binärdatei von einem Terminal aus starten. In den meisten Distributionen funktioniert es, indem Sie ein Terminal öffnen und diesen Befehl eingeben:
/usr/bin/firefox


Gehen Sie zur Webseite Download Mozilla Firefox für Linux und klicken Sie auf 32-Bit für Linux herunterladen oder 64-Bit für Linux herunterladen.
Öffnen Sie ein Terminal und gehen Sie zum Ordner, in dem der Download gespeichert wurde, z. B.
cd ~/Downloads
Entpacken Sie den Inhalt der heruntergeladenen Datei, indem Sie diesen Text eingeben:
tar xjf firefox-*.tar.bz2

Wichtig: Die folgenden Befehle müssen als Root ausgeführt werden oder durch Voranstellen von sudo.
Verschieben Sie den entpackten Firefox-Ordner zu /opt:
mv firefox /opt
Erstellen Sie eine symbolische Verknüpfung („Symlink“) zur ausführbaren Firefox-Datei:
ln -s /opt/firefox/firefox /usr/local/bin/firefox
Laden Sie eine Kopie der Desktop-Datei herunter:
wget https://raw.githubusercontent.com/mozilla/sumo-kb/main/install-firefox-linux/firefox.desktop -P /usr/local/share/applications

Wenn wget nicht auf Ihrem Computer installiert ist, gehen Sie alternativ zur oben genannten URL, klicken Sie mit der rechten Maustaste auf die Seite, um das Kontextmenü zu öffnen, und wählen Sie Seite speichern unter…. Nachdem Sie die Datei heruntergeladen haben, verschieben Sie sie nach /usr/local/share/applications.
Um sicherzustellen, dass die Installation erfolgreich war, können Sie about:support in die Adressleiste eingeben und die Eingabetaste drücken. Danach öffnet sich die Seite mit den Informationen zur Fehlerbehebung. Im Abschnitt Allgemeine Informationen sollte in der Zeile „Anwendungsprogrammdatei“ der Wert /opt/firefox/firefox-bin sein.

Lokale Installation von Firefox über ein Benutzerkonto
Wenn Sie keinen Root-Zugriff haben, keine sudo-Befehle ausführen können oder einfach lieber einen lokalen Firefox für Ihr Konto bevorzugen, können Sie eine lokale Installation erstellen. Lokale Installationen eignen sich auch, wenn Sie mehrere Firefox-Installationen für verschiedene Versionen (Builds) haben möchten.
Gehen Sie zur Webseite Download Mozilla Firefox für Linux und klicken Sie auf 32-Bit für Linux herunterladen oder 64-Bit für Linux herunterladen.
Sie können auf dieser Seite auch auf den Link Firefox für andere Plattformen & Sprachen herunterladen klicken, um eine andere Version (Build) zu wählen: Firefox Nightly, Firefox Beta, Firefox Developer Edition oder Firefox Extended Support Release (ESR).
Öffnen Sie ein Terminal und gehen Sie zum Ordner, in dem der Download gespeichert wurde, z. B.
cd ~/Downloads
Entpacken Sie den Inhalt der heruntergeladenen Datei. Dadurch wird ein Ordner mit dem Namen firefox erstellt. Danach können Sie das Archiv löschen:
tar xjf firefox-*.tar.bz2
rm firefox-*.tar.bz2
Wenn Sie eine bestimmte Version heruntergeladen haben, müssen Sie möglicherweise den entpackten Ordner entsprechend umbenennen, z. B.
mv firefox firefox-nightly
Sie können den entpackten Ordner in Ihrem Download-Ordner belassen oder ihn an einen anderen Ort in Ihrem Konto verschieben, z. B.
mv firefox ~/firefox
Firefox ist nun einsatzbereit und Sie können ihn mit diesem Befehl direkt vom Terminal aus starten:
~/firefox/firefox &
Erstellen Sie eine Verknüpfung auf dem Desktop (die Reihenfolge der Schritte und die Bezeichnung der Einträge im Kontextmenü kann sich je nach Linux-Distribution unterscheiden).
Klicken Sie mit der rechten Maustaste auf den Desktop und wählen Sie im Kontextmenü Starter erstellen.
Sie können im Terminal auch Folgendes eingeben:
ln -s ~/firefox/firefox ~/Desktop/
Das Symbol für die Desktop-Verknüpfung finden Sie in
~/firefox/browser/chrome/icons/default/
Hinweis: Diese Methode ändert die Zuordnung der Dateitypen auf dem System nicht, sodass Links von anderen Anwendungen in einer lokalen Installation nicht geöffnet werden. Sie müssen den Link manuell kopieren und in die Firefox-Adressleiste einfügen.