// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXAggregateTarget section */
		0B1AF7D91B8D0F0B005BF60D /* all */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 0B1AF7DA1B8D0F0B005BF60D /* Build configuration list for PBXAggregateTarget "all" */;
			buildPhases = (
			);
			dependencies = (
				0B1AF7DE1B8D0F16005BF60D /* PBXTargetDependency */,
				0B1AF7E01B8D0F16005BF60D /* PBXTargetDependency */,
			);
			name = all;
			productName = all;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		0B1AF7C01B8CF8A2005BF60D /* lzvn_decode_base.c in Sources */ = {isa = PBXBuildFile; fileRef = 0B1AF7B71B8CF8A2005BF60D /* lzvn_decode_base.c */; };
		0B1AF7C41B8CF8A2005BF60D /* lzvn_encode_base.c in Sources */ = {isa = PBXBuildFile; fileRef = 0B1AF7BB1B8CF8A2005BF60D /* lzvn_encode_base.c */; };
		0B1AF7E31B8D0F65005BF60D /* lzfse_main.c in Sources */ = {isa = PBXBuildFile; fileRef = 0B1AF7E11B8D0F59005BF60D /* lzfse_main.c */; };
		0B1AF7E41B8D1EFD005BF60D /* liblzfse.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 0B57822C1B8BC12F002F950F /* liblzfse.a */; };
		0B57823B1B8BC151002F950F /* lzfse_decode_base.c in Sources */ = {isa = PBXBuildFile; fileRef = 0B5782321B8BC151002F950F /* lzfse_decode_base.c */; };
		0B57823D1B8BC151002F950F /* lzfse_decode.c in Sources */ = {isa = PBXBuildFile; fileRef = 0B5782341B8BC151002F950F /* lzfse_decode.c */; };
		0B57823E1B8BC151002F950F /* lzfse_encode_base.c in Sources */ = {isa = PBXBuildFile; fileRef = 0B5782351B8BC151002F950F /* lzfse_encode_base.c */; };
		0B5782401B8BC151002F950F /* lzfse_encode.c in Sources */ = {isa = PBXBuildFile; fileRef = 0B5782371B8BC151002F950F /* lzfse_encode.c */; };
		0B5782431B8BC151002F950F /* lzfse.h in Headers */ = {isa = PBXBuildFile; fileRef = 0B57823A1B8BC151002F950F /* lzfse.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0BB39B9F1BE140C3004F4733 /* lzfse_fse.c in Sources */ = {isa = PBXBuildFile; fileRef = 0BB39B9E1BE13F12004F4733 /* lzfse_fse.c */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		0B1AF7DD1B8D0F16005BF60D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0B7CB3821B8BBEA500730478 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0B57822B1B8BC12F002F950F;
			remoteInfo = "lzfse lib";
		};
		0B1AF7DF1B8D0F16005BF60D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0B7CB3821B8BBEA500730478 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0B1AF7D11B8D0EBF005BF60D;
			remoteInfo = "lzfse command";
		};
		0BC009291C481C600068DBA1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0B7CB3821B8BBEA500730478 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0B57822B1B8BC12F002F950F;
			remoteInfo = "lzfse lib";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0B1AF7B71B8CF8A2005BF60D /* lzvn_decode_base.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = lzvn_decode_base.c; path = src/lzvn_decode_base.c; sourceTree = "<group>"; };
		0B1AF7B81B8CF8A2005BF60D /* lzvn_decode_base.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = lzvn_decode_base.h; path = src/lzvn_decode_base.h; sourceTree = "<group>"; };
		0B1AF7BB1B8CF8A2005BF60D /* lzvn_encode_base.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = lzvn_encode_base.c; path = src/lzvn_encode_base.c; sourceTree = "<group>"; };
		0B1AF7BC1B8CF8A2005BF60D /* lzvn_encode_base.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = lzvn_encode_base.h; path = src/lzvn_encode_base.h; sourceTree = "<group>"; };
		0B1AF7CB1B8CFF58005BF60D /* Makefile */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.make; path = Makefile; sourceTree = "<group>"; };
		0B1AF7D21B8D0EBF005BF60D /* lzfse */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = lzfse; sourceTree = BUILT_PRODUCTS_DIR; };
		0B1AF7E11B8D0F59005BF60D /* lzfse_main.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = lzfse_main.c; path = src/lzfse_main.c; sourceTree = "<group>"; };
		0B494D921D18A060002BE277 /* README.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		0B57822C1B8BC12F002F950F /* liblzfse.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = liblzfse.a; sourceTree = BUILT_PRODUCTS_DIR; };
		0B5782321B8BC151002F950F /* lzfse_decode_base.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = lzfse_decode_base.c; path = src/lzfse_decode_base.c; sourceTree = "<group>"; };
		0B5782341B8BC151002F950F /* lzfse_decode.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = lzfse_decode.c; path = src/lzfse_decode.c; sourceTree = "<group>"; };
		0B5782351B8BC151002F950F /* lzfse_encode_base.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = lzfse_encode_base.c; path = src/lzfse_encode_base.c; sourceTree = "<group>"; };
		0B5782361B8BC151002F950F /* lzfse_encode_tables.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = lzfse_encode_tables.h; path = src/lzfse_encode_tables.h; sourceTree = "<group>"; };
		0B5782371B8BC151002F950F /* lzfse_encode.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = lzfse_encode.c; path = src/lzfse_encode.c; sourceTree = "<group>"; };
		0B5782381B8BC151002F950F /* lzfse_fse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = lzfse_fse.h; path = src/lzfse_fse.h; sourceTree = "<group>"; };
		0B5782391B8BC151002F950F /* lzfse_internal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = lzfse_internal.h; path = src/lzfse_internal.h; sourceTree = "<group>"; };
		0B57823A1B8BC151002F950F /* lzfse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = lzfse.h; path = src/lzfse.h; sourceTree = "<group>"; };
		0BA652BD1D0733B20019BFE3 /* LICENSE */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = LICENSE; sourceTree = "<group>"; };
		0BB39B9E1BE13F12004F4733 /* lzfse_fse.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = lzfse_fse.c; path = src/lzfse_fse.c; sourceTree = "<group>"; };
		6301162F1BFE759100D6CAA3 /* lzfse_tunables.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = lzfse_tunables.h; path = src/lzfse_tunables.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0B1AF7CF1B8D0EBF005BF60D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				0B1AF7E41B8D1EFD005BF60D /* liblzfse.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0B5782291B8BC12F002F950F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0B1AF7CD1B8D0E88005BF60D /* LZFSE command */ = {
			isa = PBXGroup;
			children = (
				0B1AF7E11B8D0F59005BF60D /* lzfse_main.c */,
			);
			name = "LZFSE command";
			sourceTree = "<group>";
		};
		0B57822D1B8BC12F002F950F /* Products */ = {
			isa = PBXGroup;
			children = (
				0B57822C1B8BC12F002F950F /* liblzfse.a */,
				0B1AF7D21B8D0EBF005BF60D /* lzfse */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		0B5782311B8BC13D002F950F /* LZFSE lib */ = {
			isa = PBXGroup;
			children = (
				0B57823A1B8BC151002F950F /* lzfse.h */,
				6301162F1BFE759100D6CAA3 /* lzfse_tunables.h */,
				0B5782391B8BC151002F950F /* lzfse_internal.h */,
				0B5782381B8BC151002F950F /* lzfse_fse.h */,
				0BB39B9E1BE13F12004F4733 /* lzfse_fse.c */,
				0B5782361B8BC151002F950F /* lzfse_encode_tables.h */,
				0B5782351B8BC151002F950F /* lzfse_encode_base.c */,
				0B5782371B8BC151002F950F /* lzfse_encode.c */,
				0B5782321B8BC151002F950F /* lzfse_decode_base.c */,
				0B5782341B8BC151002F950F /* lzfse_decode.c */,
				0B1AF7BC1B8CF8A2005BF60D /* lzvn_encode_base.h */,
				0B1AF7BB1B8CF8A2005BF60D /* lzvn_encode_base.c */,
				0B1AF7B81B8CF8A2005BF60D /* lzvn_decode_base.h */,
				0B1AF7B71B8CF8A2005BF60D /* lzvn_decode_base.c */,
			);
			name = "LZFSE lib";
			sourceTree = "<group>";
		};
		0B7CB3811B8BBEA500730478 = {
			isa = PBXGroup;
			children = (
				0B494D921D18A060002BE277 /* README.md */,
				0BA652BD1D0733B20019BFE3 /* LICENSE */,
				0B1AF7CB1B8CFF58005BF60D /* Makefile */,
				0B1AF7CD1B8D0E88005BF60D /* LZFSE command */,
				0B5782311B8BC13D002F950F /* LZFSE lib */,
				0B57822D1B8BC12F002F950F /* Products */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		0B57822A1B8BC12F002F950F /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				0B5782431B8BC151002F950F /* lzfse.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		0B1AF7D11B8D0EBF005BF60D /* lzfse command */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0B1AF7D61B8D0EBF005BF60D /* Build configuration list for PBXNativeTarget "lzfse command" */;
			buildPhases = (
				0B1AF7CE1B8D0EBF005BF60D /* Sources */,
				0B1AF7CF1B8D0EBF005BF60D /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				0BC0092A1C481C600068DBA1 /* PBXTargetDependency */,
			);
			name = "lzfse command";
			productName = "lzfse command";
			productReference = 0B1AF7D21B8D0EBF005BF60D /* lzfse */;
			productType = "com.apple.product-type.tool";
		};
		0B57822B1B8BC12F002F950F /* lzfse lib */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0B5782301B8BC12F002F950F /* Build configuration list for PBXNativeTarget "lzfse lib" */;
			buildPhases = (
				0B5782281B8BC12F002F950F /* Sources */,
				0B5782291B8BC12F002F950F /* Frameworks */,
				0B57822A1B8BC12F002F950F /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "lzfse lib";
			productName = lzfse;
			productReference = 0B57822C1B8BC12F002F950F /* liblzfse.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0B7CB3821B8BBEA500730478 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0700;
				TargetAttributes = {
					0B1AF7D11B8D0EBF005BF60D = {
						CreatedOnToolsVersion = 7.0;
					};
					0B1AF7D91B8D0F0B005BF60D = {
						CreatedOnToolsVersion = 7.0;
					};
					0B57822B1B8BC12F002F950F = {
						CreatedOnToolsVersion = 7.0;
					};
				};
			};
			buildConfigurationList = 0B7CB3851B8BBEA500730478 /* Build configuration list for PBXProject "lzfse" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
			);
			mainGroup = 0B7CB3811B8BBEA500730478;
			productRefGroup = 0B57822D1B8BC12F002F950F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0B1AF7D91B8D0F0B005BF60D /* all */,
				0B57822B1B8BC12F002F950F /* lzfse lib */,
				0B1AF7D11B8D0EBF005BF60D /* lzfse command */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		0B1AF7CE1B8D0EBF005BF60D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0B1AF7E31B8D0F65005BF60D /* lzfse_main.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0B5782281B8BC12F002F950F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0B5782401B8BC151002F950F /* lzfse_encode.c in Sources */,
				0B1AF7C01B8CF8A2005BF60D /* lzvn_decode_base.c in Sources */,
				0B57823D1B8BC151002F950F /* lzfse_decode.c in Sources */,
				0B57823E1B8BC151002F950F /* lzfse_encode_base.c in Sources */,
				0B1AF7C41B8CF8A2005BF60D /* lzvn_encode_base.c in Sources */,
				0B57823B1B8BC151002F950F /* lzfse_decode_base.c in Sources */,
				0BB39B9F1BE140C3004F4733 /* lzfse_fse.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0B1AF7DE1B8D0F16005BF60D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0B57822B1B8BC12F002F950F /* lzfse lib */;
			targetProxy = 0B1AF7DD1B8D0F16005BF60D /* PBXContainerItemProxy */;
		};
		0B1AF7E01B8D0F16005BF60D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0B1AF7D11B8D0EBF005BF60D /* lzfse command */;
			targetProxy = 0B1AF7DF1B8D0F16005BF60D /* PBXContainerItemProxy */;
		};
		0BC0092A1C481C600068DBA1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0B57822B1B8BC12F002F950F /* lzfse lib */;
			targetProxy = 0BC009291C481C600068DBA1 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0B1AF7D71B8D0EBF005BF60D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = lzfse;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		0B1AF7D81B8D0EBF005BF60D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_NAME = lzfse;
				SDKROOT = macosx;
			};
			name = Release;
		};
		0B1AF7DB1B8D0F0B005BF60D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		0B1AF7DC1B8D0F0B005BF60D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		0B57822E1B8BC12F002F950F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				EXECUTABLE_PREFIX = lib;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				GENERATE_MASTER_OBJECT_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = lzfse;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		0B57822F1B8BC12F002F950F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				EXECUTABLE_PREFIX = lib;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				GENERATE_MASTER_OBJECT_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_NAME = lzfse;
				SDKROOT = macosx;
			};
			name = Release;
		};
		0B7CB3861B8BBEA500730478 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				GCC_C_LANGUAGE_STANDARD = c99;
			};
			name = Debug;
		};
		0B7CB3871B8BBEA500730478 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				GCC_C_LANGUAGE_STANDARD = c99;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0B1AF7D61B8D0EBF005BF60D /* Build configuration list for PBXNativeTarget "lzfse command" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0B1AF7D71B8D0EBF005BF60D /* Debug */,
				0B1AF7D81B8D0EBF005BF60D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0B1AF7DA1B8D0F0B005BF60D /* Build configuration list for PBXAggregateTarget "all" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0B1AF7DB1B8D0F0B005BF60D /* Debug */,
				0B1AF7DC1B8D0F0B005BF60D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0B5782301B8BC12F002F950F /* Build configuration list for PBXNativeTarget "lzfse lib" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0B57822E1B8BC12F002F950F /* Debug */,
				0B57822F1B8BC12F002F950F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0B7CB3851B8BBEA500730478 /* Build configuration list for PBXProject "lzfse" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0B7CB3861B8BBEA500730478 /* Debug */,
				0B7CB3871B8BBEA500730478 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0B7CB3821B8BBEA500730478 /* Project object */;
}
