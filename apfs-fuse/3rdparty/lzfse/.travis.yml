language: c
sudo: false
matrix:
  include:
    ###
    ## Linux builds using various versions of GCC.
    ###
    - os: linux
      env: C_COMPILER=gcc-6
      addons:
        apt:
          sources:
          - ubuntu-toolchain-r-test
          packages:
          - gcc-6
    - os: linux
      env: C_COMPILER=gcc-5
      addons:
        apt:
          sources:
          - ubuntu-toolchain-r-test
          packages:
          - gcc-5
    - os: linux
      env: C_COMPILER=gcc-4.9
      addons:
        apt:
          sources:
          - ubuntu-toolchain-r-test
          packages:
          - gcc-4.9
    - os: linux
      env: C_COMPILER=gcc-4.8
      addons:
        apt:
          sources:
          - ubuntu-toolchain-r-test
          packages:
          - gcc-4.8
    - os: linux
      env: C_COMPILER=gcc-4.7
      addons:
        apt:
          sources:
          - ubuntu-toolchain-r-test
          packages:
          - gcc-4.7
    - os: linux
      env: C_COMPILER=gcc-4.6
      addons:
        apt:
          sources:
          - ubuntu-toolchain-r-test
          packages:
          - gcc-4.6
    - os: linux
      env: C_COMPILER=gcc-4.5
      addons:
        apt:
          sources:
          - ubuntu-toolchain-r-test
          packages:
          - gcc-4.5
    - os: linux
      env: C_COMPILER=gcc-4.4
      addons:
        apt:
          sources:
          - ubuntu-toolchain-r-test
          packages:
          - gcc-4.4

    ###
    ## clang on Linux
    ###
    - os: linux
      env: C_COMPILER=clang-3.8
      addons:
        apt:
          sources:
          - llvm-toolchain-precise-3.8
          - ubuntu-toolchain-r-test
          packages:
          - clang-3.8
    - os: linux
      env: C_COMPILER=clang-3.7
      addons:
        apt:
          sources:
          - llvm-toolchain-precise-3.7
          - ubuntu-toolchain-r-test
          packages:
          - clang-3.7
    - os: linux
      env: C_COMPILER=clang-3.6
      addons:
        apt:
          sources:
          - llvm-toolchain-precise-3.6
          - ubuntu-toolchain-r-test
          packages:
          - clang-3.6
    - os: linux
      env: C_COMPILER=clang-3.5
      addons:
        apt:
          sources:
          - llvm-toolchain-precise-3.5
          - ubuntu-toolchain-r-test
          packages:
          - clang-3.5

    ###
    ## Sanitizers
    ###
    - os: linux
      env: C_COMPILER=clang-3.8 SANITIZER=address
      addons:
        apt:
          sources:
          - ubuntu-toolchain-r-test
          - llvm-toolchain-precise-3.8
          packages:
          - clang-3.8
    - os: linux
      env: C_COMPILER=clang-3.8 SANITIZER=thread
      addons:
        apt:
          sources:
          - ubuntu-toolchain-r-test
          - llvm-toolchain-precise-3.8
          packages:
          - clang-3.8
    - os: linux
      env: C_COMPILER=clang-3.8 SANITIZER=undefined CFLAGS="-fno-sanitize-recover=undefined,integer"
      addons:
        apt:
          sources:
          - ubuntu-toolchain-r-test
          - llvm-toolchain-precise-3.8
          packages:
          - clang-3.8

before_install:
###
## If we use the matrix to set CC/CXX Travis, overwrites the values,
## so instead we use C/CXX_COMPILER, then copy the values to CC/CXX
## here (after Travis has set CC/CXX).
###
- if [ -n "${C_COMPILER}" ]; then export CC="${C_COMPILER}"; fi
- mkdir build
- cd build

script:
- cmake --version
- cmake .. -DCMAKE_C_FLAGS="${CFLAGS}" -DENABLE_SANITIZER="${SANITIZER}"
- make VERBOSE=1
- ctest -V
