#include <cstdint>

static const uint16_t nf_trie_hi[0x300] =
{
	// 0000
	0xC000, 0xC001, 0xC002, 0xC003, 0xC004, 0xC005, 0xC006, 0xC007, 0xC008, 0xC009, 0xC00A, 0xC00B, 0xC00C, 0xC00D, 0xC00E, 0xC00F,
	0xC010, 0x0000, 0xC011, 0xC012, 0x0000, 0x0000, 0xC013, 0xC014, 0xC015, 0xC016, 0xC017, 0xC018, 0xC019, 0xC01A, 0xC01B, 0xC01C,
	0xC01D, 0xC01E, 0xC01F, 0xC020, 0xC021, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC022, 0xC023, 0xC024, 0xC025, 0xC026, 0xC027,
	0xC028, 0xC029, 0xC02A, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC02B, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	// 0080
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC02C,
	0x0000, 0x0000, 0x0000, 0x0000, 0xC02D, 0x0000, 0xC02E, 0xC02F, 0xC030, 0xC031, 0xC032, 0xC033, 0xAC00, 0xAC00, 0xAC00, 0xAC00,
	0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00,
	0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00,
	0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xC034, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC035, 0xC036, 0xC037, 0x0000, 0xC038, 0xC039, 0xC03A,
	// 0100
	0xC03B, 0xC03C, 0xC03D, 0xC03E, 0xC03F, 0xC040, 0x0000, 0xC041, 0xC042, 0xC043, 0xC044, 0xC045, 0xC046, 0xFFFF, 0xC047, 0xFFFF,
	0xC048, 0xC049, 0xC04A, 0xC04B, 0xC04C, 0xC04D, 0xC04E, 0xC04F, 0xC050, 0xFFFF, 0xC051, 0xFFFF, 0xC052, 0xC053, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0xC054, 0xC055, 0xC056, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0xC057, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0xC058, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0xC059, 0xC05A, 0xFFFF, 0xFFFF, 0xFFFF, 0xC05B,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	// 0180
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC05C, 0x0000, 0x0000, 0xC05D, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0xC05E, 0xC05F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC060, 0xFFFF, 0xFFFF, 0xFFFF,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xC061, 0xC062, 0xC063, 0xC064, 0xC065, 0xC066, 0xC067, 0xC068, 0x0000, 0x0000, 0xC069, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xC06A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC06B, 0xC06C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC06D, 0xFFFF,
	0xC06E, 0xC06F, 0xC070, 0x0000, 0x0000, 0x0000, 0xC071, 0xC072, 0xC073, 0xC074, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	// 0200
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	// 0280
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC075, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC076, 0xC077, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC078, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC079, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC07A, 0xC07B, 0xC07C, 0xC07D, 0xC07E, 0x0000, 0x0000, 0x0000
};

static const uint16_t nf_trie_mid[0x7F0] =
{
	// 0000
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC000, 0xC001, 0xC002, 0xC003,
	0xC004, 0xC005, 0xC006, 0xC007, 0xC008, 0xC009, 0xC00A, 0xC00B, 0x0000, 0x0000, 0xC00C, 0xC00D, 0xC00E, 0xC00F, 0xC010, 0xC011,
	0xC012, 0xC013, 0xC014, 0xC015, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xADE6, 0xC016, 0xC017, 0xC018, 0xC019, 0xC01A, 0xC01B, 0xC01C, 0xC01D, 0xC01E, 0xC01F, 0xC020, 0xC021, 0xC022, 0x0000, 0x0000,
	0xC023, 0xC024, 0x0000, 0xC025, 0x0000, 0xC026, 0x0000, 0xC027, 0xC028, 0x0000, 0x0000, 0x0000, 0xC029, 0xC02A, 0xC02B, 0xC02C,
	0xC02D, 0xC02E, 0xC02F, 0xC030, 0xC031, 0xC032, 0xAE00, 0x0000, 0xC033, 0xC034, 0xC035, 0xC036, 0xC037, 0x0000, 0xAE01, 0xAE02,
	0x0000, 0xC038, 0xC039, 0x0000, 0xC03A, 0xC03B, 0x0000, 0xC03C, 0x0000, 0x0000, 0x0000, 0x0000, 0xC03D, 0xC03E, 0xC03F, 0x0000,
	0xAE03, 0xC040, 0x0000, 0xC041, 0xC042, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE04, 0x0000, 0x0000, 0xC043, 0xC044,
	// 0080
	0x0000, 0xC045, 0xC046, 0xAE05, 0x0000, 0xC047, 0xAE01, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0xAE06, 0xFFFF, 0xC048, 0xC049, 0xC04A,
	0x0000, 0x0000, 0xC04B, 0xC04C, 0xC04D, 0xC04E, 0x0000, 0x0000, 0xAE07, 0xAE08, 0xAE09, 0xC04F, 0xC050, 0xC051, 0xAE0A, 0xAE0B,
	0xAE0C, 0xAE08, 0xAE09, 0xC052, 0xC053, 0xC054, 0xAE0D, 0xAE0E, 0xAE0F, 0xAE10, 0xAE09, 0xC055, 0xC056, 0xAE11, 0xAE0A, 0xAE12,
	0xAE13, 0xAE08, 0xAE09, 0xC057, 0xC058, 0xC059, 0xAE0A, 0xAE14, 0xAE15, 0xC05A, 0xAE16, 0xAE17, 0xC05B, 0xAE18, 0xAE0D, 0xAE01,
	0xAE19, 0xAE1A, 0xAE09, 0xAE1B, 0xC05C, 0xC05D, 0xAE0A, 0xAE1C, 0xAE19, 0xAE1A, 0xAE09, 0xC05E, 0xC05F, 0xAE1D, 0xAE0A, 0xAE1E,
	0xAE19, 0xAE1A, 0x0000, 0xC060, 0xC061, 0xAE1F, 0xAE0A, 0x0000, 0xAE20, 0xAE21, 0x0000, 0xAE22, 0xC062, 0xC063, 0xAE0D, 0xAE23,
	0xAE00, 0x0000, 0x0000, 0xC064, 0xC065, 0xAE24, 0xFFFF, 0xFFFF, 0xAE25, 0xAE26, 0xAE27, 0xC066, 0xC067, 0xAE28, 0xFFFF, 0xFFFF,
	0x0000, 0xC068, 0x0000, 0xC069, 0xC06A, 0xC06B, 0xC06C, 0xC06D, 0xC06E, 0xC06F, 0xC070, 0xC071, 0xC072, 0xAE01, 0xFFFF, 0xFFFF,
	// 0100
	0x0000, 0x0000, 0xC073, 0xC074, 0x0000, 0x0000, 0x0000, 0x0000, 0xC075, 0x0000, 0xC076, 0xC077, 0xC078, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0xAE29, 0xAE2A, 0x0000, 0x0000, 0xAE29, 0x0000, 0x0000, 0xAE2B, 0xAE2C, 0xAE2D, 0x0000, 0x0000,
	0x0000, 0xAE2C, 0x0000, 0x0000, 0x0000, 0xC079, 0x0000, 0xAE2E, 0x0000, 0xAE2F, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC07A,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE2E, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE30,
	0xAE31, 0xC07B, 0x0000, 0xC07C, 0x0000, 0xAE32, 0xAE31, 0xAE33, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC07D, 0xAE2F, 0xAE2F,
	0xAE05, 0xAE2F, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE14, 0x0000, 0x0000, 0xC07E, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE0E,
	0x0000, 0xAE05, 0xAE24, 0xC07F, 0xAE34, 0x0000, 0xAE0B, 0xAE02, 0x0000, 0x0000, 0xAE24, 0x0000, 0xAE2F, 0xAE35, 0x0000, 0x0000,
	0x0000, 0xC080, 0x0000, 0x0000, 0x0000, 0xAE05, 0xC081, 0xC082, 0xAE2F, 0xAE2F, 0xAE0B, 0xC083, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	// 0180
	0xC084, 0xC085, 0x0000, 0xC086, 0xC087, 0x0000, 0xC088, 0xC089, 0x0000, 0x0000, 0xC08A, 0x0000, 0x0000, 0x0000, 0xC08B, 0xC08C,
	0x0000, 0x0000, 0x0000, 0xC08D, 0xAE1B, 0x0000, 0x0000, 0x0000, 0xC08E, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE14, 0xC08F, 0xC090, 0xC091,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC092, 0xC093, 0xADE6, 0xC094,
	0xC095, 0xC096, 0xC097, 0xC098, 0xC099, 0xC09A, 0xC09B, 0xC09C, 0xC09D, 0xC09E, 0xC09F, 0xC0A0, 0xC0A1, 0xC0A2, 0xC0A3, 0xC0A4,
	0xC0A5, 0xC0A6, 0xC0A7, 0xC0A8, 0xC0A9, 0xC0AA, 0xC0AB, 0xC0AC, 0xC0AD, 0xC0AE, 0xC0AF, 0xC0B0, 0xC0B1, 0xC0B2, 0xC0B3, 0xC0B4,
	0xC0B5, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE36, 0xAE37, 0xAE05, 0xAE2E, 0x0000, 0x0000, 0xFFFF, 0xC0B6, 0xC0B7, 0xC0B8,
	0x0000, 0x0000, 0xC0B9, 0xC0BA, 0x0000, 0x0000, 0xC0BB, 0x0000, 0xC0BC, 0xC0BD, 0xC0BE, 0x0000, 0xC0BF, 0x0000, 0x0000, 0x0000,
	0xC0C0, 0x0000, 0xC0C1, 0x0000, 0xC0C2, 0x0000, 0xC0C3, 0xC0C4, 0xC0C5, 0x0000, 0xC0C6, 0x0000, 0x0000, 0x0000, 0xC0C7, 0x0000,
	// 0200
	0x0000, 0x0000, 0xC0C8, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0xAE38, 0xFFFF, 0xAE01, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC0C9, 0xC0CA, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC0CB, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE0A, 0x0000, 0xAE39, 0x0000, 0xAE1B, 0xAE09, 0xAE3A, 0xAE3B, 0xFFFF,
	0xC0CC, 0xC0CD, 0xC0CE, 0x0000, 0x0000, 0xAE05, 0xC0CF, 0xC0D0, 0xC0D1, 0xC0D2, 0xC0D3, 0xC0D4, 0xC0D5, 0xC0D6, 0xC0D7, 0xC0D8,
	0x0000, 0x0000, 0xAE3C, 0x0000, 0x0000, 0x0000, 0xAE3D, 0xC0D9, 0x0000, 0xAE38, 0xAE3E, 0xAE3E, 0xAE3E, 0xAE3E, 0xADE6, 0xADE6,
	0x0000, 0x0000, 0x0000, 0x0000, 0xAE2F, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0xAE3F, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE32,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE0E, 0xFFFF, 0xAE24,
	// 0280
	0x0000, 0x0000, 0xC0DA, 0x0000, 0xC0DB, 0xC0DC, 0xC0DD, 0xC0DE, 0x0000, 0xC0DF, 0xC0E0, 0xC0E1, 0xC0E2, 0xC0E3, 0x0000, 0xC0E4,
	0xAE40, 0x0000, 0xAE05, 0xAE00, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE05, 0x0000, 0x0000, 0xAE01, 0x0000, 0x0000, 0xAE32, 0x0000,
	0x0000, 0xAE05, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE05,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE0E, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE01, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE2E, 0x0000, 0x0000, 0x0000, 0xAE38, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0xAE24, 0xFFFF, 0xC0E5, 0xC0E6, 0xC0E7, 0xC0E8, 0xC0E9, 0xC0EA, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC0EB,
	0x0000, 0x0000, 0xC0EC, 0xC0ED, 0xC0EE, 0xC0EF, 0xC0F0, 0xC0F1, 0xC0F2, 0xC0F3, 0xC0F4, 0xC0F5, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE41,
	// 0300
	0xC0F6, 0x0000, 0xAE24, 0xAE2F, 0x0000, 0x0000, 0x0000, 0xAE14, 0x0000, 0x0000, 0x0000, 0x0000, 0xC0F7, 0xAE2F, 0xADE6, 0xC0F8,
	0x0000, 0x0000, 0xC0F9, 0x0000, 0x0000, 0xC0FA, 0x0000, 0xAE2E, 0x0000, 0x0000, 0x0000, 0xC0FB, 0xC0FC, 0xAE17, 0x0000, 0xAE05,
	0x0000, 0x0000, 0x0000, 0xAE38, 0xAE0B, 0xAE28, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xC0FD, 0xC0FE, 0xAE42, 0x0000, 0xC0FF,
	0xAE43, 0xAE44, 0xAE3E, 0x0000, 0x0000, 0x0000, 0xAE0E, 0xC100, 0xC101, 0xC102, 0xC103, 0xC104, 0x0000, 0x0000, 0xC105, 0xAE2F,
	0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xC106, 0x0000, 0xAE45, 0x0000, 0x0000, 0xAE24,
	0xC107, 0xC108, 0xC109, 0xC10A, 0xC10B, 0xC10C, 0xC10D, 0xC10E, 0xC10F, 0xC110, 0xC111, 0xC112, 0xC113, 0xC114, 0xC115, 0xC116,
	0xC117, 0xC118, 0xC119, 0xC11A, 0xC11B, 0xC11C, 0xC11D, 0xC11E, 0xC11F, 0xC120, 0xC121, 0xC122, 0xC123, 0xC124, 0xFFFF, 0xFFFF,
	0xC125, 0xC126, 0xC127, 0xC128, 0xC129, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE04, 0xAE46, 0x0000, 0x0000,
	// 0380
	0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE47, 0x0000, 0x0000, 0xAE14, 0xFFFF, 0xFFFF, 0xAE0B,
	0x0000, 0xAE2F, 0xC12A, 0x0000, 0x0000, 0xAE48, 0xAE49, 0xAE36, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE4A,
	0xAE00, 0x0000, 0xC12B, 0xC12C, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE05, 0xAE4B, 0xAE4C, 0xAE3E, 0xAE4D,
	0xAE4E, 0x0000, 0xAE2D, 0xAE4F, 0xAE0B, 0xAE0B, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE01,
	0xAE50, 0x0000, 0x0000, 0xAE51, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE05, 0xAE24, 0xAE11, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0xC12D,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0xAE2E, 0x0000, 0x0000, 0x0000, 0xAE11, 0xC12E, 0xAE24,
	0x0000, 0x0000, 0xAE52, 0x0000, 0xAE01, 0x0000, 0x0000, 0xC12F, 0x0000, 0xAE03, 0x0000, 0x0000, 0xAE53, 0xAE0E, 0xFFFF, 0xFFFF,
	0xC130, 0xC131, 0xC132, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE0B, 0xAE2F, 0xC133, 0xC134, 0xC135, 0x0000, 0xAE24,
	// 0400
	0x0000, 0x0000, 0xAE14, 0x0000, 0x0000, 0x0000, 0xAE54, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0xAE38, 0x0000, 0xAE0E, 0xAE14, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xAE55, 0x0000, 0x0000, 0xAE56, 0x0000, 0xAE57, 0x0000, 0x0000, 0x0000, 0xAE05, 0xAE41, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0xAE58,
	0x0000, 0xAE59, 0x0000, 0xAE5A, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0xAE5B, 0x0000, 0xAE47, 0x0000, 0x0000,
	0xC136, 0xAE5C, 0x0000, 0xC137, 0xAE14, 0xAE30, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0xC138, 0xAE38,
	0x0000, 0x0000, 0x0000, 0xAE5D, 0x0000, 0xAE39, 0x0000, 0xAE5E, 0x0000, 0xAE5F, 0xAE60, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0xAE30, 0xFFFF, 0xFFFF, 0xFFFF, 0xC139, 0xC13A, 0xC13B, 0xC13C, 0x0000, 0x0000, 0x0000, 0xAE61,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0xAE05, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	// 0480
	0x0000, 0x0000, 0x0000, 0x0000, 0xC13D, 0xAE47, 0x0000, 0xC13E, 0x0000, 0xC13F, 0xC140, 0xC141, 0xAE04, 0x0000, 0xAE30, 0xAE2F,
	0xC142, 0x0000, 0xC143, 0xC144, 0xAE32, 0x0000, 0x0000, 0xC145, 0x0000, 0x0000, 0x0000, 0x0000, 0xC146, 0x0000, 0xAE00, 0xAE02,
	0x0000, 0xAE10, 0x0000, 0xC147, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE62, 0xAE03, 0xAE2F, 0x0000, 0x0000, 0x0000, 0xC148, 0xAE2F,
	0xAE07, 0xAE08, 0xAE09, 0xC149, 0xC14A, 0xAE63, 0xC14B, 0xC14C, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0xC14D, 0xAE64, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0xC14E, 0xC14F, 0xAE2F, 0xFFFF, 0xFFFF,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0xC150, 0xC151, 0xAE0B, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0xC152, 0xAE02, 0xAE2F, 0xAE2E, 0xFFFF, 0x0000, 0x0000, 0x0000, 0xC153, 0xAE2F, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0xAE1B, 0xC154, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	// 0500
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xC155, 0xC156, 0x0000, 0x0000, 0x0000, 0xAE65,
	0x0000, 0x0000, 0x0000, 0xC157, 0xC158, 0x0000, 0x0000, 0x0000, 0xAE0A, 0xC159, 0xAE3A, 0xFFFF, 0x0000, 0x0000, 0x0000, 0xAE30,
	0xAE09, 0x0000, 0x0000, 0xC15A, 0xAE0E, 0x0000, 0xAE2E, 0x0000, 0x0000, 0xAE47, 0xAE66, 0xAE38, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xAE67, 0x0000, 0x0000, 0xAE68, 0xC15B, 0xAE2F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE2F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE05, 0xAE02, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0xAE32, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0xAE05, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	// 0580
	0x0000, 0x0000, 0x0000, 0x0000, 0xAE38, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0xAE30, 0x0000, 0xAE05, 0xAE17, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0xAE0B, 0xC15C,
	0x0000, 0x0000, 0x0000, 0xC15D, 0xAE0E, 0xAE3F, 0xAE10, 0xAE69, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0xAE02, 0x0000, 0x0000, 0xAE05, 0xAE6A, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE04, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE2E, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE3A,
	0x0000, 0xAE05, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE24,
	// 0600
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE01, 0xAE2E, 0xAE30, 0xC15E, 0xAE32, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE0E,
	0x0000, 0x0000, 0xAE6B, 0x0000, 0x0000, 0xC15F, 0xC160, 0xC161, 0xC162, 0x0000, 0xC163, 0xC164, 0xC165, 0x0000, 0xAE30, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0xC166, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE38, 0x0000, 0xAE04, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE36, 0x0000, 0x0000, 0x0000, 0xAE31, 0xAE6C, 0xAE6D, 0xAE6E, 0x0000, 0x0000, 0x0000,
	0xAE6F, 0xAE70, 0x0000, 0xAE71, 0xAE72, 0xAE1A, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE39, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	// 0680
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE73, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE24, 0xAE42, 0xAE00, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xC167, 0xC168, 0xC169, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE74, 0xC16A, 0xFFFF, 0xFFFF,
	0xC16B, 0xC16C, 0xC16D, 0x0000, 0xC16E, 0xAE17, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xAE6E, 0x0000, 0xAE75, 0xAE76, 0xAE77, 0xAE78, 0xAE79, 0xAE7A, 0xAE3F, 0xAE24, 0xAE7B, 0xAE24, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE04,
	0x0000, 0x0000, 0xAE24, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE32, 0xAE05, 0xAE00, 0xAE00, 0xAE00, 0x0000, 0xAE0E,
	0xAE2E, 0x0000, 0xAE05, 0x0000, 0x0000, 0x0000, 0xAE24, 0x0000, 0x0000, 0x0000, 0xAE2E, 0xFFFF, 0xFFFF, 0xFFFF, 0xAE0D, 0x0000,
	// 0700
	0xAE3A, 0x0000, 0x0000, 0xAE24, 0xAE30, 0xAE04, 0xAE0E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE02, 0xAE2E, 0xAE30,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE32, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE02, 0xFFFF, 0xFFFF,
	0xAE24, 0x0000, 0x0000, 0x0000, 0xAE14, 0xAE2F, 0x0000, 0x0000, 0xAE14, 0x0000, 0xAE0B, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xAE24, 0x0000, 0x0000, 0xAE05, 0xAE2E, 0x0000, 0xAE24, 0xFFFF, 0x0000, 0xAE14, 0xFFFF, 0xFFFF, 0xAE11, 0x0000, 0xAE38, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE38, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0xAE02, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0xAE0B, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	// 0780
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE04, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAE11, 0xFFFF,
	0xC16F, 0xC170, 0xC171, 0xC172, 0xC173, 0xC174, 0xC175, 0xC176, 0xC177, 0xC178, 0xC179, 0xC17A, 0xC17B, 0xC17C, 0xC17D, 0xC17E,
	0xC17F, 0xC180, 0xC181, 0xC182, 0xC183, 0xC184, 0xC185, 0xC186, 0xC187, 0xC188, 0xC189, 0xC18A, 0xC18B, 0xC18C, 0xC18D, 0xC18E,
	0xC18F, 0xC190, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xAE7C, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF
};

static const uint16_t nf_basic_cf[0x500] =
{
	// 0000
	0x0000, 0x0001, 0x0002, 0x0003, 0x0004, 0x0005, 0x0006, 0x0007, 0x0008, 0x0009, 0x000A, 0x000B, 0x000C, 0x000D, 0x000E, 0x000F,
	0x0010, 0x0011, 0x0012, 0x0013, 0x0014, 0x0015, 0x0016, 0x0017, 0x0018, 0x0019, 0x001A, 0x001B, 0x001C, 0x001D, 0x001E, 0x001F,
	0x0020, 0x0021, 0x0022, 0x0023, 0x0024, 0x0025, 0x0026, 0x0027, 0x0028, 0x0029, 0x002A, 0x002B, 0x002C, 0x002D, 0x002E, 0x002F,
	0x0030, 0x0031, 0x0032, 0x0033, 0x0034, 0x0035, 0x0036, 0x0037, 0x0038, 0x0039, 0x003A, 0x003B, 0x003C, 0x003D, 0x003E, 0x003F,
	0x0040, 0x0061, 0x0062, 0x0063, 0x0064, 0x0065, 0x0066, 0x0067, 0x0068, 0x0069, 0x006A, 0x006B, 0x006C, 0x006D, 0x006E, 0x006F,
	0x0070, 0x0071, 0x0072, 0x0073, 0x0074, 0x0075, 0x0076, 0x0077, 0x0078, 0x0079, 0x007A, 0x005B, 0x005C, 0x005D, 0x005E, 0x005F,
	0x0060, 0x0061, 0x0062, 0x0063, 0x0064, 0x0065, 0x0066, 0x0067, 0x0068, 0x0069, 0x006A, 0x006B, 0x006C, 0x006D, 0x006E, 0x006F,
	0x0070, 0x0071, 0x0072, 0x0073, 0x0074, 0x0075, 0x0076, 0x0077, 0x0078, 0x0079, 0x007A, 0x007B, 0x007C, 0x007D, 0x007E, 0x007F,
	// 0080
	0x0080, 0x0081, 0x0082, 0x0083, 0x0084, 0x0085, 0x0086, 0x0087, 0x0088, 0x0089, 0x008A, 0x008B, 0x008C, 0x008D, 0x008E, 0x008F,
	0x0090, 0x0091, 0x0092, 0x0093, 0x0094, 0x0095, 0x0096, 0x0097, 0x0098, 0x0099, 0x009A, 0x009B, 0x009C, 0x009D, 0x009E, 0x009F,
	0x00A0, 0x00A1, 0x00A2, 0x00A3, 0x00A4, 0x00A5, 0x00A6, 0x00A7, 0x00A8, 0x00A9, 0x00AA, 0x00AB, 0x00AC, 0x00AD, 0x00AE, 0x00AF,
	0x00B0, 0x00B1, 0x00B2, 0x00B3, 0x00B4, 0x03BC, 0x00B6, 0x00B7, 0x00B8, 0x00B9, 0x00BA, 0x00BB, 0x00BC, 0x00BD, 0x00BE, 0x00BF,
	0x00E0, 0x00E1, 0x00E2, 0x00E3, 0x00E4, 0x00E5, 0x00E6, 0x00E7, 0x00E8, 0x00E9, 0x00EA, 0x00EB, 0x00EC, 0x00ED, 0x00EE, 0x00EF,
	0x00F0, 0x00F1, 0x00F2, 0x00F3, 0x00F4, 0x00F5, 0x00F6, 0x00D7, 0x00F8, 0x00F9, 0x00FA, 0x00FB, 0x00FC, 0x00FD, 0x00FE, 0x00DF,
	0x00E0, 0x00E1, 0x00E2, 0x00E3, 0x00E4, 0x00E5, 0x00E6, 0x00E7, 0x00E8, 0x00E9, 0x00EA, 0x00EB, 0x00EC, 0x00ED, 0x00EE, 0x00EF,
	0x00F0, 0x00F1, 0x00F2, 0x00F3, 0x00F4, 0x00F5, 0x00F6, 0x00F7, 0x00F8, 0x00F9, 0x00FA, 0x00FB, 0x00FC, 0x00FD, 0x00FE, 0x00FF,
	// 0100
	0x0101, 0x0101, 0x0103, 0x0103, 0x0105, 0x0105, 0x0107, 0x0107, 0x0109, 0x0109, 0x010B, 0x010B, 0x010D, 0x010D, 0x010F, 0x010F,
	0x0111, 0x0111, 0x0113, 0x0113, 0x0115, 0x0115, 0x0117, 0x0117, 0x0119, 0x0119, 0x011B, 0x011B, 0x011D, 0x011D, 0x011F, 0x011F,
	0x0121, 0x0121, 0x0123, 0x0123, 0x0125, 0x0125, 0x0127, 0x0127, 0x0129, 0x0129, 0x012B, 0x012B, 0x012D, 0x012D, 0x012F, 0x012F,
	0x0130, 0x0131, 0x0133, 0x0133, 0x0135, 0x0135, 0x0137, 0x0137, 0x0138, 0x013A, 0x013A, 0x013C, 0x013C, 0x013E, 0x013E, 0x0140,
	0x0140, 0x0142, 0x0142, 0x0144, 0x0144, 0x0146, 0x0146, 0x0148, 0x0148, 0x0149, 0x014B, 0x014B, 0x014D, 0x014D, 0x014F, 0x014F,
	0x0151, 0x0151, 0x0153, 0x0153, 0x0155, 0x0155, 0x0157, 0x0157, 0x0159, 0x0159, 0x015B, 0x015B, 0x015D, 0x015D, 0x015F, 0x015F,
	0x0161, 0x0161, 0x0163, 0x0163, 0x0165, 0x0165, 0x0167, 0x0167, 0x0169, 0x0169, 0x016B, 0x016B, 0x016D, 0x016D, 0x016F, 0x016F,
	0x0171, 0x0171, 0x0173, 0x0173, 0x0175, 0x0175, 0x0177, 0x0177, 0x00FF, 0x017A, 0x017A, 0x017C, 0x017C, 0x017E, 0x017E, 0x0073,
	// 0180
	0x0180, 0x0253, 0x0183, 0x0183, 0x0185, 0x0185, 0x0254, 0x0188, 0x0188, 0x0256, 0x0257, 0x018C, 0x018C, 0x018D, 0x01DD, 0x0259,
	0x025B, 0x0192, 0x0192, 0x0260, 0x0263, 0x0195, 0x0269, 0x0268, 0x0199, 0x0199, 0x019A, 0x019B, 0x026F, 0x0272, 0x019E, 0x0275,
	0x01A1, 0x01A1, 0x01A3, 0x01A3, 0x01A5, 0x01A5, 0x0280, 0x01A8, 0x01A8, 0x0283, 0x01AA, 0x01AB, 0x01AD, 0x01AD, 0x0288, 0x01B0,
	0x01B0, 0x028A, 0x028B, 0x01B4, 0x01B4, 0x01B6, 0x01B6, 0x0292, 0x01B9, 0x01B9, 0x01BA, 0x01BB, 0x01BD, 0x01BD, 0x01BE, 0x01BF,
	0x01C0, 0x01C1, 0x01C2, 0x01C3, 0x01C6, 0x01C6, 0x01C6, 0x01C9, 0x01C9, 0x01C9, 0x01CC, 0x01CC, 0x01CC, 0x01CE, 0x01CE, 0x01D0,
	0x01D0, 0x01D2, 0x01D2, 0x01D4, 0x01D4, 0x01D6, 0x01D6, 0x01D8, 0x01D8, 0x01DA, 0x01DA, 0x01DC, 0x01DC, 0x01DD, 0x01DF, 0x01DF,
	0x01E1, 0x01E1, 0x01E3, 0x01E3, 0x01E5, 0x01E5, 0x01E7, 0x01E7, 0x01E9, 0x01E9, 0x01EB, 0x01EB, 0x01ED, 0x01ED, 0x01EF, 0x01EF,
	0x01F0, 0x01F3, 0x01F3, 0x01F3, 0x01F5, 0x01F5, 0x0195, 0x01BF, 0x01F9, 0x01F9, 0x01FB, 0x01FB, 0x01FD, 0x01FD, 0x01FF, 0x01FF,
	// 0200
	0x0201, 0x0201, 0x0203, 0x0203, 0x0205, 0x0205, 0x0207, 0x0207, 0x0209, 0x0209, 0x020B, 0x020B, 0x020D, 0x020D, 0x020F, 0x020F,
	0x0211, 0x0211, 0x0213, 0x0213, 0x0215, 0x0215, 0x0217, 0x0217, 0x0219, 0x0219, 0x021B, 0x021B, 0x021D, 0x021D, 0x021F, 0x021F,
	0x019E, 0x0221, 0x0223, 0x0223, 0x0225, 0x0225, 0x0227, 0x0227, 0x0229, 0x0229, 0x022B, 0x022B, 0x022D, 0x022D, 0x022F, 0x022F,
	0x0231, 0x0231, 0x0233, 0x0233, 0x0234, 0x0235, 0x0236, 0x0237, 0x0238, 0x0239, 0x2C65, 0x023C, 0x023C, 0x019A, 0x2C66, 0x023F,
	0x0240, 0x0242, 0x0242, 0x0180, 0x0289, 0x028C, 0x0247, 0x0247, 0x0249, 0x0249, 0x024B, 0x024B, 0x024D, 0x024D, 0x024F, 0x024F,
	0x0250, 0x0251, 0x0252, 0x0253, 0x0254, 0x0255, 0x0256, 0x0257, 0x0258, 0x0259, 0x025A, 0x025B, 0x025C, 0x025D, 0x025E, 0x025F,
	0x0260, 0x0261, 0x0262, 0x0263, 0x0264, 0x0265, 0x0266, 0x0267, 0x0268, 0x0269, 0x026A, 0x026B, 0x026C, 0x026D, 0x026E, 0x026F,
	0x0270, 0x0271, 0x0272, 0x0273, 0x0274, 0x0275, 0x0276, 0x0277, 0x0278, 0x0279, 0x027A, 0x027B, 0x027C, 0x027D, 0x027E, 0x027F,
	// 0280
	0x0280, 0x0281, 0x0282, 0x0283, 0x0284, 0x0285, 0x0286, 0x0287, 0x0288, 0x0289, 0x028A, 0x028B, 0x028C, 0x028D, 0x028E, 0x028F,
	0x0290, 0x0291, 0x0292, 0x0293, 0x0294, 0x0295, 0x0296, 0x0297, 0x0298, 0x0299, 0x029A, 0x029B, 0x029C, 0x029D, 0x029E, 0x029F,
	0x02A0, 0x02A1, 0x02A2, 0x02A3, 0x02A4, 0x02A5, 0x02A6, 0x02A7, 0x02A8, 0x02A9, 0x02AA, 0x02AB, 0x02AC, 0x02AD, 0x02AE, 0x02AF,
	0x02B0, 0x02B1, 0x02B2, 0x02B3, 0x02B4, 0x02B5, 0x02B6, 0x02B7, 0x02B8, 0x02B9, 0x02BA, 0x02BB, 0x02BC, 0x02BD, 0x02BE, 0x02BF,
	0x02C0, 0x02C1, 0x02C2, 0x02C3, 0x02C4, 0x02C5, 0x02C6, 0x02C7, 0x02C8, 0x02C9, 0x02CA, 0x02CB, 0x02CC, 0x02CD, 0x02CE, 0x02CF,
	0x02D0, 0x02D1, 0x02D2, 0x02D3, 0x02D4, 0x02D5, 0x02D6, 0x02D7, 0x02D8, 0x02D9, 0x02DA, 0x02DB, 0x02DC, 0x02DD, 0x02DE, 0x02DF,
	0x02E0, 0x02E1, 0x02E2, 0x02E3, 0x02E4, 0x02E5, 0x02E6, 0x02E7, 0x02E8, 0x02E9, 0x02EA, 0x02EB, 0x02EC, 0x02ED, 0x02EE, 0x02EF,
	0x02F0, 0x02F1, 0x02F2, 0x02F3, 0x02F4, 0x02F5, 0x02F6, 0x02F7, 0x02F8, 0x02F9, 0x02FA, 0x02FB, 0x02FC, 0x02FD, 0x02FE, 0x02FF,
	// 0300
	0x0300, 0x0301, 0x0302, 0x0303, 0x0304, 0x0305, 0x0306, 0x0307, 0x0308, 0x0309, 0x030A, 0x030B, 0x030C, 0x030D, 0x030E, 0x030F,
	0x0310, 0x0311, 0x0312, 0x0313, 0x0314, 0x0315, 0x0316, 0x0317, 0x0318, 0x0319, 0x031A, 0x031B, 0x031C, 0x031D, 0x031E, 0x031F,
	0x0320, 0x0321, 0x0322, 0x0323, 0x0324, 0x0325, 0x0326, 0x0327, 0x0328, 0x0329, 0x032A, 0x032B, 0x032C, 0x032D, 0x032E, 0x032F,
	0x0330, 0x0331, 0x0332, 0x0333, 0x0334, 0x0335, 0x0336, 0x0337, 0x0338, 0x0339, 0x033A, 0x033B, 0x033C, 0x033D, 0x033E, 0x033F,
	0x0340, 0x0341, 0x0342, 0x0343, 0x0344, 0x03B9, 0x0346, 0x0347, 0x0348, 0x0349, 0x034A, 0x034B, 0x034C, 0x034D, 0x034E, 0x034F,
	0x0350, 0x0351, 0x0352, 0x0353, 0x0354, 0x0355, 0x0356, 0x0357, 0x0358, 0x0359, 0x035A, 0x035B, 0x035C, 0x035D, 0x035E, 0x035F,
	0x0360, 0x0361, 0x0362, 0x0363, 0x0364, 0x0365, 0x0366, 0x0367, 0x0368, 0x0369, 0x036A, 0x036B, 0x036C, 0x036D, 0x036E, 0x036F,
	0x0371, 0x0371, 0x0373, 0x0373, 0x0374, 0x0375, 0x0377, 0x0377, 0x0378, 0x0379, 0x037A, 0x037B, 0x037C, 0x037D, 0x037E, 0x03F3,
	// 0380
	0x0380, 0x0381, 0x0382, 0x0383, 0x0384, 0x0385, 0x03AC, 0x0387, 0x03AD, 0x03AE, 0x03AF, 0x038B, 0x03CC, 0x038D, 0x03CD, 0x03CE,
	0x0390, 0x03B1, 0x03B2, 0x03B3, 0x03B4, 0x03B5, 0x03B6, 0x03B7, 0x03B8, 0x03B9, 0x03BA, 0x03BB, 0x03BC, 0x03BD, 0x03BE, 0x03BF,
	0x03C0, 0x03C1, 0x03A2, 0x03C3, 0x03C4, 0x03C5, 0x03C6, 0x03C7, 0x03C8, 0x03C9, 0x03CA, 0x03CB, 0x03AC, 0x03AD, 0x03AE, 0x03AF,
	0x03B0, 0x03B1, 0x03B2, 0x03B3, 0x03B4, 0x03B5, 0x03B6, 0x03B7, 0x03B8, 0x03B9, 0x03BA, 0x03BB, 0x03BC, 0x03BD, 0x03BE, 0x03BF,
	0x03C0, 0x03C1, 0x03C3, 0x03C3, 0x03C4, 0x03C5, 0x03C6, 0x03C7, 0x03C8, 0x03C9, 0x03CA, 0x03CB, 0x03CC, 0x03CD, 0x03CE, 0x03D7,
	0x03B2, 0x03B8, 0x03D2, 0x03D3, 0x03D4, 0x03C6, 0x03C0, 0x03D7, 0x03D9, 0x03D9, 0x03DB, 0x03DB, 0x03DD, 0x03DD, 0x03DF, 0x03DF,
	0x03E1, 0x03E1, 0x03E3, 0x03E3, 0x03E5, 0x03E5, 0x03E7, 0x03E7, 0x03E9, 0x03E9, 0x03EB, 0x03EB, 0x03ED, 0x03ED, 0x03EF, 0x03EF,
	0x03BA, 0x03C1, 0x03F2, 0x03F3, 0x03B8, 0x03B5, 0x03F6, 0x03F8, 0x03F8, 0x03F2, 0x03FB, 0x03FB, 0x03FC, 0x037B, 0x037C, 0x037D,
	// 0400
	0x0450, 0x0451, 0x0452, 0x0453, 0x0454, 0x0455, 0x0456, 0x0457, 0x0458, 0x0459, 0x045A, 0x045B, 0x045C, 0x045D, 0x045E, 0x045F,
	0x0430, 0x0431, 0x0432, 0x0433, 0x0434, 0x0435, 0x0436, 0x0437, 0x0438, 0x0439, 0x043A, 0x043B, 0x043C, 0x043D, 0x043E, 0x043F,
	0x0440, 0x0441, 0x0442, 0x0443, 0x0444, 0x0445, 0x0446, 0x0447, 0x0448, 0x0449, 0x044A, 0x044B, 0x044C, 0x044D, 0x044E, 0x044F,
	0x0430, 0x0431, 0x0432, 0x0433, 0x0434, 0x0435, 0x0436, 0x0437, 0x0438, 0x0439, 0x043A, 0x043B, 0x043C, 0x043D, 0x043E, 0x043F,
	0x0440, 0x0441, 0x0442, 0x0443, 0x0444, 0x0445, 0x0446, 0x0447, 0x0448, 0x0449, 0x044A, 0x044B, 0x044C, 0x044D, 0x044E, 0x044F,
	0x0450, 0x0451, 0x0452, 0x0453, 0x0454, 0x0455, 0x0456, 0x0457, 0x0458, 0x0459, 0x045A, 0x045B, 0x045C, 0x045D, 0x045E, 0x045F,
	0x0461, 0x0461, 0x0463, 0x0463, 0x0465, 0x0465, 0x0467, 0x0467, 0x0469, 0x0469, 0x046B, 0x046B, 0x046D, 0x046D, 0x046F, 0x046F,
	0x0471, 0x0471, 0x0473, 0x0473, 0x0475, 0x0475, 0x0477, 0x0477, 0x0479, 0x0479, 0x047B, 0x047B, 0x047D, 0x047D, 0x047F, 0x047F,
	// 0480
	0x0481, 0x0481, 0x0482, 0x0483, 0x0484, 0x0485, 0x0486, 0x0487, 0x0488, 0x0489, 0x048B, 0x048B, 0x048D, 0x048D, 0x048F, 0x048F,
	0x0491, 0x0491, 0x0493, 0x0493, 0x0495, 0x0495, 0x0497, 0x0497, 0x0499, 0x0499, 0x049B, 0x049B, 0x049D, 0x049D, 0x049F, 0x049F,
	0x04A1, 0x04A1, 0x04A3, 0x04A3, 0x04A5, 0x04A5, 0x04A7, 0x04A7, 0x04A9, 0x04A9, 0x04AB, 0x04AB, 0x04AD, 0x04AD, 0x04AF, 0x04AF,
	0x04B1, 0x04B1, 0x04B3, 0x04B3, 0x04B5, 0x04B5, 0x04B7, 0x04B7, 0x04B9, 0x04B9, 0x04BB, 0x04BB, 0x04BD, 0x04BD, 0x04BF, 0x04BF,
	0x04CF, 0x04C2, 0x04C2, 0x04C4, 0x04C4, 0x04C6, 0x04C6, 0x04C8, 0x04C8, 0x04CA, 0x04CA, 0x04CC, 0x04CC, 0x04CE, 0x04CE, 0x04CF,
	0x04D1, 0x04D1, 0x04D3, 0x04D3, 0x04D5, 0x04D5, 0x04D7, 0x04D7, 0x04D9, 0x04D9, 0x04DB, 0x04DB, 0x04DD, 0x04DD, 0x04DF, 0x04DF,
	0x04E1, 0x04E1, 0x04E3, 0x04E3, 0x04E5, 0x04E5, 0x04E7, 0x04E7, 0x04E9, 0x04E9, 0x04EB, 0x04EB, 0x04ED, 0x04ED, 0x04EF, 0x04EF,
	0x04F1, 0x04F1, 0x04F3, 0x04F3, 0x04F5, 0x04F5, 0x04F7, 0x04F7, 0x04F9, 0x04F9, 0x04FB, 0x04FB, 0x04FD, 0x04FD, 0x04FF, 0x04FF
};

static const uint16_t nf_u16_inv_masks[0x80] =
{
	// 0000
	0x0001, 0xF800, 0xFFE0, 0x4000, 0xFFFC, 0x8000, 0xC020, 0x6010, 0x0006, 0x0200, 0x0030, 0xC000, 0x7811, 0x003F, 0xFFC0, 0x4011,
	0x0004, 0xFFFE, 0x01FC, 0x6011, 0xFF00, 0x3813, 0x38E7, 0x3C00, 0xFF7E, 0x2010, 0x0002, 0x1C00, 0x00FF, 0xBF9F, 0xFFF9, 0x000F,
	0x0013, 0x0380, 0xD004, 0xFFE3, 0xF000, 0xDA69, 0x010F, 0x1351, 0x0C00, 0xC200, 0xC280, 0x80C2, 0x00C2, 0x0080, 0xE000, 0xFC00,
	0xFE00, 0x2000, 0xFFF0, 0xFFF2, 0x000E, 0x3800, 0x0020, 0x000C, 0xFF80, 0x00C0, 0xFFF8, 0x0FFF, 0xDF40, 0x7F00, 0x8080, 0x0400,
	0x001F, 0x007F, 0x07FF, 0x8181, 0xFF81, 0x0780, 0x0007, 0x0003, 0x0008, 0xF080, 0x6000, 0x0303, 0xE303, 0xC1FF, 0x1000, 0x4800,
	0x0078, 0x0070, 0x1FF0, 0x00F0, 0x7FF0, 0x02C0, 0x6E40, 0x0040, 0x07C8, 0x7000, 0x7C00, 0x0F00, 0x0110, 0x01C0, 0x00F8, 0xE1FC,
	0x01FF, 0x03F8, 0x4280, 0x1F7E, 0xD400, 0x7FF8, 0x0100, 0x0480, 0x4B80, 0x1F00, 0x7FFF, 0x0180, 0x219B, 0x1400, 0x0010, 0x1840,
	0x2020, 0x8400, 0x03A0, 0x3000, 0x0060, 0x0169, 0xF508, 0x157B, 0x5569, 0x0869, 0xA108, 0x0411, 0xFFFD, 0x0000, 0x0000, 0x0000
};

static const uint16_t nf_trie_lo[0x1910] =
{
	// 0000
	0xB000, 0xB001, 0xB002, 0xB003, 0xB004, 0xB005, 0x0000, 0xB006, 0xB007, 0xB008, 0xB009, 0xB00A, 0xB00B, 0xB00C, 0xB00D, 0xB00E,
	0x0000, 0xB00F, 0xB010, 0xB011, 0xB012, 0xB013, 0xB014, 0x0000, 0x0000, 0xB015, 0xB016, 0xB017, 0xB018, 0xB019, 0x0000, 0xB81A,
	0xB01B, 0xB01C, 0xB01D, 0xB01E, 0xB01F, 0xB020, 0x0000, 0xB021, 0xB022, 0xB023, 0xB024, 0xB025, 0xB026, 0xB027, 0xB028, 0xB029,
	0x0000, 0xB02A, 0xB02B, 0xB02C, 0xB02D, 0xB02E, 0xB02F, 0x0000, 0x0000, 0xB030, 0xB031, 0xB032, 0xB033, 0xB034, 0x0000, 0xB035,
	0xB036, 0xB037, 0xB038, 0xB039, 0xB03A, 0xB03B, 0xB03C, 0xB03D, 0xB03E, 0xB03F, 0xB040, 0xB041, 0xB042, 0xB043, 0xB044, 0xB045,
	0x0000, 0x0000, 0xB046, 0xB047, 0xB048, 0xB049, 0xB04A, 0xB04B, 0xB04C, 0xB04D, 0xB04E, 0xB04F, 0xB050, 0xB051, 0xB052, 0xB053,
	0xB054, 0xB055, 0xB056, 0xB057, 0xB058, 0xB059, 0x0000, 0x0000, 0xB05A, 0xB05B, 0xB05C, 0xB05D, 0xB05E, 0xB05F, 0xB060, 0xB061,
	0xB062, 0x0000, 0x0000, 0x0000, 0xB063, 0xB064, 0xB065, 0xB066, 0x0000, 0xB067, 0xB068, 0xB069, 0xB06A, 0xB06B, 0xB06C, 0x0000,
	// 0080
	0x0000, 0x0000, 0x0000, 0xB06D, 0xB06E, 0xB06F, 0xB070, 0xB071, 0xB072, 0xB873, 0x0000, 0x0000, 0xB074, 0xB075, 0xB076, 0xB077,
	0xB078, 0xB079, 0x0000, 0x0000, 0xB07A, 0xB07B, 0xB07C, 0xB07D, 0xB07E, 0xB07F, 0xB080, 0xB081, 0xB082, 0xB083, 0xB084, 0xB085,
	0xB086, 0xB087, 0xB088, 0xB089, 0xB08A, 0xB08B, 0x0000, 0x0000, 0xB08C, 0xB08D, 0xB08E, 0xB08F, 0xB090, 0xB091, 0xB092, 0xB093,
	0xB094, 0xB095, 0xB096, 0xB097, 0xB098, 0xB099, 0xB09A, 0xB09B, 0xB09C, 0xB09D, 0xB09E, 0xB09F, 0xB0A0, 0xB0A1, 0xB0A2, 0x0000,
	0xB0A3, 0xB0A4, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB0A5,
	0xB0A6, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB0A7, 0xB0A8, 0xB0A9,
	0xB0AA, 0xB0AB, 0xB0AC, 0xB0AD, 0xB0AE, 0xC000, 0xC001, 0xC002, 0xC003, 0xC004, 0xC005, 0xC006, 0xC007, 0x0000, 0xC008, 0xC009,
	// 0100
	0xC00A, 0xC00B, 0xB0AF, 0xB0B0, 0x0000, 0x0000, 0xB0B1, 0xB0B2, 0xB0B3, 0xB0B4, 0xB0B5, 0xB0B6, 0xC00C, 0xC00D, 0xB0B7, 0xB0B8,
	0xB0B9, 0x0000, 0x0000, 0x0000, 0xB0BA, 0xB0BB, 0x0000, 0x0000, 0xB0BC, 0xB0BD, 0xC00E, 0xC00F, 0xB0BE, 0xB0BF, 0xB0C0, 0xB0C1,
	0xB0C2, 0xB0C3, 0xB0C4, 0xB0C5, 0xB0C6, 0xB0C7, 0xB0C8, 0xB0C9, 0xB0CA, 0xB0CB, 0xB0CC, 0xB0CD, 0xB0CE, 0xB0CF, 0xB0D0, 0xB0D1,
	0xB0D2, 0xB0D3, 0xB0D4, 0xB0D5, 0xB0D6, 0xB0D7, 0xB0D8, 0xB0D9, 0xB0DA, 0xB0DB, 0xB0DC, 0xB0DD, 0x0000, 0x0000, 0xB0DE, 0xB0DF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB0E0, 0xB0E1, 0xB0E2, 0xB0E3, 0xC010, 0xC011, 0xC012, 0xC013, 0xB0E4, 0xB0E5,
	0xC014, 0xC015, 0xB0E6, 0xB0E7, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE8, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADE8, 0xADD8, 0xADDC, 0xADDC, 0xADDC, 0xADDC,
	0xADDC, 0xADCA, 0xADCA, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADCA, 0xADCA, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC,
	// 0180
	0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xAD01, 0xAD01, 0xAD01, 0xAD01, 0xAD01, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADE6, 0xADE6, 0xADE6,
	0xD000, 0xD002, 0xADE6, 0xD004, 0xD006, 0xADF0, 0xADE6, 0xADDC, 0xADDC, 0xADDC, 0xADE6, 0xADE6, 0xADE6, 0xADDC, 0xADDC, 0x0000,
	0xADE6, 0xADE6, 0xADE6, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADE6, 0xADE8, 0xADDC, 0xADDC, 0xADE6, 0xADE9, 0xADEA, 0xADEA, 0xADE9,
	0xADEA, 0xADEA, 0xADE9, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6,
	0x0000, 0x0000, 0x0000, 0x0000, 0x02B9, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x003B, 0x0000,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0xB0E8, 0xB0E9, 0x00B7, 0xB0EA, 0xB0EB, 0xB0EC, 0xFFFF, 0xB0ED, 0xFFFF, 0xB0EE, 0xB0EF,
	0xC016, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB0F0, 0xB0F1, 0xB0F2, 0xB0F3, 0xB0F4, 0xB0F5,
	// 0200
	0xC017, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB0F6, 0xB0F7, 0xB0F8, 0xB0F9, 0xB0FA, 0x0000,
	0x0000, 0x0000, 0x0000, 0xB0FB, 0xB0FC, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xB0FD, 0xB0FE, 0x0000, 0xB0FF, 0x0000, 0x0000, 0x0000, 0xB100, 0x0000, 0x0000, 0x0000, 0x0000, 0xB101, 0xB102, 0xB103, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB104, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB105, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xB106, 0xB107, 0x0000, 0xB108, 0x0000, 0x0000, 0x0000, 0xB109, 0x0000, 0x0000, 0x0000, 0x0000, 0xB10A, 0xB10B, 0xB10C, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB10D, 0xB10E, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	// 0280
	0x0000, 0x0000, 0x0000, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0xB10F, 0xB110, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xB111, 0xB112, 0xB113, 0xB114, 0x0000, 0x0000, 0xB115, 0xB116, 0x0000, 0x0000, 0xB117, 0xB118, 0xB119, 0xB11A, 0xB11B, 0xB11C,
	0x0000, 0x0000, 0xB11D, 0xB11E, 0xB11F, 0xB120, 0xB121, 0xB122, 0x0000, 0x0000, 0xB123, 0xB124, 0xB125, 0xB126, 0xB127, 0xB128,
	0xB129, 0xB12A, 0xB12B, 0xB12C, 0xB12D, 0xB12E, 0x0000, 0x0000, 0xB12F, 0xB130, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xE800, 0x0000, 0xE801, 0x0000, 0xE802, 0x0000, 0xE803, 0x0000, 0xE804, 0x0000, 0xE805, 0x0000, 0xE806, 0x0000, 0xE807, 0x0000,
	0xE808, 0x0000, 0xE809, 0x0000, 0xE80A, 0x0000, 0xE80B, 0x0000, 0xE80C, 0x0000, 0xE80D, 0x0000, 0xE80E, 0x0000, 0xE80F, 0x0000,
	0xE810, 0x0000, 0xE811, 0x0000, 0xE812, 0x0000, 0xE813, 0x0000, 0xE814, 0x0000, 0xE815, 0x0000, 0xE816, 0x0000, 0xE817, 0x0000,
	// 0300
	0xFFFF, 0xE818, 0xE819, 0xE81A, 0xE81B, 0xE81C, 0xE81D, 0xE81E, 0xE81F, 0xE820, 0xE821, 0xE822, 0xE823, 0xE824, 0xE825, 0xE826,
	0xE827, 0xE828, 0xE829, 0xE82A, 0xE82B, 0xE82C, 0xE82D, 0xE82E, 0xE82F, 0xE830, 0xE831, 0xE832, 0xE833, 0xE834, 0xE835, 0xE836,
	0xE837, 0xE838, 0xE839, 0xE83A, 0xE83B, 0xE83C, 0xE83D, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB931, 0xFFFF, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000,
	0xFFFF, 0xADDC, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADDC, 0xADE6, 0xADE6, 0xADE6, 0xADDE, 0xADDC, 0xADE6, 0xADE6, 0xADE6, 0xADE6,
	0xADE6, 0xADE6, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADE6, 0xADE6, 0xADDC, 0xADE6, 0xADE6, 0xADDE, 0xADE4, 0xADE6,
	0xAD0A, 0xAD0B, 0xAD0C, 0xAD0D, 0xAD0E, 0xAD0F, 0xAD10, 0xAD11, 0xAD12, 0xAD13, 0xAD13, 0xAD14, 0xAD15, 0xAD16, 0x0000, 0xAD17,
	0x0000, 0xAD18, 0xAD19, 0x0000, 0xADE6, 0xADDC, 0x0000, 0xAD12, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	// 0380
	0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xAD1E, 0xAD1F, 0xAD20, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000,
	0x0000, 0x0000, 0xB132, 0xB133, 0xB134, 0xB135, 0xB136, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD1B, 0xAD1C, 0xAD1D, 0xAD1E, 0xAD1F,
	0xAD20, 0xAD21, 0xAD22, 0xADE6, 0xADE6, 0xADDC, 0xADDC, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADDC, 0xADE6, 0xADE6, 0xADDC,
	0xAD23, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xB137, 0x0000, 0xB138, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0xB139, 0x0000, 0x0000, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0x0000, 0x0000, 0xADE6,
	0xADE6, 0xADE6, 0xADE6, 0xADDC, 0xADE6, 0x0000, 0x0000, 0xADE6, 0xADE6, 0x0000, 0xADDC, 0xADE6, 0xADE6, 0xADDC, 0x0000, 0x0000,
	// 0400
	0x0000, 0xAD24, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xADE6, 0xADDC, 0xADE6, 0xADE6, 0xADDC, 0xADE6, 0xADE6, 0xADDC, 0xADDC, 0xADDC, 0xADE6, 0xADDC, 0xADDC, 0xADE6, 0xADDC, 0xADE6,
	0xADE6, 0xADE6, 0xADDC, 0xADE6, 0xADDC, 0xADE6, 0xADDC, 0xADE6, 0xADDC, 0xADE6, 0xADE6, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6,
	0xADE6, 0xADE6, 0xADDC, 0xADE6, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0x0000, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6,
	0xADE6, 0xADE6, 0xADE6, 0xADE6, 0x0000, 0xADE6, 0xADE6, 0xADE6, 0x0000, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADDC, 0xADDC, 0xADDC, 0xFFFF, 0xFFFF, 0x0000, 0xFFFF,
	// 0480
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6,
	0xADE6, 0xADE6, 0x0000, 0xADDC, 0xADE6, 0xADE6, 0xADDC, 0xADE6, 0xADE6, 0xADDC, 0xADE6, 0xADE6, 0xADE6, 0xADDC, 0xADDC, 0xADDC,
	0xAD1B, 0xAD1C, 0xAD1D, 0xADE6, 0xADE6, 0xADE6, 0xADDC, 0xADE6, 0xADE6, 0xADDC, 0xADDC, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB13A, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0xB13B, 0x0000, 0x0000, 0xB13C, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD07, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0x0000, 0x0000,
	0x0000, 0xADE6, 0xADDC, 0xADE6, 0xADE6, 0x0000, 0x0000, 0x0000, 0xB13D, 0xB13E, 0xB13F, 0xB140, 0xB141, 0xB142, 0xB143, 0xB144,
	0x0000, 0xFFFF, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xAD07, 0x0000, 0x0000, 0x0000,
	// 0500
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xB145, 0xB146, 0xAD09, 0x0000, 0xFFFF,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB147, 0xB148, 0xFFFF, 0xB149,
	0x0000, 0xFFFF, 0x0000, 0xB14A, 0xFFFF, 0x0000, 0xB14B, 0xFFFF, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xAD07, 0xFFFF, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0xAD09, 0xFFFF, 0xFFFF,
	0xFFFF, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB14C, 0xB14D, 0xB14E, 0x0000, 0xFFFF, 0xB14F, 0xFFFF,
	0x0000, 0xFFFF, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xAD07, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000, 0xAD09, 0xFFFF, 0xFFFF,
	0x0000, 0xFFFF, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xAD07, 0x0000, 0x0000, 0x0000,
	// 0580
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0x0000, 0xB150, 0xFFFF, 0xFFFF, 0xB151, 0xB152, 0xAD09, 0xFFFF, 0xFFFF,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB153, 0xB154, 0xFFFF, 0x0000,
	0x0000, 0xFFFF, 0x0000, 0x0000, 0xB155, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0xFFFF, 0x0000, 0xFFFF, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xB156, 0xB157, 0xB158, 0xAD09, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000, 0xB159, 0xFFFF, 0x0000, 0x0000, 0x0000, 0xAD09, 0xFFFF, 0xFFFF,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAD54, 0xAD5B, 0xFFFF, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xAD07, 0x0000, 0x0000, 0x0000,
	0xB15A, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0xB15B, 0xB15C, 0xFFFF, 0xB15D, 0xC018, 0x0000, 0xAD09, 0xFFFF, 0xFFFF,
	// 0600
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0xAD09, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xB15E, 0xB15F, 0xB160, 0xAD09, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xAD09, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0xFFFF, 0x0000, 0x0000, 0xB161, 0x0000, 0xB162, 0xC019, 0xB163, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD67, 0xAD67, 0xAD09, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD6B, 0xAD6B, 0xAD6B, 0xAD6B, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD76, 0xAD76, 0xFFFF, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0xFFFF, 0xAD7A, 0xAD7A, 0xAD7A, 0xAD7A, 0x0000, 0x0000, 0xFFFF, 0xFFFF,
	// 0680
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADDC, 0xADDC, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADDC, 0x0000, 0xADDC, 0x0000, 0xADD8, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0xB164, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0xB165, 0x0000, 0x0000,
	0x0000, 0x0000, 0xB166, 0x0000, 0x0000, 0x0000, 0x0000, 0xB167, 0x0000, 0x0000, 0x0000, 0x0000, 0xB168, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB169, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF,
	0xFFFF, 0xAD81, 0xAD82, 0xD009, 0xAD84, 0xD00C, 0xB16A, 0x0000, 0xB16B, 0x0000, 0xAD82, 0xAD82, 0xAD82, 0xAD82, 0x0000, 0x0000,
	0xAD82, 0xD00F, 0xADE6, 0xADE6, 0xAD09, 0x0000, 0xADE6, 0xADE6, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0xB16C, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0xB16D, 0x0000, 0x0000,
	// 0700
	0x0000, 0x0000, 0xB16E, 0x0000, 0x0000, 0x0000, 0x0000, 0xB16F, 0x0000, 0x0000, 0x0000, 0x0000, 0xB170, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB171, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADDC, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB172, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD07, 0x0000, 0xAD09, 0xAD09, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADDC, 0x0000, 0x0000,
	0xE83E, 0xE83F, 0xE840, 0xE841, 0xE842, 0xE843, 0xE844, 0xE845, 0xE846, 0xE847, 0xE848, 0xE849, 0xE84A, 0xE84B, 0xE84C, 0xE84D,
	0xE84E, 0xE84F, 0xE850, 0xE851, 0xE852, 0xE853, 0xE854, 0xE855, 0xE856, 0xE857, 0xE858, 0xE859, 0xE85A, 0xE85B, 0xE85C, 0xE85D,
	// 0780
	0xE85E, 0xE85F, 0xE860, 0xE861, 0xE862, 0xE863, 0xFFFF, 0xE864, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xE865, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xADE6, 0xADE6, 0xADE6,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xE866, 0xE867, 0xE868, 0xE869, 0xE86A, 0xE86B, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0xAD09, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADE6, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADE4, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADDE, 0xADE6, 0xADDC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	// 0800
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADE6, 0xADDC, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0x0000, 0x0000,
	0xAD09, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xFFFF, 0xFFFF, 0xADDC,
	0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADE6, 0xADE6, 0xADDC, 0x0000, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB173, 0x0000, 0xB174, 0x0000, 0xB175, 0x0000, 0xB176, 0x0000, 0xB177, 0x0000,
	0x0000, 0x0000, 0xB178, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0xAD07, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB179, 0x0000, 0xB17A, 0x0000, 0x0000,
	0xB17B, 0xB17C, 0x0000, 0xB17D, 0xAD09, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	// 0880
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADE6, 0xADDC, 0xADE6, 0xADE6, 0xADE6,
	0xADE6, 0xADE6, 0xADE6, 0xADE6, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0xAD09, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD07, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0xAD09, 0xAD09, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD07, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xE86C, 0xE86D, 0xE86E, 0xE86F, 0xE870, 0xE871, 0xE872, 0xE873, 0xE874, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xADE6, 0xADE6, 0xADE6, 0x0000, 0xAD01, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADE6, 0xADE6, 0xADDC, 0xADDC, 0xADDC, 0xADDC,
	// 0900
	0xADE6, 0x0000, 0xAD01, 0xAD01, 0xAD01, 0xAD01, 0xAD01, 0xAD01, 0xAD01, 0x0000, 0x0000, 0x0000, 0x0000, 0xADDC, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0xADE6, 0x0000, 0x0000, 0x0000, 0xADE6, 0xADE6, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xADE6, 0xADE6, 0xADDC, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADDC, 0xADE6, 0xADE6, 0xADEA, 0xADD6, 0xADDC,
	0xADCA, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6,
	0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE8, 0xADE4, 0xADE4, 0xADDC, 0xFFFF, 0xADE6, 0xADE9, 0xADDC, 0xADE6, 0xADDC,
	0xB17E, 0xB17F, 0xB180, 0xB181, 0xB182, 0xB183, 0xB184, 0xB185, 0xC01A, 0xC01B, 0xB186, 0xB187, 0xB188, 0xB189, 0xB18A, 0xB18B,
	0xB18C, 0xB18D, 0xB18E, 0xB18F, 0xC01C, 0xC01D, 0xC01E, 0xC01F, 0xB190, 0xB191, 0xB192, 0xB193, 0xC020, 0xC021, 0xB194, 0xB195,
	0xB196, 0xB197, 0xB198, 0xB199, 0xB19A, 0xB19B, 0xB19C, 0xB19D, 0xB19E, 0xB19F, 0xB1A0, 0xB1A1, 0xB1A2, 0xB1A3, 0xC022, 0xC023,
	// 0980
	0xB1A4, 0xB1A5, 0xB1A6, 0xB1A7, 0xB1A8, 0xB1A9, 0xB1AA, 0xB1AB, 0xC024, 0xC025, 0xB1AC, 0xB1AD, 0xB1AE, 0xB1AF, 0xB1B0, 0xB1B1,
	0xB1B2, 0xB1B3, 0xB1B4, 0xB1B5, 0xB1B6, 0xB1B7, 0xB1B8, 0xB1B9, 0xB1BA, 0xB1BB, 0xB1BC, 0xB1BD, 0xC026, 0xC027, 0xC028, 0xC029,
	0xC02A, 0xC02B, 0xC02C, 0xC02D, 0xB1BE, 0xB1BF, 0xB1C0, 0xB1C1, 0xB1C2, 0xB1C3, 0xB1C4, 0xB1C5, 0xC02E, 0xC02F, 0xB1C6, 0xB1C7,
	0xB1C8, 0xB1C9, 0xB1CA, 0xB1CB, 0xC030, 0xC031, 0xC032, 0xC033, 0xC034, 0xC035, 0xB1CC, 0xB1CD, 0xB1CE, 0xB1CF, 0xB1D0, 0xB1D1,
	0xB1D2, 0xB1D3, 0xB1D4, 0xB1D5, 0xB1D6, 0xB1D7, 0xB1D8, 0xB1D9, 0xC036, 0xC037, 0xC038, 0xC039, 0xB1DA, 0xB1DB, 0xB1DC, 0xB1DD,
	0xB1DE, 0xB1DF, 0xB1E0, 0xB1E1, 0xB1E2, 0xB1E3, 0xB1E4, 0xB1E5, 0xB1E6, 0xB1E7, 0xB1E8, 0xB1E9, 0xB1EA, 0xB1EB, 0xB1EC, 0xB1ED,
	0xB1EE, 0xB1EF, 0xB1F0, 0xB1F1, 0xB1F2, 0xB1F3, 0xB1F4, 0xB1F5, 0xB1F6, 0xB1F7, 0xB9F8, 0xB1F9, 0x0000, 0x0000, 0xB9FA, 0x0000,
	0xB1FB, 0xB1FC, 0xB1FD, 0xB1FE, 0xC03A, 0xC03B, 0xC03C, 0xC03D, 0xC03E, 0xC03F, 0xC040, 0xC041, 0xC042, 0xC043, 0xC044, 0xC045,
	// 0A00
	0xC046, 0xC047, 0xC048, 0xC049, 0xC04A, 0xC04B, 0xC04C, 0xC04D, 0xB1FF, 0xB200, 0xB201, 0xB202, 0xB203, 0xB204, 0xC04E, 0xC04F,
	0xC050, 0xC051, 0xC052, 0xC053, 0xC054, 0xC055, 0xC056, 0xC057, 0xB205, 0xB206, 0xB207, 0xB208, 0xB209, 0xB20A, 0xB20B, 0xB20C,
	0xC058, 0xC059, 0xC05A, 0xC05B, 0xC05C, 0xC05D, 0xC05E, 0xC05F, 0xC060, 0xC061, 0xC062, 0xC063, 0xC064, 0xC065, 0xC066, 0xC067,
	0xC068, 0xC069, 0xC06A, 0xC06B, 0xB20D, 0xB20E, 0xB20F, 0xB210, 0xC06C, 0xC06D, 0xC06E, 0xC06F, 0xC070, 0xC071, 0xC072, 0xC073,
	0xC074, 0xC075, 0xB211, 0xB212, 0xB213, 0xB214, 0xB215, 0xB216, 0xB217, 0xB218, 0xE875, 0x0000, 0xE876, 0x0000, 0xE877, 0x0000,
	0xB219, 0xB21A, 0xC076, 0xC077, 0xC078, 0xC079, 0xC07A, 0xC07B, 0xB21B, 0xB21C, 0xC07C, 0xC07D, 0xC07E, 0xC07F, 0xC080, 0xC081,
	0xB21D, 0xB21E, 0xC082, 0xC083, 0xC084, 0xC085, 0xFFFF, 0xFFFF, 0xB21F, 0xB220, 0xC086, 0xC087, 0xC088, 0xC089, 0xFFFF, 0xFFFF,
	0xB221, 0xB222, 0xC08A, 0xC08B, 0xC08C, 0xC08D, 0xC08E, 0xC08F, 0xB223, 0xB224, 0xC090, 0xC091, 0xC092, 0xC093, 0xC094, 0xC095,
	// 0A80
	0xB225, 0xB226, 0xC096, 0xC097, 0xC098, 0xC099, 0xC09A, 0xC09B, 0xB227, 0xB228, 0xC09C, 0xC09D, 0xC09E, 0xC09F, 0xC0A0, 0xC0A1,
	0xB229, 0xB22A, 0xC0A2, 0xC0A3, 0xC0A4, 0xC0A5, 0xFFFF, 0xFFFF, 0xB22B, 0xB22C, 0xC0A6, 0xC0A7, 0xC0A8, 0xC0A9, 0xFFFF, 0xFFFF,
	0xB22D, 0xB22E, 0xC0AA, 0xC0AB, 0xC0AC, 0xC0AD, 0xC0AE, 0xC0AF, 0xFFFF, 0xB22F, 0xFFFF, 0xC0B0, 0xFFFF, 0xC0B1, 0xFFFF, 0xC0B2,
	0xB230, 0xB231, 0xC0B3, 0xC0B4, 0xC0B5, 0xC0B6, 0xC0B7, 0xC0B8, 0xB232, 0xB233, 0xC0B9, 0xC0BA, 0xC0BB, 0xC0BC, 0xC0BD, 0xC0BE,
	0xB234, 0xB235, 0xB236, 0xB237, 0xB238, 0xB239, 0xB23A, 0xB23B, 0xB23C, 0xB23D, 0xB23E, 0xB23F, 0xB240, 0xB241, 0xFFFF, 0xFFFF,
	0xC0BF, 0xC0C0, 0xD012, 0xD017, 0xD01C, 0xD021, 0xD026, 0xD02B, 0xC0C1, 0xC0C2, 0xD030, 0xD035, 0xD03A, 0xD03F, 0xD044, 0xD049,
	0xC0C3, 0xC0C4, 0xD04E, 0xD053, 0xD058, 0xD05D, 0xD062, 0xD067, 0xC0C5, 0xC0C6, 0xD06C, 0xD071, 0xD076, 0xD07B, 0xD080, 0xD085,
	0xC0C7, 0xC0C8, 0xD08A, 0xD08F, 0xD094, 0xD099, 0xD09E, 0xD0A3, 0xC0C9, 0xC0CA, 0xD0A8, 0xD0AD, 0xD0B2, 0xD0B7, 0xD0BC, 0xD0C1,
	// 0B00
	0xB242, 0xB243, 0xC0CB, 0xB244, 0xC0CC, 0xFFFF, 0xB245, 0xC0CD, 0xB246, 0xB247, 0xB248, 0xB249, 0xB24A, 0x0000, 0x03B9, 0x0000,
	0x0000, 0xB24B, 0xC0CE, 0xB24C, 0xC0CF, 0xFFFF, 0xB24D, 0xC0D0, 0xB24E, 0xB24F, 0xB250, 0xB251, 0xB252, 0xB253, 0xB254, 0xB255,
	0xB256, 0xB257, 0xC0D1, 0xC0D2, 0xFFFF, 0xFFFF, 0xB258, 0xC0D3, 0xB259, 0xB25A, 0xB25B, 0xB25C, 0xFFFF, 0xB25D, 0xB25E, 0xB25F,
	0xB260, 0xB261, 0xC0D4, 0xC0D5, 0xB262, 0xB263, 0xB264, 0xC0D6, 0xB265, 0xB266, 0xB267, 0xB268, 0xB269, 0xB26A, 0xB26B, 0x0060,
	0xFFFF, 0xFFFF, 0xC0D7, 0xB26C, 0xC0D8, 0xFFFF, 0xB26D, 0xC0D9, 0xB26E, 0xB26F, 0xB270, 0xB271, 0xB272, 0x00B4, 0x0000, 0xFFFF,
	0x2002, 0x2003, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xADE6, 0xADE6, 0xAD01, 0xAD01, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xAD01, 0xAD01, 0xAD01, 0xADE6, 0xADE6, 0x0000, 0x0000, 0x0000,
	0x0000, 0xADE6, 0x0000, 0x0000, 0x0000, 0xAD01, 0xAD01, 0xADE6, 0xADDC, 0xADE6, 0xAD01, 0xAD01, 0xADDC, 0xADDC, 0xADDC, 0xADDC,
	// 0B80
	0xADE6, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x03A9, 0x0000, 0x0000, 0x0000, 0x004B, 0xB273, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0xE878, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xE879, 0xE87A, 0xE87B, 0xE87C, 0xE87D, 0xE87E, 0xE87F, 0xE880, 0xE881, 0xE882, 0xE883, 0xE884, 0xE885, 0xE886, 0xE887, 0xE888,
	0x0000, 0x0000, 0x0000, 0xE889, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB274, 0xB275, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB276, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB277, 0xB278, 0xB279,
	// 0C00
	0x0000, 0x0000, 0x0000, 0x0000, 0xB27A, 0x0000, 0x0000, 0x0000, 0x0000, 0xB27B, 0x0000, 0x0000, 0xB27C, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0xB27D, 0x0000, 0xB27E, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0xB27F, 0x0000, 0x0000, 0xB280, 0x0000, 0x0000, 0xB281, 0x0000, 0xB282, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xB283, 0x0000, 0xB284, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB285, 0xB286, 0xB287,
	0xB288, 0xB289, 0x0000, 0x0000, 0xB28A, 0xB28B, 0x0000, 0x0000, 0xB28C, 0xB28D, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xB28E, 0xB28F, 0x0000, 0x0000, 0xB290, 0xB291, 0x0000, 0x0000, 0xB292, 0xB293, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB294, 0xB295, 0xB296, 0xB297,
	0xB298, 0xB299, 0xB29A, 0xB29B, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB29C, 0xB29D, 0xB29E, 0xB29F, 0x0000, 0x0000,
	// 0C80
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x3008, 0x3009, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xE88A, 0xE88B, 0xE88C, 0xE88D, 0xE88E, 0xE88F, 0xE890, 0xE891, 0xE892, 0xE893,
	0xE894, 0xE895, 0xE896, 0xE897, 0xE898, 0xE899, 0xE89A, 0xE89B, 0xE89C, 0xE89D, 0xE89E, 0xE89F, 0xE8A0, 0xE8A1, 0xE8A2, 0xE8A3,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB2A0, 0x0000, 0x0000, 0x0000,
	0xE8A4, 0xE8A5, 0xE8A6, 0xE8A7, 0xE8A8, 0xE8A9, 0xE8AA, 0xE8AB, 0xE8AC, 0xE8AD, 0xE8AE, 0xE8AF, 0xE8B0, 0xE8B1, 0xE8B2, 0xE8B3,
	0xE8B4, 0xE8B5, 0xE8B6, 0xE8B7, 0xE8B8, 0xE8B9, 0xE8BA, 0xE8BB, 0xE8BC, 0xE8BD, 0xE8BE, 0xE8BF, 0xE8C0, 0xE8C1, 0xE8C2, 0xE8C3,
	0xE8C4, 0xE8C5, 0xE8C6, 0xE8C7, 0xE8C8, 0xE8C9, 0xE8CA, 0xE8CB, 0xE8CC, 0xE8CD, 0xE8CE, 0xE8CF, 0xE8D0, 0xE8D1, 0xE8D2, 0xFFFF,
	0xE8D3, 0x0000, 0xE8D4, 0xE8D5, 0xE8D6, 0x0000, 0x0000, 0xE8D7, 0x0000, 0xE8D8, 0x0000, 0xE8D9, 0x0000, 0xE8DA, 0xE8DB, 0xE8DC,
	// 0D00
	0xE8DD, 0x0000, 0xE8DE, 0x0000, 0x0000, 0xE8DF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xE8E0, 0xE8E1,
	0xE8E2, 0x0000, 0xE8E3, 0x0000, 0xE8E4, 0x0000, 0xE8E5, 0x0000, 0xE8E6, 0x0000, 0xE8E7, 0x0000, 0xE8E8, 0x0000, 0xE8E9, 0x0000,
	0xE8EA, 0x0000, 0xE8EB, 0x0000, 0xE8EC, 0x0000, 0xE8ED, 0x0000, 0xE8EE, 0x0000, 0xE8EF, 0x0000, 0xE8F0, 0x0000, 0xE8F1, 0x0000,
	0xE8F2, 0x0000, 0xE8F3, 0x0000, 0xE8F4, 0x0000, 0xE8F5, 0x0000, 0xE8F6, 0x0000, 0xE8F7, 0x0000, 0xE8F8, 0x0000, 0xE8F9, 0x0000,
	0xE8FA, 0x0000, 0xE8FB, 0x0000, 0xE8FC, 0x0000, 0xE8FD, 0x0000, 0xE8FE, 0x0000, 0xE8FF, 0x0000, 0xE900, 0x0000, 0xE901, 0x0000,
	0xE902, 0x0000, 0xE903, 0x0000, 0xE904, 0x0000, 0xE905, 0x0000, 0xE906, 0x0000, 0xE907, 0x0000, 0xE908, 0x0000, 0xE909, 0x0000,
	0xE90A, 0x0000, 0xE90B, 0x0000, 0xE90C, 0x0000, 0xE90D, 0x0000, 0xE90E, 0x0000, 0xE90F, 0x0000, 0xE910, 0x0000, 0xE911, 0x0000,
	0xE912, 0x0000, 0xE913, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xE914, 0x0000, 0xE915, 0x0000, 0xADE6,
	// 0D80
	0xADE6, 0xADE6, 0xE916, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAD09,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADDA, 0xADE4, 0xADE8, 0xADDE, 0xADE0, 0xADE0,
	0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB2A1, 0x0000, 0xB2A2, 0x0000,
	0xB2A3, 0x0000, 0xB2A4, 0x0000, 0xB2A5, 0x0000, 0xB2A6, 0x0000, 0xB2A7, 0x0000, 0xB2A8, 0x0000, 0xB2A9, 0x0000, 0xB2AA, 0x0000,
	0xB2AB, 0x0000, 0xB2AC, 0x0000, 0x0000, 0xB2AD, 0x0000, 0xB2AE, 0x0000, 0xB2AF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xB2B0, 0xB2B1, 0x0000, 0xB2B2, 0xB2B3, 0x0000, 0xB2B4, 0xB2B5, 0x0000, 0xB2B6, 0xB2B7, 0x0000, 0xB2B8, 0xB2B9, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0xB2BA, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xAD08, 0xAD08, 0x0000, 0x0000, 0x0000, 0xB2BB, 0x0000,
	// 0E00
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB2BC, 0x0000, 0xB2BD, 0x0000,
	0xB2BE, 0x0000, 0xB2BF, 0x0000, 0xB2C0, 0x0000, 0xB2C1, 0x0000, 0xB2C2, 0x0000, 0xB2C3, 0x0000, 0xB2C4, 0x0000, 0xB2C5, 0x0000,
	0xB2C6, 0x0000, 0xB2C7, 0x0000, 0x0000, 0xB2C8, 0x0000, 0xB2C9, 0x0000, 0xB2CA, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xB2CB, 0xB2CC, 0x0000, 0xB2CD, 0xB2CE, 0x0000, 0xB2CF, 0xB2D0, 0x0000, 0xB2D1, 0xB2D2, 0x0000, 0xB2D3, 0xB2D4, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0xB2D5, 0x0000, 0x0000, 0xB2D6, 0xB2D7, 0xB2D8, 0xB2D9, 0x0000, 0x0000, 0x0000, 0xB2DA, 0x0000,
	0xE917, 0x0000, 0xE918, 0x0000, 0xE919, 0x0000, 0xE91A, 0x0000, 0xE91B, 0x0000, 0xE91C, 0x0000, 0xE91D, 0x0000, 0xE91E, 0x0000,
	0xE91F, 0x0000, 0xE920, 0x0000, 0xE921, 0x0000, 0xE922, 0x0000, 0xE923, 0x0000, 0xE924, 0x0000, 0xE925, 0x0000, 0xE926, 0x0000,
	0xE927, 0x0000, 0xE928, 0x0000, 0xE929, 0x0000, 0xE92A, 0x0000, 0xE92B, 0x0000, 0xE92C, 0x0000, 0xE92D, 0x0000, 0x0000, 0xADE6,
	// 0E80
	0x0000, 0x0000, 0x0000, 0x0000, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0x0000, 0x0000,
	0xE92E, 0x0000, 0xE92F, 0x0000, 0xE930, 0x0000, 0xE931, 0x0000, 0xE932, 0x0000, 0xE933, 0x0000, 0xE934, 0x0000, 0xE935, 0x0000,
	0xE936, 0x0000, 0xE937, 0x0000, 0xE938, 0x0000, 0xE939, 0x0000, 0xE93A, 0x0000, 0xE93B, 0x0000, 0x0000, 0x0000, 0xADE6, 0xADE6,
	0xADE6, 0xADE6, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0xE93C, 0x0000, 0xE93D, 0x0000, 0xE93E, 0x0000, 0xE93F, 0x0000, 0xE940, 0x0000, 0xE941, 0x0000, 0xE942, 0x0000,
	0x0000, 0x0000, 0xE943, 0x0000, 0xE944, 0x0000, 0xE945, 0x0000, 0xE946, 0x0000, 0xE947, 0x0000, 0xE948, 0x0000, 0xE949, 0x0000,
	0xE94A, 0x0000, 0xE94B, 0x0000, 0xE94C, 0x0000, 0xE94D, 0x0000, 0xE94E, 0x0000, 0xE94F, 0x0000, 0xE950, 0x0000, 0xE951, 0x0000,
	0xE952, 0x0000, 0xE953, 0x0000, 0xE954, 0x0000, 0xE955, 0x0000, 0xE956, 0x0000, 0xE957, 0x0000, 0xE958, 0x0000, 0xE959, 0x0000,
	// 0F00
	0xE95A, 0x0000, 0xE95B, 0x0000, 0xE95C, 0x0000, 0xE95D, 0x0000, 0xE95E, 0x0000, 0xE95F, 0x0000, 0xE960, 0x0000, 0xE961, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xE962, 0x0000, 0xE963, 0x0000, 0xE964, 0xE965, 0x0000,
	0xE966, 0x0000, 0xE967, 0x0000, 0xE968, 0x0000, 0xE969, 0x0000, 0x0000, 0x0000, 0x0000, 0xE96A, 0x0000, 0xE96B, 0x0000, 0x0000,
	0xE96C, 0x0000, 0xE96D, 0x0000, 0x0000, 0x0000, 0xE96E, 0x0000, 0xE96F, 0x0000, 0xE970, 0x0000, 0xE971, 0x0000, 0xE972, 0x0000,
	0xE973, 0x0000, 0xE974, 0x0000, 0xE975, 0x0000, 0xE976, 0x0000, 0xE977, 0x0000, 0xE978, 0xE979, 0xE97A, 0xE97B, 0xE97C, 0xFFFF,
	0xE97D, 0xE97E, 0xE97F, 0xE980, 0xE981, 0x0000, 0xE982, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000,
	// 0F80
	0xADE6, 0xADE6, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADDC, 0xADDC, 0xADDC, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0xAD09, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000,
	0x0000, 0x0000, 0x0000, 0xAD07, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xAD09, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000,
	0xADE6, 0x0000, 0xADE6, 0xADE6, 0xADDC, 0x0000, 0x0000, 0xADE6, 0xADE6, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADE6, 0xADE6,
	0x0000, 0xADE6, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	// 1000
	0xE983, 0xE984, 0xE985, 0xE986, 0xE987, 0xE988, 0xE989, 0xE98A, 0xE98B, 0xE98C, 0xE98D, 0xE98E, 0xE98F, 0xE990, 0xE991, 0xE992,
	0xE993, 0xE994, 0xE995, 0xE996, 0xE997, 0xE998, 0xE999, 0xE99A, 0xE99B, 0xE99C, 0xE99D, 0xE99E, 0xE99F, 0xE9A0, 0xE9A1, 0xE9A2,
	0xE9A3, 0xE9A4, 0xE9A5, 0xE9A6, 0xE9A7, 0xE9A8, 0xE9A9, 0xE9AA, 0xE9AB, 0xE9AC, 0xE9AD, 0xE9AE, 0xE9AF, 0xE9B0, 0xE9B1, 0xE9B2,
	0xE9B3, 0xE9B4, 0xE9B5, 0xE9B6, 0xE9B7, 0xE9B8, 0xE9B9, 0xE9BA, 0xE9BB, 0xE9BC, 0xE9BD, 0xE9BE, 0xE9BF, 0xE9C0, 0xE9C1, 0xE9C2,
	0xE9C3, 0xE9C4, 0xE9C5, 0xE9C6, 0xE9C7, 0xE9C8, 0xE9C9, 0xE9CA, 0xE9CB, 0xE9CC, 0xE9CD, 0xE9CE, 0xE9CF, 0xE9D0, 0xE9D1, 0xE9D2,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0xFFFF, 0xFFFF,
	0xAC00, 0xAC00, 0xAC00, 0xAC00, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x8C48, 0x66F4, 0x8ECA, 0x8CC8, 0x6ED1, 0x4E32, 0x53E5, 0x9F9C, 0x9F9C, 0x5951, 0x91D1, 0x5587, 0x5948, 0x61F6, 0x7669, 0x7F85,
	// 1080
	0x863F, 0x87BA, 0x88F8, 0x908F, 0x6A02, 0x6D1B, 0x70D9, 0x73DE, 0x843D, 0x916A, 0x99F1, 0x4E82, 0x5375, 0x6B04, 0x721B, 0x862D,
	0x9E1E, 0x5D50, 0x6FEB, 0x85CD, 0x8964, 0x62C9, 0x81D8, 0x881F, 0x5ECA, 0x6717, 0x6D6A, 0x72FC, 0x90CE, 0x4F86, 0x51B7, 0x52DE,
	0x64C4, 0x6AD3, 0x7210, 0x76E7, 0x8001, 0x8606, 0x865C, 0x8DEF, 0x9732, 0x9B6F, 0x9DFA, 0x788C, 0x797F, 0x7DA0, 0x83C9, 0x9304,
	0x9E7F, 0x8AD6, 0x58DF, 0x5F04, 0x7C60, 0x807E, 0x7262, 0x78CA, 0x8CC2, 0x96F7, 0x58D8, 0x5C62, 0x6A13, 0x6DDA, 0x6F0F, 0x7D2F,
	0x7E37, 0x964B, 0x52D2, 0x808B, 0x51DC, 0x51CC, 0x7A1C, 0x7DBE, 0x83F1, 0x9675, 0x8B80, 0x62CF, 0x6A02, 0x8AFE, 0x4E39, 0x5BE7,
	0x6012, 0x7387, 0x7570, 0x5317, 0x78FB, 0x4FBF, 0x5FA9, 0x4E0D, 0x6CCC, 0x6578, 0x7D22, 0x53C3, 0x585E, 0x7701, 0x8449, 0x8AAA,
	0x6BBA, 0x8FB0, 0x6C88, 0x62FE, 0x82E5, 0x63A0, 0x7565, 0x4EAE, 0x5169, 0x51C9, 0x6881, 0x7CE7, 0x826F, 0x8AD2, 0x91CF, 0x52F5,
	0x5442, 0x5973, 0x5EEC, 0x65C5, 0x6FFE, 0x792A, 0x95AD, 0x9A6A, 0x9E97, 0x9ECE, 0x529B, 0x66C6, 0x6B77, 0x8F62, 0x5E74, 0x6190,
	// 1100
	0x6200, 0x649A, 0x6F23, 0x7149, 0x7489, 0x79CA, 0x7DF4, 0x806F, 0x8F26, 0x84EE, 0x9023, 0x934A, 0x5217, 0x52A3, 0x54BD, 0x70C8,
	0x88C2, 0x8AAA, 0x5EC9, 0x5FF5, 0x637B, 0x6BAE, 0x7C3E, 0x7375, 0x4EE4, 0x56F9, 0x5BE7, 0x5DBA, 0x601C, 0x73B2, 0x7469, 0x7F9A,
	0x8046, 0x9234, 0x96F6, 0x9748, 0x9818, 0x4F8B, 0x79AE, 0x91B4, 0x96B8, 0x60E1, 0x4E86, 0x50DA, 0x5BEE, 0x5C3F, 0x6599, 0x6A02,
	0x71CE, 0x7642, 0x84FC, 0x907C, 0x9F8D, 0x6688, 0x962E, 0x5289, 0x677B, 0x67F3, 0x6D41, 0x6E9C, 0x7409, 0x7559, 0x786B, 0x7D10,
	0x985E, 0x516D, 0x622E, 0x9678, 0x502B, 0x5D19, 0x6DEA, 0x8F2A, 0x5F8B, 0x6144, 0x6817, 0x7387, 0x9686, 0x5229, 0x540F, 0x5C65,
	0x6613, 0x674E, 0x68A8, 0x6CE5, 0x7406, 0x75E2, 0x7F79, 0x88CF, 0x88E1, 0x91CC, 0x96E2, 0x533F, 0x6EBA, 0x541D, 0x71D0, 0x7498,
	0x85FA, 0x96A3, 0x9C57, 0x9E9F, 0x6797, 0x6DCB, 0x81E8, 0x7ACB, 0x7B20, 0x7C92, 0x72C0, 0x7099, 0x8B58, 0x4EC0, 0x8336, 0x523A,
	0x5207, 0x5EA6, 0x62D3, 0x7CD6, 0x5B85, 0x6D1E, 0x66B4, 0x8F3B, 0x884C, 0x964D, 0x898B, 0x5ED3, 0x5140, 0x55C0, 0x0000, 0x0000,
	// 1180
	0x585A, 0x0000, 0x6674, 0x0000, 0x0000, 0x51DE, 0x732A, 0x76CA, 0x793C, 0x795E, 0x7965, 0x798F, 0x9756, 0x7CBE, 0x7FBD, 0x0000,
	0x8612, 0x0000, 0x8AF8, 0x0000, 0x0000, 0x9038, 0x90FD, 0x0000, 0x0000, 0x0000, 0x98EF, 0x98FC, 0x9928, 0x9DB4, 0x90DE, 0x96B7,
	0x4FAE, 0x50E7, 0x514D, 0x52C9, 0x52E4, 0x5351, 0x559D, 0x5606, 0x5668, 0x5840, 0x58A8, 0x5C64, 0x5C6E, 0x6094, 0x6168, 0x618E,
	0x61F2, 0x654F, 0x65E2, 0x6691, 0x6885, 0x6D77, 0x6E1A, 0x6F22, 0x716E, 0x722B, 0x7422, 0x7891, 0x793E, 0x7949, 0x7948, 0x7950,
	0x7956, 0x795D, 0x798D, 0x798E, 0x7A40, 0x7A81, 0x7BC0, 0x7DF4, 0x7E09, 0x7E41, 0x7F72, 0x8005, 0x81ED, 0x8279, 0x8279, 0x8457,
	0x8910, 0x8996, 0x8B01, 0x8B39, 0x8CD3, 0x8D08, 0x8FB6, 0x9038, 0x96E3, 0x97FF, 0x983B, 0x6075, 0xE1D3, 0x8218, 0xFFFF, 0xFFFF,
	0x4E26, 0x51B5, 0x5168, 0x4F80, 0x5145, 0x5180, 0x52C7, 0x52FA, 0x559D, 0x5555, 0x5599, 0x55E2, 0x585A, 0x58B3, 0x5944, 0x5954,
	0x5A62, 0x5B28, 0x5ED2, 0x5ED9, 0x5F69, 0x5FAD, 0x60D8, 0x614E, 0x6108, 0x618E, 0x6160, 0x61F2, 0x6234, 0x63C4, 0x641C, 0x6452,
	// 1200
	0x6556, 0x6674, 0x6717, 0x671B, 0x6756, 0x6B79, 0x6BBA, 0x6D41, 0x6EDB, 0x6ECB, 0x6F22, 0x701E, 0x716E, 0x77A7, 0x7235, 0x72AF,
	0x732A, 0x7471, 0x7506, 0x753B, 0x761D, 0x761F, 0x76CA, 0x76DB, 0x76F4, 0x774A, 0x7740, 0x78CC, 0x7AB1, 0x7BC0, 0x7C7B, 0x7D5B,
	0x7DF4, 0x7F3E, 0x8005, 0x8352, 0x83EF, 0x8779, 0x8941, 0x8986, 0x8996, 0x8ABF, 0x8AF8, 0x8ACB, 0x8B01, 0x8AFE, 0x8AED, 0x8B39,
	0x8B8A, 0x8D08, 0x8F38, 0x9072, 0x9199, 0x9276, 0x967C, 0x96E3, 0x9756, 0x97DB, 0x97FF, 0x980B, 0x983B, 0x9B12, 0x9F9C, 0xE1D4,
	0xE1D5, 0xE1D6, 0x3B9D, 0x4018, 0x4039, 0xE1D7, 0xE1D8, 0xE1D9, 0x9F43, 0x9F8E, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xBADB, 0xBADC, 0xBADD, 0xC8DA, 0xC8DB, 0xBADE, 0xBADF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xFFFF, 0xFFFF, 0xFFFF, 0xBAE0, 0xBAE1, 0xBAE2, 0xBAE3, 0xBAE4, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xB2E5, 0xAD1A, 0xB2E6,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xB2E7, 0xB2E8, 0xC0DC, 0xC0DD, 0xB2E9, 0xB2EA,
	// 1280
	0xB2EB, 0xB2EC, 0xB2ED, 0xB2EE, 0xB2EF, 0xB2F0, 0xB2F1, 0xFFFF, 0xB2F2, 0xB2F3, 0xB2F4, 0xB2F5, 0xB2F6, 0xFFFF, 0xB2F7, 0xFFFF,
	0xB2F8, 0xB2F9, 0xFFFF, 0xB2FA, 0xB2FB, 0xFFFF, 0xB2FC, 0xB2FD, 0xB2FE, 0xB2FF, 0xB300, 0xB301, 0xB302, 0xB303, 0xB304, 0x0000,
	0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADE6, 0xADE6,
	0x0000, 0xE9DA, 0xE9DB, 0xE9DC, 0xE9DD, 0xE9DE, 0xE9DF, 0xE9E0, 0xE9E1, 0xE9E2, 0xE9E3, 0xE9E4, 0xE9E5, 0xE9E6, 0xE9E7, 0xE9E8,
	0xE9E9, 0xE9EA, 0xE9EB, 0xE9EC, 0xE9ED, 0xE9EE, 0xE9EF, 0xE9F0, 0xE9F1, 0xE9F2, 0xE9F3, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADDC, 0xFFFF, 0xFFFF,
	0xADDC, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	// 1300
	0xE9F4, 0xE9F5, 0xE9F6, 0xE9F7, 0xE9F8, 0xE9F9, 0xE9FA, 0xE9FB, 0xE9FC, 0xE9FD, 0xE9FE, 0xE9FF, 0xEA00, 0xEA01, 0xEA02, 0xEA03,
	0xEA04, 0xEA05, 0xEA06, 0xEA07, 0xEA08, 0xEA09, 0xEA0A, 0xEA0B, 0xEA0C, 0xEA0D, 0xEA0E, 0xEA0F, 0xEA10, 0xEA11, 0xEA12, 0xEA13,
	0xEA14, 0xEA15, 0xEA16, 0xEA17, 0xEA18, 0xEA19, 0xEA1A, 0xEA1B, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xEA1C, 0xEA1D, 0xEA1E, 0xEA1F, 0xEA20, 0xEA21, 0xEA22, 0xEA23, 0xEA24, 0xEA25, 0xEA26, 0xEA27, 0xEA28, 0xEA29, 0xEA2A, 0xEA2B,
	0xEA2C, 0xEA2D, 0xEA2E, 0xEA2F, 0xEA30, 0xEA31, 0xEA32, 0xEA33, 0xEA34, 0xEA35, 0xEA36, 0xEA37, 0xEA38, 0xEA39, 0xEA3A, 0xEA3B,
	0xEA3C, 0xEA3D, 0xEA3E, 0xEA3F, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0xADDC, 0x0000, 0xADE6,
	0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xADE6, 0xAD01, 0xADDC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAD09,
	// 1380
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADE6, 0xADDC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xEA40, 0xEA41, 0xEA42, 0xEA43, 0xEA44, 0xEA45, 0xEA46, 0xEA47, 0xEA48, 0xEA49, 0xEA4A, 0xEA4B, 0xEA4C, 0xEA4D, 0xEA4E, 0xEA4F,
	0xEA50, 0xEA51, 0xEA52, 0xEA53, 0xEA54, 0xEA55, 0xEA56, 0xEA57, 0xEA58, 0xEA59, 0xEA5A, 0xEA5B, 0xEA5C, 0xEA5D, 0xEA5E, 0xEA5F,
	0xEA60, 0xEA61, 0xEA62, 0xEA63, 0xEA64, 0xEA65, 0xEA66, 0xEA67, 0xEA68, 0xEA69, 0xEA6A, 0xEA6B, 0xEA6C, 0xEA6D, 0xEA6E, 0xEA6F,
	0xEA70, 0xEA71, 0xEA72, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF,
	0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xAD09,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xF000, 0x0000, 0xF003, 0x0000, 0x0000, 0x0000,
	// 1400
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xF006, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0xAD07, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0xADE6, 0xADE6, 0xADE6, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xF009, 0xF00C,
	0x0000, 0x0000, 0x0000, 0xAD09, 0xAD09, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0xAD07, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xAD09, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD07, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0xAD07, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF,
	// 1480
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD07, 0xAD09, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0xFFFF, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xAD07, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xF00F, 0xF012, 0xAD09, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xFFFF, 0xFFFF, 0xFFFF,
	0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0xAD09, 0x0000, 0x0000, 0x0000, 0xAD07, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xF015, 0xF018, 0x0000, 0xF01B, 0x0000,
	0x0000, 0x0000, 0xAD09, 0xAD07, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	// 1500
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0xF01E, 0xF021, 0x0000, 0x0000, 0x0000, 0xAD09,
	0xAD07, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0xAD07, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xEA73, 0xEA74, 0xEA75, 0xEA76, 0xEA77, 0xEA78, 0xEA79, 0xEA7A, 0xEA7B, 0xEA7C, 0xEA7D, 0xEA7E, 0xEA7F, 0xEA80, 0xEA81, 0xEA82,
	0xEA83, 0xEA84, 0xEA85, 0xEA86, 0xEA87, 0xEA88, 0xEA89, 0xEA8A, 0xEA8B, 0xEA8C, 0xEA8D, 0xEA8E, 0xEA8F, 0xEA90, 0xEA91, 0xEA92,
	0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	// 1580
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xAD09,
	0x0000, 0x0000, 0xAD07, 0x0000, 0xAD09, 0xAD09, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xAD01, 0xAD01, 0xAD01, 0xAD01, 0xAD01, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0xAD01, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xF024, 0xF027,
	// 1600
	0xF02A, 0xF02E, 0xF032, 0xF036, 0xF03A, 0xADD8, 0xADD8, 0xAD01, 0xAD01, 0xAD01, 0x0000, 0x0000, 0x0000, 0xADE2, 0xADD8, 0xADD8,
	0xADD8, 0xADD8, 0xADD8, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC,
	0xADDC, 0xADDC, 0xADDC, 0x0000, 0x0000, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADDC, 0xADDC, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xF03E, 0xF041, 0xF044, 0xF048, 0xF04C,
	0xF050, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0xADE6, 0xADE6, 0xADE6, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xFFFF, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6,
	// 1680
	0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xFFFF, 0xFFFF, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6,
	0xADE6, 0xADE6, 0xFFFF, 0xADE6, 0xADE6, 0xFFFF, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xADDC, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0xEA93, 0xEA94, 0xEA95, 0xEA96, 0xEA97, 0xEA98, 0xEA99, 0xEA9A, 0xEA9B, 0xEA9C, 0xEA9D, 0xEA9E, 0xEA9F, 0xEAA0, 0xEAA1, 0xEAA2,
	0xEAA3, 0xEAA4, 0xEAA5, 0xEAA6, 0xEAA7, 0xEAA8, 0xEAA9, 0xEAAA, 0xEAAB, 0xEAAC, 0xEAAD, 0xEAAE, 0xEAAF, 0xEAB0, 0xEAB1, 0xEAB2,
	0xEAB3, 0xEAB4, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xADE6, 0xAD07, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
	0x4E3D, 0x4E38, 0x4E41, 0xE2B5, 0x4F60, 0x4FAE, 0x4FBB, 0x5002, 0x507A, 0x5099, 0x50E7, 0x50CF, 0x349E, 0xE2B6, 0x514D, 0x5154,
	// 1700
	0x5164, 0x5177, 0xE2B7, 0x34B9, 0x5167, 0x518D, 0xE2B8, 0x5197, 0x51A4, 0x4ECC, 0x51AC, 0x51B5, 0xE2B9, 0x51F5, 0x5203, 0x34DF,
	0x523B, 0x5246, 0x5272, 0x5277, 0x3515, 0x52C7, 0x52C9, 0x52E4, 0x52FA, 0x5305, 0x5306, 0x5317, 0x5349, 0x5351, 0x535A, 0x5373,
	0x537D, 0x537F, 0x537F, 0x537F, 0xE2BA, 0x7070, 0x53CA, 0x53DF, 0xE2BB, 0x53EB, 0x53F1, 0x5406, 0x549E, 0x5438, 0x5448, 0x5468,
	0x54A2, 0x54F6, 0x5510, 0x5553, 0x5563, 0x5584, 0x5584, 0x5599, 0x55AB, 0x55B3, 0x55C2, 0x5716, 0x5606, 0x5717, 0x5651, 0x5674,
	0x5207, 0x58EE, 0x57CE, 0x57F4, 0x580D, 0x578B, 0x5832, 0x5831, 0x58AC, 0xE2BC, 0x58F2, 0x58F7, 0x5906, 0x591A, 0x5922, 0x5962,
	0xE2BD, 0xE2BE, 0x59EC, 0x5A1B, 0x5A27, 0x59D8, 0x5A66, 0x36EE, 0x36FC, 0x5B08, 0x5B3E, 0x5B3E, 0xE2BF, 0x5BC3, 0x5BD8, 0x5BE7,
	0x5BF3, 0xE2C0, 0x5BFF, 0x5C06, 0x5F53, 0x5C22, 0x3781, 0x5C60, 0x5C6E, 0x5CC0, 0x5C8D, 0xE2C1, 0x5D43, 0xE2C2, 0x5D6E, 0x5D6B,
	0x5D7C, 0x5DE1, 0x5DE2, 0x382F, 0x5DFD, 0x5E28, 0x5E3D, 0x5E69, 0x3862, 0xE2C3, 0x387C, 0x5EB0, 0x5EB3, 0x5EB6, 0x5ECA, 0xE2C4,
	// 1780
	0x5EFE, 0xE2C5, 0xE2C6, 0x8201, 0x5F22, 0x5F22, 0x38C7, 0xE2C7, 0xE2C8, 0x5F62, 0x5F6B, 0x38E3, 0x5F9A, 0x5FCD, 0x5FD7, 0x5FF9,
	0x6081, 0x393A, 0x391C, 0x6094, 0xE2C9, 0x60C7, 0x6148, 0x614C, 0x614E, 0x614C, 0x617A, 0x618E, 0x61B2, 0x61A4, 0x61AF, 0x61DE,
	0x61F2, 0x61F6, 0x6210, 0x621B, 0x625D, 0x62B1, 0x62D4, 0x6350, 0xE2CA, 0x633D, 0x62FC, 0x6368, 0x6383, 0x63E4, 0xE2CB, 0x6422,
	0x63C5, 0x63A9, 0x3A2E, 0x6469, 0x647E, 0x649D, 0x6477, 0x3A6C, 0x654F, 0x656C, 0xE2CC, 0x65E3, 0x66F8, 0x6649, 0x3B19, 0x6691,
	0x3B08, 0x3AE4, 0x5192, 0x5195, 0x6700, 0x669C, 0x80AD, 0x43D9, 0x6717, 0x671B, 0x6721, 0x675E, 0x6753, 0xE2CD, 0x3B49, 0x67FA,
	0x6785, 0x6852, 0x6885, 0xE2CE, 0x688E, 0x681F, 0x6914, 0x3B9D, 0x6942, 0x69A3, 0x69EA, 0x6AA8, 0xE2CF, 0x6ADB, 0x3C18, 0x6B21,
	0xE2D0, 0x6B54, 0x3C4E, 0x6B72, 0x6B9F, 0x6BBA, 0x6BBB, 0xE2D1, 0xE2D2, 0xE2D3, 0x6C4E, 0xE2D4, 0x6CBF, 0x6CCD, 0x6C67, 0x6D16,
	0x6D3E, 0x6D77, 0x6D41, 0x6D69, 0x6D78, 0x6D85, 0xE2D5, 0x6D34, 0x6E2F, 0x6E6E, 0x3D33, 0x6ECB, 0x6EC7, 0xE2D6, 0x6DF9, 0x6F6E,
	// 1800
	0xE2D7, 0xE2D8, 0x6FC6, 0x7039, 0x701E, 0x701B, 0x3D96, 0x704A, 0x707D, 0x7077, 0x70AD, 0xE2D9, 0x7145, 0xE2DA, 0x719C, 0xE2DB,
	0x7228, 0x7235, 0x7250, 0xE2DC, 0x7280, 0x7295, 0xE2DD, 0xE2DE, 0x737A, 0x738B, 0x3EAC, 0x73A5, 0x3EB8, 0x3EB8, 0x7447, 0x745C,
	0x7471, 0x7485, 0x74CA, 0x3F1B, 0x7524, 0xE2DF, 0x753E, 0xE2E0, 0x7570, 0xE2E1, 0x7610, 0xE2E2, 0xE2E3, 0xE2E4, 0x3FFC, 0x4008,
	0x76F4, 0xE2E5, 0xE2E6, 0xE2E7, 0xE2E8, 0x771E, 0x771F, 0x771F, 0x774A, 0x4039, 0x778B, 0x4046, 0x4096, 0xE2E9, 0x784E, 0x788C,
	0x78CC, 0x40E3, 0xE2EA, 0x7956, 0xE2EB, 0xE2EC, 0x798F, 0x79EB, 0x412F, 0x7A40, 0x7A4A, 0x7A4F, 0xE2ED, 0xE2EE, 0xE2EF, 0x7AEE,
	0x4202, 0xE2F0, 0x7BC6, 0x7BC9, 0x4227, 0xE2F1, 0x7CD2, 0x42A0, 0x7CE8, 0x7CE3, 0x7D00, 0xE2F2, 0x7D63, 0x4301, 0x7DC7, 0x7E02,
	0x7E45, 0x4334, 0xE2F3, 0xE2F4, 0x4359, 0xE2F5, 0x7F7A, 0xE2F6, 0x7F95, 0x7FFA, 0x8005, 0xE2F7, 0xE2F8, 0x8060, 0xE2F9, 0x8070,
	0xE2FA, 0x43D5, 0x80B2, 0x8103, 0x440B, 0x813E, 0x5AB5, 0xE2FB, 0xE2FC, 0xE2FD, 0xE2FE, 0x8201, 0x8204, 0x8F9E, 0x446B, 0x8291,
	// 1880
	0x828B, 0x829D, 0x52B3, 0x82B1, 0x82B3, 0x82BD, 0x82E6, 0xE2FF, 0x82E5, 0x831D, 0x8363, 0x83AD, 0x8323, 0x83BD, 0x83E7, 0x8457,
	0x8353, 0x83CA, 0x83CC, 0x83DC, 0xE300, 0xE301, 0xE302, 0x452B, 0x84F1, 0x84F3, 0x8516, 0xE303, 0x8564, 0xE304, 0x455D, 0x4561,
	0xE305, 0xE306, 0x456B, 0x8650, 0x865C, 0x8667, 0x8669, 0x86A9, 0x8688, 0x870E, 0x86E2, 0x8779, 0x8728, 0x876B, 0x8786, 0x45D7,
	0x87E1, 0x8801, 0x45F9, 0x8860, 0x8863, 0xE307, 0x88D7, 0x88DE, 0x4635, 0x88FA, 0x34BB, 0xE308, 0xE309, 0x46BE, 0x46C7, 0x8AA0,
	0x8AED, 0x8B8A, 0x8C55, 0xE30A, 0x8CAB, 0x8CC1, 0x8D1B, 0x8D77, 0xE30B, 0xE30C, 0x8DCB, 0x8DBC, 0x8DF0, 0xE30D, 0x8ED4, 0x8F38,
	0xE30E, 0xE30F, 0x9094, 0x90F1, 0x9111, 0xE310, 0x911B, 0x9238, 0x92D7, 0x92D8, 0x927C, 0x93F9, 0x9415, 0xE311, 0x958B, 0x4995,
	0x95B7, 0xE312, 0x49E6, 0x96C3, 0x5DB2, 0x9723, 0xE313, 0xE314, 0x4A6E, 0x4A76, 0x97E0, 0xE315, 0x4AB2, 0xE316, 0x980B, 0x980B,
	0x9829, 0xE317, 0x98E2, 0x4B33, 0x9929, 0x99A7, 0x99C2, 0x99FE, 0x4BCE, 0xE318, 0x9B12, 0x9C40, 0x9CFD, 0x4CCE, 0x4CED, 0x9D67,
	// 1900
	0xE319, 0x4CF8, 0xE31A, 0xE31B, 0xE31C, 0x9EBB, 0x4D56, 0x9EF9, 0x9EFE, 0x9F05, 0x9F0F, 0x9F16, 0x9F3B, 0xE31D, 0xFFFF, 0xFFFF
};

static const uint16_t nf_u16_seq_2[0x610] =
{
	// 0000
	0x0041, 0x0300, 0x0041, 0x0301, 0x0041, 0x0302, 0x0041, 0x0303, 0x0041, 0x0308, 0x0041, 0x030A, 0x0043, 0x0327, 0x0045, 0x0300,
	0x0045, 0x0301, 0x0045, 0x0302, 0x0045, 0x0308, 0x0049, 0x0300, 0x0049, 0x0301, 0x0049, 0x0302, 0x0049, 0x0308, 0x004E, 0x0303,
	0x004F, 0x0300, 0x004F, 0x0301, 0x004F, 0x0302, 0x004F, 0x0303, 0x004F, 0x0308, 0x0055, 0x0300, 0x0055, 0x0301, 0x0055, 0x0302,
	0x0055, 0x0308, 0x0059, 0x0301, 0x0073, 0x0073, 0x0061, 0x0300, 0x0061, 0x0301, 0x0061, 0x0302, 0x0061, 0x0303, 0x0061, 0x0308,
	0x0061, 0x030A, 0x0063, 0x0327, 0x0065, 0x0300, 0x0065, 0x0301, 0x0065, 0x0302, 0x0065, 0x0308, 0x0069, 0x0300, 0x0069, 0x0301,
	0x0069, 0x0302, 0x0069, 0x0308, 0x006E, 0x0303, 0x006F, 0x0300, 0x006F, 0x0301, 0x006F, 0x0302, 0x006F, 0x0303, 0x006F, 0x0308,
	0x0075, 0x0300, 0x0075, 0x0301, 0x0075, 0x0302, 0x0075, 0x0308, 0x0079, 0x0301, 0x0079, 0x0308, 0x0041, 0x0304, 0x0061, 0x0304,
	0x0041, 0x0306, 0x0061, 0x0306, 0x0041, 0x0328, 0x0061, 0x0328, 0x0043, 0x0301, 0x0063, 0x0301, 0x0043, 0x0302, 0x0063, 0x0302,
	// 0080
	0x0043, 0x0307, 0x0063, 0x0307, 0x0043, 0x030C, 0x0063, 0x030C, 0x0044, 0x030C, 0x0064, 0x030C, 0x0045, 0x0304, 0x0065, 0x0304,
	0x0045, 0x0306, 0x0065, 0x0306, 0x0045, 0x0307, 0x0065, 0x0307, 0x0045, 0x0328, 0x0065, 0x0328, 0x0045, 0x030C, 0x0065, 0x030C,
	0x0047, 0x0302, 0x0067, 0x0302, 0x0047, 0x0306, 0x0067, 0x0306, 0x0047, 0x0307, 0x0067, 0x0307, 0x0047, 0x0327, 0x0067, 0x0327,
	0x0048, 0x0302, 0x0068, 0x0302, 0x0049, 0x0303, 0x0069, 0x0303, 0x0049, 0x0304, 0x0069, 0x0304, 0x0049, 0x0306, 0x0069, 0x0306,
	0x0049, 0x0328, 0x0069, 0x0328, 0x0049, 0x0307, 0x004A, 0x0302, 0x006A, 0x0302, 0x004B, 0x0327, 0x006B, 0x0327, 0x004C, 0x0301,
	0x006C, 0x0301, 0x004C, 0x0327, 0x006C, 0x0327, 0x004C, 0x030C, 0x006C, 0x030C, 0x004E, 0x0301, 0x006E, 0x0301, 0x004E, 0x0327,
	0x006E, 0x0327, 0x004E, 0x030C, 0x006E, 0x030C, 0x02BC, 0x006E, 0x004F, 0x0304, 0x006F, 0x0304, 0x004F, 0x0306, 0x006F, 0x0306,
	0x004F, 0x030B, 0x006F, 0x030B, 0x0052, 0x0301, 0x0072, 0x0301, 0x0052, 0x0327, 0x0072, 0x0327, 0x0052, 0x030C, 0x0072, 0x030C,
	// 0100
	0x0053, 0x0301, 0x0073, 0x0301, 0x0053, 0x0302, 0x0073, 0x0302, 0x0053, 0x0327, 0x0073, 0x0327, 0x0053, 0x030C, 0x0073, 0x030C,
	0x0054, 0x0327, 0x0074, 0x0327, 0x0054, 0x030C, 0x0074, 0x030C, 0x0055, 0x0303, 0x0075, 0x0303, 0x0055, 0x0304, 0x0075, 0x0304,
	0x0055, 0x0306, 0x0075, 0x0306, 0x0055, 0x030A, 0x0075, 0x030A, 0x0055, 0x030B, 0x0075, 0x030B, 0x0055, 0x0328, 0x0075, 0x0328,
	0x0057, 0x0302, 0x0077, 0x0302, 0x0059, 0x0302, 0x0079, 0x0302, 0x0059, 0x0308, 0x005A, 0x0301, 0x007A, 0x0301, 0x005A, 0x0307,
	0x007A, 0x0307, 0x005A, 0x030C, 0x007A, 0x030C, 0x004F, 0x031B, 0x006F, 0x031B, 0x0055, 0x031B, 0x0075, 0x031B, 0x0041, 0x030C,
	0x0061, 0x030C, 0x0049, 0x030C, 0x0069, 0x030C, 0x004F, 0x030C, 0x006F, 0x030C, 0x0055, 0x030C, 0x0075, 0x030C, 0x00C6, 0x0304,
	0x00E6, 0x0304, 0x0047, 0x030C, 0x0067, 0x030C, 0x004B, 0x030C, 0x006B, 0x030C, 0x004F, 0x0328, 0x006F, 0x0328, 0x01B7, 0x030C,
	0x0292, 0x030C, 0x006A, 0x030C, 0x0047, 0x0301, 0x0067, 0x0301, 0x004E, 0x0300, 0x006E, 0x0300, 0x00C6, 0x0301, 0x00E6, 0x0301,
	// 0180
	0x00D8, 0x0301, 0x00F8, 0x0301, 0x0041, 0x030F, 0x0061, 0x030F, 0x0041, 0x0311, 0x0061, 0x0311, 0x0045, 0x030F, 0x0065, 0x030F,
	0x0045, 0x0311, 0x0065, 0x0311, 0x0049, 0x030F, 0x0069, 0x030F, 0x0049, 0x0311, 0x0069, 0x0311, 0x004F, 0x030F, 0x006F, 0x030F,
	0x004F, 0x0311, 0x006F, 0x0311, 0x0052, 0x030F, 0x0072, 0x030F, 0x0052, 0x0311, 0x0072, 0x0311, 0x0055, 0x030F, 0x0075, 0x030F,
	0x0055, 0x0311, 0x0075, 0x0311, 0x0053, 0x0326, 0x0073, 0x0326, 0x0054, 0x0326, 0x0074, 0x0326, 0x0048, 0x030C, 0x0068, 0x030C,
	0x0041, 0x0307, 0x0061, 0x0307, 0x0045, 0x0327, 0x0065, 0x0327, 0x004F, 0x0307, 0x006F, 0x0307, 0x0059, 0x0304, 0x0079, 0x0304,
	0x00A8, 0x0301, 0x0391, 0x0301, 0x0395, 0x0301, 0x0397, 0x0301, 0x0399, 0x0301, 0x039F, 0x0301, 0x03A5, 0x0301, 0x03A9, 0x0301,
	0x0399, 0x0308, 0x03A5, 0x0308, 0x03B1, 0x0301, 0x03B5, 0x0301, 0x03B7, 0x0301, 0x03B9, 0x0301, 0x03B9, 0x0308, 0x03C5, 0x0308,
	0x03BF, 0x0301, 0x03C5, 0x0301, 0x03C9, 0x0301, 0x03D2, 0x0301, 0x03D2, 0x0308, 0x0415, 0x0300, 0x0415, 0x0308, 0x0413, 0x0301,
	// 0200
	0x0406, 0x0308, 0x041A, 0x0301, 0x0418, 0x0300, 0x0423, 0x0306, 0x0418, 0x0306, 0x0438, 0x0306, 0x0435, 0x0300, 0x0435, 0x0308,
	0x0433, 0x0301, 0x0456, 0x0308, 0x043A, 0x0301, 0x0438, 0x0300, 0x0443, 0x0306, 0x0474, 0x030F, 0x0475, 0x030F, 0x0416, 0x0306,
	0x0436, 0x0306, 0x0410, 0x0306, 0x0430, 0x0306, 0x0410, 0x0308, 0x0430, 0x0308, 0x0415, 0x0306, 0x0435, 0x0306, 0x04D8, 0x0308,
	0x04D9, 0x0308, 0x0416, 0x0308, 0x0436, 0x0308, 0x0417, 0x0308, 0x0437, 0x0308, 0x0418, 0x0304, 0x0438, 0x0304, 0x0418, 0x0308,
	0x0438, 0x0308, 0x041E, 0x0308, 0x043E, 0x0308, 0x04E8, 0x0308, 0x04E9, 0x0308, 0x042D, 0x0308, 0x044D, 0x0308, 0x0423, 0x0304,
	0x0443, 0x0304, 0x0423, 0x0308, 0x0443, 0x0308, 0x0423, 0x030B, 0x0443, 0x030B, 0x0427, 0x0308, 0x0447, 0x0308, 0x042B, 0x0308,
	0x044B, 0x0308, 0x0565, 0x0582, 0x0627, 0x0653, 0x0627, 0x0654, 0x0648, 0x0654, 0x0627, 0x0655, 0x064A, 0x0654, 0x06D5, 0x0654,
	0x06C1, 0x0654, 0x06D2, 0x0654, 0x0928, 0x093C, 0x0930, 0x093C, 0x0933, 0x093C, 0x0915, 0x093C, 0x0916, 0x093C, 0x0917, 0x093C,
	// 0280
	0x091C, 0x093C, 0x0921, 0x093C, 0x0922, 0x093C, 0x092B, 0x093C, 0x092F, 0x093C, 0x09C7, 0x09BE, 0x09C7, 0x09D7, 0x09A1, 0x09BC,
	0x09A2, 0x09BC, 0x09AF, 0x09BC, 0x0A32, 0x0A3C, 0x0A38, 0x0A3C, 0x0A16, 0x0A3C, 0x0A17, 0x0A3C, 0x0A1C, 0x0A3C, 0x0A2B, 0x0A3C,
	0x0B47, 0x0B56, 0x0B47, 0x0B3E, 0x0B47, 0x0B57, 0x0B21, 0x0B3C, 0x0B22, 0x0B3C, 0x0B92, 0x0BD7, 0x0BC6, 0x0BBE, 0x0BC7, 0x0BBE,
	0x0BC6, 0x0BD7, 0x0C46, 0x0C56, 0x0CBF, 0x0CD5, 0x0CC6, 0x0CD5, 0x0CC6, 0x0CD6, 0x0CC6, 0x0CC2, 0x0D46, 0x0D3E, 0x0D47, 0x0D3E,
	0x0D46, 0x0D57, 0x0DD9, 0x0DCA, 0x0DD9, 0x0DCF, 0x0DD9, 0x0DDF, 0x0F42, 0x0FB7, 0x0F4C, 0x0FB7, 0x0F51, 0x0FB7, 0x0F56, 0x0FB7,
	0x0F5B, 0x0FB7, 0x0F40, 0x0FB5, 0x0FB2, 0x0F80, 0x0FB3, 0x0F80, 0x0F92, 0x0FB7, 0x0F9C, 0x0FB7, 0x0FA1, 0x0FB7, 0x0FA6, 0x0FB7,
	0x0FAB, 0x0FB7, 0x0F90, 0x0FB5, 0x1025, 0x102E, 0x1B05, 0x1B35, 0x1B07, 0x1B35, 0x1B09, 0x1B35, 0x1B0B, 0x1B35, 0x1B0D, 0x1B35,
	0x1B11, 0x1B35, 0x1B3A, 0x1B35, 0x1B3C, 0x1B35, 0x1B3E, 0x1B35, 0x1B3F, 0x1B35, 0x1B42, 0x1B35, 0x0041, 0x0325, 0x0061, 0x0325,
	// 0300
	0x0042, 0x0307, 0x0062, 0x0307, 0x0042, 0x0323, 0x0062, 0x0323, 0x0042, 0x0331, 0x0062, 0x0331, 0x0044, 0x0307, 0x0064, 0x0307,
	0x0044, 0x0323, 0x0064, 0x0323, 0x0044, 0x0331, 0x0064, 0x0331, 0x0044, 0x0327, 0x0064, 0x0327, 0x0044, 0x032D, 0x0064, 0x032D,
	0x0045, 0x032D, 0x0065, 0x032D, 0x0045, 0x0330, 0x0065, 0x0330, 0x0046, 0x0307, 0x0066, 0x0307, 0x0047, 0x0304, 0x0067, 0x0304,
	0x0048, 0x0307, 0x0068, 0x0307, 0x0048, 0x0323, 0x0068, 0x0323, 0x0048, 0x0308, 0x0068, 0x0308, 0x0048, 0x0327, 0x0068, 0x0327,
	0x0048, 0x032E, 0x0068, 0x032E, 0x0049, 0x0330, 0x0069, 0x0330, 0x004B, 0x0301, 0x006B, 0x0301, 0x004B, 0x0323, 0x006B, 0x0323,
	0x004B, 0x0331, 0x006B, 0x0331, 0x004C, 0x0323, 0x006C, 0x0323, 0x004C, 0x0331, 0x006C, 0x0331, 0x004C, 0x032D, 0x006C, 0x032D,
	0x004D, 0x0301, 0x006D, 0x0301, 0x004D, 0x0307, 0x006D, 0x0307, 0x004D, 0x0323, 0x006D, 0x0323, 0x004E, 0x0307, 0x006E, 0x0307,
	0x004E, 0x0323, 0x006E, 0x0323, 0x004E, 0x0331, 0x006E, 0x0331, 0x004E, 0x032D, 0x006E, 0x032D, 0x0050, 0x0301, 0x0070, 0x0301,
	// 0380
	0x0050, 0x0307, 0x0070, 0x0307, 0x0052, 0x0307, 0x0072, 0x0307, 0x0052, 0x0323, 0x0072, 0x0323, 0x0052, 0x0331, 0x0072, 0x0331,
	0x0053, 0x0307, 0x0073, 0x0307, 0x0053, 0x0323, 0x0073, 0x0323, 0x0054, 0x0307, 0x0074, 0x0307, 0x0054, 0x0323, 0x0074, 0x0323,
	0x0054, 0x0331, 0x0074, 0x0331, 0x0054, 0x032D, 0x0074, 0x032D, 0x0055, 0x0324, 0x0075, 0x0324, 0x0055, 0x0330, 0x0075, 0x0330,
	0x0055, 0x032D, 0x0075, 0x032D, 0x0056, 0x0303, 0x0076, 0x0303, 0x0056, 0x0323, 0x0076, 0x0323, 0x0057, 0x0300, 0x0077, 0x0300,
	0x0057, 0x0301, 0x0077, 0x0301, 0x0057, 0x0308, 0x0077, 0x0308, 0x0057, 0x0307, 0x0077, 0x0307, 0x0057, 0x0323, 0x0077, 0x0323,
	0x0058, 0x0307, 0x0078, 0x0307, 0x0058, 0x0308, 0x0078, 0x0308, 0x0059, 0x0307, 0x0079, 0x0307, 0x005A, 0x0302, 0x007A, 0x0302,
	0x005A, 0x0323, 0x007A, 0x0323, 0x005A, 0x0331, 0x007A, 0x0331, 0x0068, 0x0331, 0x0074, 0x0308, 0x0077, 0x030A, 0x0079, 0x030A,
	0x0061, 0x02BE, 0x017F, 0x0307, 0x0073, 0x0073, 0x0041, 0x0323, 0x0061, 0x0323, 0x0041, 0x0309, 0x0061, 0x0309, 0x0045, 0x0323,
	// 0400
	0x0065, 0x0323, 0x0045, 0x0309, 0x0065, 0x0309, 0x0045, 0x0303, 0x0065, 0x0303, 0x0049, 0x0309, 0x0069, 0x0309, 0x0049, 0x0323,
	0x0069, 0x0323, 0x004F, 0x0323, 0x006F, 0x0323, 0x004F, 0x0309, 0x006F, 0x0309, 0x0055, 0x0323, 0x0075, 0x0323, 0x0055, 0x0309,
	0x0075, 0x0309, 0x0059, 0x0300, 0x0079, 0x0300, 0x0059, 0x0323, 0x0079, 0x0323, 0x0059, 0x0309, 0x0079, 0x0309, 0x0059, 0x0303,
	0x0079, 0x0303, 0x03B1, 0x0313, 0x03B1, 0x0314, 0x0391, 0x0313, 0x0391, 0x0314, 0x03B5, 0x0313, 0x03B5, 0x0314, 0x0395, 0x0313,
	0x0395, 0x0314, 0x03B7, 0x0313, 0x03B7, 0x0314, 0x0397, 0x0313, 0x0397, 0x0314, 0x03B9, 0x0313, 0x03B9, 0x0314, 0x0399, 0x0313,
	0x0399, 0x0314, 0x03BF, 0x0313, 0x03BF, 0x0314, 0x039F, 0x0313, 0x039F, 0x0314, 0x03C5, 0x0313, 0x03C5, 0x0314, 0x03A5, 0x0314,
	0x03C9, 0x0313, 0x03C9, 0x0314, 0x03A9, 0x0313, 0x03A9, 0x0314, 0x03B1, 0x0300, 0x03B1, 0x0301, 0x03B5, 0x0300, 0x03B5, 0x0301,
	0x03B7, 0x0300, 0x03B7, 0x0301, 0x03B9, 0x0300, 0x03B9, 0x0301, 0x03BF, 0x0300, 0x03BF, 0x0301, 0x03C5, 0x0300, 0x03C5, 0x0301,
	// 0480
	0x03C9, 0x0300, 0x03C9, 0x0301, 0x03B1, 0x0306, 0x03B1, 0x0304, 0x03B1, 0x0345, 0x03B1, 0x0342, 0x0391, 0x0306, 0x0391, 0x0304,
	0x0391, 0x0300, 0x0391, 0x0301, 0x0391, 0x0345, 0x00A8, 0x0342, 0x03B7, 0x0345, 0x03B7, 0x0342, 0x0395, 0x0300, 0x0395, 0x0301,
	0x0397, 0x0300, 0x0397, 0x0301, 0x0397, 0x0345, 0x1FBF, 0x0300, 0x1FBF, 0x0301, 0x1FBF, 0x0342, 0x03B9, 0x0306, 0x03B9, 0x0304,
	0x03B9, 0x0342, 0x0399, 0x0306, 0x0399, 0x0304, 0x0399, 0x0300, 0x0399, 0x0301, 0x1FFE, 0x0300, 0x1FFE, 0x0301, 0x1FFE, 0x0342,
	0x03C5, 0x0306, 0x03C5, 0x0304, 0x03C1, 0x0313, 0x03C1, 0x0314, 0x03C5, 0x0342, 0x03A5, 0x0306, 0x03A5, 0x0304, 0x03A5, 0x0300,
	0x03A5, 0x0301, 0x03A1, 0x0314, 0x00A8, 0x0300, 0x00A8, 0x0301, 0x03C9, 0x0345, 0x03C9, 0x0342, 0x039F, 0x0300, 0x039F, 0x0301,
	0x03A9, 0x0300, 0x03A9, 0x0301, 0x03A9, 0x0345, 0x0041, 0x030A, 0x2190, 0x0338, 0x2192, 0x0338, 0x2194, 0x0338, 0x21D0, 0x0338,
	0x21D4, 0x0338, 0x21D2, 0x0338, 0x2203, 0x0338, 0x2208, 0x0338, 0x220B, 0x0338, 0x2223, 0x0338, 0x2225, 0x0338, 0x223C, 0x0338,
	// 0500
	0x2243, 0x0338, 0x2245, 0x0338, 0x2248, 0x0338, 0x003D, 0x0338, 0x2261, 0x0338, 0x224D, 0x0338, 0x003C, 0x0338, 0x003E, 0x0338,
	0x2264, 0x0338, 0x2265, 0x0338, 0x2272, 0x0338, 0x2273, 0x0338, 0x2276, 0x0338, 0x2277, 0x0338, 0x227A, 0x0338, 0x227B, 0x0338,
	0x2282, 0x0338, 0x2283, 0x0338, 0x2286, 0x0338, 0x2287, 0x0338, 0x22A2, 0x0338, 0x22A8, 0x0338, 0x22A9, 0x0338, 0x22AB, 0x0338,
	0x227C, 0x0338, 0x227D, 0x0338, 0x2291, 0x0338, 0x2292, 0x0338, 0x22B2, 0x0338, 0x22B3, 0x0338, 0x22B4, 0x0338, 0x22B5, 0x0338,
	0x2ADD, 0x0338, 0x304B, 0x3099, 0x304D, 0x3099, 0x304F, 0x3099, 0x3051, 0x3099, 0x3053, 0x3099, 0x3055, 0x3099, 0x3057, 0x3099,
	0x3059, 0x3099, 0x305B, 0x3099, 0x305D, 0x3099, 0x305F, 0x3099, 0x3061, 0x3099, 0x3064, 0x3099, 0x3066, 0x3099, 0x3068, 0x3099,
	0x306F, 0x3099, 0x306F, 0x309A, 0x3072, 0x3099, 0x3072, 0x309A, 0x3075, 0x3099, 0x3075, 0x309A, 0x3078, 0x3099, 0x3078, 0x309A,
	0x307B, 0x3099, 0x307B, 0x309A, 0x3046, 0x3099, 0x309D, 0x3099, 0x30AB, 0x3099, 0x30AD, 0x3099, 0x30AF, 0x3099, 0x30B1, 0x3099,
	// 0580
	0x30B3, 0x3099, 0x30B5, 0x3099, 0x30B7, 0x3099, 0x30B9, 0x3099, 0x30BB, 0x3099, 0x30BD, 0x3099, 0x30BF, 0x3099, 0x30C1, 0x3099,
	0x30C4, 0x3099, 0x30C6, 0x3099, 0x30C8, 0x3099, 0x30CF, 0x3099, 0x30CF, 0x309A, 0x30D2, 0x3099, 0x30D2, 0x309A, 0x30D5, 0x3099,
	0x30D5, 0x309A, 0x30D8, 0x3099, 0x30D8, 0x309A, 0x30DB, 0x3099, 0x30DB, 0x309A, 0x30A6, 0x3099, 0x30EF, 0x3099, 0x30F0, 0x3099,
	0x30F1, 0x3099, 0x30F2, 0x3099, 0x30FD, 0x3099, 0x0066, 0x0066, 0x0066, 0x0069, 0x0066, 0x006C, 0x0073, 0x0074, 0x0073, 0x0074,
	0x0574, 0x0576, 0x0574, 0x0565, 0x0574, 0x056B, 0x057E, 0x0576, 0x0574, 0x056D, 0x05D9, 0x05B4, 0x05F2, 0x05B7, 0x05E9, 0x05C1,
	0x05E9, 0x05C2, 0x05D0, 0x05B7, 0x05D0, 0x05B8, 0x05D0, 0x05BC, 0x05D1, 0x05BC, 0x05D2, 0x05BC, 0x05D3, 0x05BC, 0x05D4, 0x05BC,
	0x05D5, 0x05BC, 0x05D6, 0x05BC, 0x05D8, 0x05BC, 0x05D9, 0x05BC, 0x05DA, 0x05BC, 0x05DB, 0x05BC, 0x05DC, 0x05BC, 0x05DE, 0x05BC,
	0x05E0, 0x05BC, 0x05E1, 0x05BC, 0x05E3, 0x05BC, 0x05E4, 0x05BC, 0x05E6, 0x05BC, 0x05E7, 0x05BC, 0x05E8, 0x05BC, 0x05E9, 0x05BC,
	// 0600
	0x05EA, 0x05BC, 0x05D5, 0x05B9, 0x05D1, 0x05BF, 0x05DB, 0x05BF, 0x05E4, 0x05BF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000
};

static const uint16_t nf_u16_seq_3[0x2A0] =
{
	// 0000
	0x0055, 0x0308, 0x0304, 0x0075, 0x0308, 0x0304, 0x0055, 0x0308, 0x0301, 0x0075, 0x0308, 0x0301, 0x0055, 0x0308, 0x030C, 0x0075,
	0x0308, 0x030C, 0x0055, 0x0308, 0x0300, 0x0075, 0x0308, 0x0300, 0x0041, 0x0308, 0x0304, 0x0061, 0x0308, 0x0304, 0x0041, 0x0307,
	0x0304, 0x0061, 0x0307, 0x0304, 0x004F, 0x0328, 0x0304, 0x006F, 0x0328, 0x0304, 0x0041, 0x030A, 0x0301, 0x0061, 0x030A, 0x0301,
	0x004F, 0x0308, 0x0304, 0x006F, 0x0308, 0x0304, 0x004F, 0x0303, 0x0304, 0x006F, 0x0303, 0x0304, 0x004F, 0x0307, 0x0304, 0x006F,
	0x0307, 0x0304, 0x03B9, 0x0308, 0x0301, 0x03C5, 0x0308, 0x0301, 0x0CC6, 0x0CC2, 0x0CD5, 0x0DD9, 0x0DCF, 0x0DCA, 0x0043, 0x0327,
	0x0301, 0x0063, 0x0327, 0x0301, 0x0045, 0x0304, 0x0300, 0x0065, 0x0304, 0x0300, 0x0045, 0x0304, 0x0301, 0x0065, 0x0304, 0x0301,
	0x0045, 0x0327, 0x0306, 0x0065, 0x0327, 0x0306, 0x0049, 0x0308, 0x0301, 0x0069, 0x0308, 0x0301, 0x004C, 0x0323, 0x0304, 0x006C,
	0x0323, 0x0304, 0x004F, 0x0303, 0x0301, 0x006F, 0x0303, 0x0301, 0x004F, 0x0303, 0x0308, 0x006F, 0x0303, 0x0308, 0x004F, 0x0304,
	// 0080
	0x0300, 0x006F, 0x0304, 0x0300, 0x004F, 0x0304, 0x0301, 0x006F, 0x0304, 0x0301, 0x0052, 0x0323, 0x0304, 0x0072, 0x0323, 0x0304,
	0x0053, 0x0301, 0x0307, 0x0073, 0x0301, 0x0307, 0x0053, 0x030C, 0x0307, 0x0073, 0x030C, 0x0307, 0x0053, 0x0323, 0x0307, 0x0073,
	0x0323, 0x0307, 0x0055, 0x0303, 0x0301, 0x0075, 0x0303, 0x0301, 0x0055, 0x0304, 0x0308, 0x0075, 0x0304, 0x0308, 0x0041, 0x0302,
	0x0301, 0x0061, 0x0302, 0x0301, 0x0041, 0x0302, 0x0300, 0x0061, 0x0302, 0x0300, 0x0041, 0x0302, 0x0309, 0x0061, 0x0302, 0x0309,
	0x0041, 0x0302, 0x0303, 0x0061, 0x0302, 0x0303, 0x0041, 0x0323, 0x0302, 0x0061, 0x0323, 0x0302, 0x0041, 0x0306, 0x0301, 0x0061,
	0x0306, 0x0301, 0x0041, 0x0306, 0x0300, 0x0061, 0x0306, 0x0300, 0x0041, 0x0306, 0x0309, 0x0061, 0x0306, 0x0309, 0x0041, 0x0306,
	0x0303, 0x0061, 0x0306, 0x0303, 0x0041, 0x0323, 0x0306, 0x0061, 0x0323, 0x0306, 0x0045, 0x0302, 0x0301, 0x0065, 0x0302, 0x0301,
	0x0045, 0x0302, 0x0300, 0x0065, 0x0302, 0x0300, 0x0045, 0x0302, 0x0309, 0x0065, 0x0302, 0x0309, 0x0045, 0x0302, 0x0303, 0x0065,
	// 0100
	0x0302, 0x0303, 0x0045, 0x0323, 0x0302, 0x0065, 0x0323, 0x0302, 0x004F, 0x0302, 0x0301, 0x006F, 0x0302, 0x0301, 0x004F, 0x0302,
	0x0300, 0x006F, 0x0302, 0x0300, 0x004F, 0x0302, 0x0309, 0x006F, 0x0302, 0x0309, 0x004F, 0x0302, 0x0303, 0x006F, 0x0302, 0x0303,
	0x004F, 0x0323, 0x0302, 0x006F, 0x0323, 0x0302, 0x004F, 0x031B, 0x0301, 0x006F, 0x031B, 0x0301, 0x004F, 0x031B, 0x0300, 0x006F,
	0x031B, 0x0300, 0x004F, 0x031B, 0x0309, 0x006F, 0x031B, 0x0309, 0x004F, 0x031B, 0x0303, 0x006F, 0x031B, 0x0303, 0x004F, 0x031B,
	0x0323, 0x006F, 0x031B, 0x0323, 0x0055, 0x031B, 0x0301, 0x0075, 0x031B, 0x0301, 0x0055, 0x031B, 0x0300, 0x0075, 0x031B, 0x0300,
	0x0055, 0x031B, 0x0309, 0x0075, 0x031B, 0x0309, 0x0055, 0x031B, 0x0303, 0x0075, 0x031B, 0x0303, 0x0055, 0x031B, 0x0323, 0x0075,
	0x031B, 0x0323, 0x03B1, 0x0313, 0x0300, 0x03B1, 0x0314, 0x0300, 0x03B1, 0x0313, 0x0301, 0x03B1, 0x0314, 0x0301, 0x03B1, 0x0313,
	0x0342, 0x03B1, 0x0314, 0x0342, 0x0391, 0x0313, 0x0300, 0x0391, 0x0314, 0x0300, 0x0391, 0x0313, 0x0301, 0x0391, 0x0314, 0x0301,
	// 0180
	0x0391, 0x0313, 0x0342, 0x0391, 0x0314, 0x0342, 0x03B5, 0x0313, 0x0300, 0x03B5, 0x0314, 0x0300, 0x03B5, 0x0313, 0x0301, 0x03B5,
	0x0314, 0x0301, 0x0395, 0x0313, 0x0300, 0x0395, 0x0314, 0x0300, 0x0395, 0x0313, 0x0301, 0x0395, 0x0314, 0x0301, 0x03B7, 0x0313,
	0x0300, 0x03B7, 0x0314, 0x0300, 0x03B7, 0x0313, 0x0301, 0x03B7, 0x0314, 0x0301, 0x03B7, 0x0313, 0x0342, 0x03B7, 0x0314, 0x0342,
	0x0397, 0x0313, 0x0300, 0x0397, 0x0314, 0x0300, 0x0397, 0x0313, 0x0301, 0x0397, 0x0314, 0x0301, 0x0397, 0x0313, 0x0342, 0x0397,
	0x0314, 0x0342, 0x03B9, 0x0313, 0x0300, 0x03B9, 0x0314, 0x0300, 0x03B9, 0x0313, 0x0301, 0x03B9, 0x0314, 0x0301, 0x03B9, 0x0313,
	0x0342, 0x03B9, 0x0314, 0x0342, 0x0399, 0x0313, 0x0300, 0x0399, 0x0314, 0x0300, 0x0399, 0x0313, 0x0301, 0x0399, 0x0314, 0x0301,
	0x0399, 0x0313, 0x0342, 0x0399, 0x0314, 0x0342, 0x03BF, 0x0313, 0x0300, 0x03BF, 0x0314, 0x0300, 0x03BF, 0x0313, 0x0301, 0x03BF,
	0x0314, 0x0301, 0x039F, 0x0313, 0x0300, 0x039F, 0x0314, 0x0300, 0x039F, 0x0313, 0x0301, 0x039F, 0x0314, 0x0301, 0x03C5, 0x0313,
	// 0200
	0x0300, 0x03C5, 0x0314, 0x0300, 0x03C5, 0x0313, 0x0301, 0x03C5, 0x0314, 0x0301, 0x03C5, 0x0313, 0x0342, 0x03C5, 0x0314, 0x0342,
	0x03A5, 0x0314, 0x0300, 0x03A5, 0x0314, 0x0301, 0x03A5, 0x0314, 0x0342, 0x03C9, 0x0313, 0x0300, 0x03C9, 0x0314, 0x0300, 0x03C9,
	0x0313, 0x0301, 0x03C9, 0x0314, 0x0301, 0x03C9, 0x0313, 0x0342, 0x03C9, 0x0314, 0x0342, 0x03A9, 0x0313, 0x0300, 0x03A9, 0x0314,
	0x0300, 0x03A9, 0x0313, 0x0301, 0x03A9, 0x0314, 0x0301, 0x03A9, 0x0313, 0x0342, 0x03A9, 0x0314, 0x0342, 0x03B1, 0x0313, 0x0345,
	0x03B1, 0x0314, 0x0345, 0x0391, 0x0313, 0x0345, 0x0391, 0x0314, 0x0345, 0x03B7, 0x0313, 0x0345, 0x03B7, 0x0314, 0x0345, 0x0397,
	0x0313, 0x0345, 0x0397, 0x0314, 0x0345, 0x03C9, 0x0313, 0x0345, 0x03C9, 0x0314, 0x0345, 0x03A9, 0x0313, 0x0345, 0x03A9, 0x0314,
	0x0345, 0x03B1, 0x0300, 0x0345, 0x03B1, 0x0301, 0x0345, 0x03B1, 0x0342, 0x0345, 0x03B7, 0x0300, 0x0345, 0x03B7, 0x0301, 0x0345,
	0x03B7, 0x0342, 0x0345, 0x03B9, 0x0308, 0x0300, 0x03B9, 0x0308, 0x0301, 0x03B9, 0x0308, 0x0342, 0x03C5, 0x0308, 0x0300, 0x03C5,
	// 0280
	0x0308, 0x0301, 0x03C5, 0x0308, 0x0342, 0x03C9, 0x0300, 0x0345, 0x03C9, 0x0301, 0x0345, 0x03C9, 0x0342, 0x0345, 0x0066, 0x0066,
	0x0069, 0x0066, 0x0066, 0x006C, 0x05E9, 0x05BC, 0x05C1, 0x05E9, 0x05BC, 0x05C2, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000
};

static const uint16_t nf_u16_seq_misc[0xC8] =
{
	// 0000
	0x0E61, 0x0300, 0x0E61, 0x0301, 0x0E61, 0x0313, 0x0E62, 0x0308, 0x0301, 0x0812, 0x0F71, 0x0F72, 0x0812, 0x0F71, 0x0F74, 0x0812,
	0x0F71, 0x0F80, 0x0004, 0x03B1, 0x0313, 0x0300, 0x0345, 0x0004, 0x03B1, 0x0314, 0x0300, 0x0345, 0x0004, 0x03B1, 0x0313, 0x0301,
	0x0345, 0x0004, 0x03B1, 0x0314, 0x0301, 0x0345, 0x0004, 0x03B1, 0x0313, 0x0342, 0x0345, 0x0004, 0x03B1, 0x0314, 0x0342, 0x0345,
	0x0004, 0x0391, 0x0313, 0x0300, 0x0345, 0x0004, 0x0391, 0x0314, 0x0300, 0x0345, 0x0004, 0x0391, 0x0313, 0x0301, 0x0345, 0x0004,
	0x0391, 0x0314, 0x0301, 0x0345, 0x0004, 0x0391, 0x0313, 0x0342, 0x0345, 0x0004, 0x0391, 0x0314, 0x0342, 0x0345, 0x0004, 0x03B7,
	0x0313, 0x0300, 0x0345, 0x0004, 0x03B7, 0x0314, 0x0300, 0x0345, 0x0004, 0x03B7, 0x0313, 0x0301, 0x0345, 0x0004, 0x03B7, 0x0314,
	0x0301, 0x0345, 0x0004, 0x03B7, 0x0313, 0x0342, 0x0345, 0x0004, 0x03B7, 0x0314, 0x0342, 0x0345, 0x0004, 0x0397, 0x0313, 0x0300,
	0x0345, 0x0004, 0x0397, 0x0314, 0x0300, 0x0345, 0x0004, 0x0397, 0x0313, 0x0301, 0x0345, 0x0004, 0x0397, 0x0314, 0x0301, 0x0345,
	// 0080
	0x0004, 0x0397, 0x0313, 0x0342, 0x0345, 0x0004, 0x0397, 0x0314, 0x0342, 0x0345, 0x0004, 0x03C9, 0x0313, 0x0300, 0x0345, 0x0004,
	0x03C9, 0x0314, 0x0300, 0x0345, 0x0004, 0x03C9, 0x0313, 0x0301, 0x0345, 0x0004, 0x03C9, 0x0314, 0x0301, 0x0345, 0x0004, 0x03C9,
	0x0313, 0x0342, 0x0345, 0x0004, 0x03C9, 0x0314, 0x0342, 0x0345, 0x0004, 0x03A9, 0x0313, 0x0300, 0x0345, 0x0004, 0x03A9, 0x0314,
	0x0300, 0x0345, 0x0004, 0x03A9, 0x0313, 0x0301, 0x0345, 0x0004, 0x03A9, 0x0314, 0x0301, 0x0345, 0x0004, 0x03A9, 0x0313, 0x0342,
	0x0345, 0x0004, 0x03A9, 0x0314, 0x0342, 0x0345, 0x0000, 0x0000
};

static const uint32_t nf_u32_char[0x320] =
{
	// 0000
	0x00000501, 0x00000503, 0x00000505, 0x00000507, 0x00000509, 0x0000050B, 0x0000050D, 0x0000050F, 0x00000511, 0x00000513, 0x00000515, 0x00000517, 0x00000519, 0x0000051B, 0x0000051D, 0x0000051F,
	0x00000521, 0x00000523, 0x00000525, 0x00000527, 0x00000529, 0x0000052B, 0x0000052D, 0x0000052F, 0x00000561, 0x00000562, 0x00000563, 0x00000564, 0x00000565, 0x00000566, 0x00000567, 0x00000568,
	0x00000569, 0x0000056A, 0x0000056B, 0x0000056C, 0x0000056D, 0x0000056E, 0x0000056F, 0x00000570, 0x00000571, 0x00000572, 0x00000573, 0x00000574, 0x00000575, 0x00000576, 0x00000577, 0x00000578,
	0x00000579, 0x0000057A, 0x0000057B, 0x0000057C, 0x0000057D, 0x0000057E, 0x0000057F, 0x00000580, 0x00000581, 0x00000582, 0x00000583, 0x00000584, 0x00000585, 0x00000586, 0x00002D00, 0x00002D01,
	0x00002D02, 0x00002D03, 0x00002D04, 0x00002D05, 0x00002D06, 0x00002D07, 0x00002D08, 0x00002D09, 0x00002D0A, 0x00002D0B, 0x00002D0C, 0x00002D0D, 0x00002D0E, 0x00002D0F, 0x00002D10, 0x00002D11,
	0x00002D12, 0x00002D13, 0x00002D14, 0x00002D15, 0x00002D16, 0x00002D17, 0x00002D18, 0x00002D19, 0x00002D1A, 0x00002D1B, 0x00002D1C, 0x00002D1D, 0x00002D1E, 0x00002D1F, 0x00002D20, 0x00002D21,
	0x00002D22, 0x00002D23, 0x00002D24, 0x00002D25, 0x00002D27, 0x00002D2D, 0x000013F0, 0x000013F1, 0x000013F2, 0x000013F3, 0x000013F4, 0x000013F5, 0x00000432, 0x00000434, 0x0000043E, 0x00000441,
	0x00000442, 0x00000442, 0x0000044A, 0x00000463, 0x0000A64B, 0x00001EFB, 0x00001EFD, 0x00001EFF, 0x0000214E, 0x00002170, 0x00002171, 0x00002172, 0x00002173, 0x00002174, 0x00002175, 0x00002176,
	// 0080
	0x00002177, 0x00002178, 0x00002179, 0x0000217A, 0x0000217B, 0x0000217C, 0x0000217D, 0x0000217E, 0x0000217F, 0x00002184, 0x000024D0, 0x000024D1, 0x000024D2, 0x000024D3, 0x000024D4, 0x000024D5,
	0x000024D6, 0x000024D7, 0x000024D8, 0x000024D9, 0x000024DA, 0x000024DB, 0x000024DC, 0x000024DD, 0x000024DE, 0x000024DF, 0x000024E0, 0x000024E1, 0x000024E2, 0x000024E3, 0x000024E4, 0x000024E5,
	0x000024E6, 0x000024E7, 0x000024E8, 0x000024E9, 0x00002C30, 0x00002C31, 0x00002C32, 0x00002C33, 0x00002C34, 0x00002C35, 0x00002C36, 0x00002C37, 0x00002C38, 0x00002C39, 0x00002C3A, 0x00002C3B,
	0x00002C3C, 0x00002C3D, 0x00002C3E, 0x00002C3F, 0x00002C40, 0x00002C41, 0x00002C42, 0x00002C43, 0x00002C44, 0x00002C45, 0x00002C46, 0x00002C47, 0x00002C48, 0x00002C49, 0x00002C4A, 0x00002C4B,
	0x00002C4C, 0x00002C4D, 0x00002C4E, 0x00002C4F, 0x00002C50, 0x00002C51, 0x00002C52, 0x00002C53, 0x00002C54, 0x00002C55, 0x00002C56, 0x00002C57, 0x00002C58, 0x00002C59, 0x00002C5A, 0x00002C5B,
	0x00002C5C, 0x00002C5D, 0x00002C5E, 0x00002C61, 0x0000026B, 0x00001D7D, 0x0000027D, 0x00002C68, 0x00002C6A, 0x00002C6C, 0x00000251, 0x00000271, 0x00000250, 0x00000252, 0x00002C73, 0x00002C76,
	0x0000023F, 0x00000240, 0x00002C81, 0x00002C83, 0x00002C85, 0x00002C87, 0x00002C89, 0x00002C8B, 0x00002C8D, 0x00002C8F, 0x00002C91, 0x00002C93, 0x00002C95, 0x00002C97, 0x00002C99, 0x00002C9B,
	0x00002C9D, 0x00002C9F, 0x00002CA1, 0x00002CA3, 0x00002CA5, 0x00002CA7, 0x00002CA9, 0x00002CAB, 0x00002CAD, 0x00002CAF, 0x00002CB1, 0x00002CB3, 0x00002CB5, 0x00002CB7, 0x00002CB9, 0x00002CBB,
	// 0100
	0x00002CBD, 0x00002CBF, 0x00002CC1, 0x00002CC3, 0x00002CC5, 0x00002CC7, 0x00002CC9, 0x00002CCB, 0x00002CCD, 0x00002CCF, 0x00002CD1, 0x00002CD3, 0x00002CD5, 0x00002CD7, 0x00002CD9, 0x00002CDB,
	0x00002CDD, 0x00002CDF, 0x00002CE1, 0x00002CE3, 0x00002CEC, 0x00002CEE, 0x00002CF3, 0x0000A641, 0x0000A643, 0x0000A645, 0x0000A647, 0x0000A649, 0x0000A64B, 0x0000A64D, 0x0000A64F, 0x0000A651,
	0x0000A653, 0x0000A655, 0x0000A657, 0x0000A659, 0x0000A65B, 0x0000A65D, 0x0000A65F, 0x0000A661, 0x0000A663, 0x0000A665, 0x0000A667, 0x0000A669, 0x0000A66B, 0x0000A66D, 0x0000A681, 0x0000A683,
	0x0000A685, 0x0000A687, 0x0000A689, 0x0000A68B, 0x0000A68D, 0x0000A68F, 0x0000A691, 0x0000A693, 0x0000A695, 0x0000A697, 0x0000A699, 0x0000A69B, 0x0000A723, 0x0000A725, 0x0000A727, 0x0000A729,
	0x0000A72B, 0x0000A72D, 0x0000A72F, 0x0000A733, 0x0000A735, 0x0000A737, 0x0000A739, 0x0000A73B, 0x0000A73D, 0x0000A73F, 0x0000A741, 0x0000A743, 0x0000A745, 0x0000A747, 0x0000A749, 0x0000A74B,
	0x0000A74D, 0x0000A74F, 0x0000A751, 0x0000A753, 0x0000A755, 0x0000A757, 0x0000A759, 0x0000A75B, 0x0000A75D, 0x0000A75F, 0x0000A761, 0x0000A763, 0x0000A765, 0x0000A767, 0x0000A769, 0x0000A76B,
	0x0000A76D, 0x0000A76F, 0x0000A77A, 0x0000A77C, 0x00001D79, 0x0000A77F, 0x0000A781, 0x0000A783, 0x0000A785, 0x0000A787, 0x0000A78C, 0x00000265, 0x0000A791, 0x0000A793, 0x0000A797, 0x0000A799,
	0x0000A79B, 0x0000A79D, 0x0000A79F, 0x0000A7A1, 0x0000A7A3, 0x0000A7A5, 0x0000A7A7, 0x0000A7A9, 0x00000266, 0x0000025C, 0x00000261, 0x0000026C, 0x0000026A, 0x0000029E, 0x00000287, 0x0000029D,
	// 0180
	0x0000AB53, 0x0000A7B5, 0x0000A7B7, 0x000013A0, 0x000013A1, 0x000013A2, 0x000013A3, 0x000013A4, 0x000013A5, 0x000013A6, 0x000013A7, 0x000013A8, 0x000013A9, 0x000013AA, 0x000013AB, 0x000013AC,
	0x000013AD, 0x000013AE, 0x000013AF, 0x000013B0, 0x000013B1, 0x000013B2, 0x000013B3, 0x000013B4, 0x000013B5, 0x000013B6, 0x000013B7, 0x000013B8, 0x000013B9, 0x000013BA, 0x000013BB, 0x000013BC,
	0x000013BD, 0x000013BE, 0x000013BF, 0x000013C0, 0x000013C1, 0x000013C2, 0x000013C3, 0x000013C4, 0x000013C5, 0x000013C6, 0x000013C7, 0x000013C8, 0x000013C9, 0x000013CA, 0x000013CB, 0x000013CC,
	0x000013CD, 0x000013CE, 0x000013CF, 0x000013D0, 0x000013D1, 0x000013D2, 0x000013D3, 0x000013D4, 0x000013D5, 0x000013D6, 0x000013D7, 0x000013D8, 0x000013D9, 0x000013DA, 0x000013DB, 0x000013DC,
	0x000013DD, 0x000013DE, 0x000013DF, 0x000013E0, 0x000013E1, 0x000013E2, 0x000013E3, 0x000013E4, 0x000013E5, 0x000013E6, 0x000013E7, 0x000013E8, 0x000013E9, 0x000013EA, 0x000013EB, 0x000013EC,
	0x000013ED, 0x000013EE, 0x000013EF, 0x000242EE, 0x0002284A, 0x00022844, 0x000233D5, 0x00025249, 0x00025CD0, 0x00027ED3, 0x0000FF41, 0x0000FF42, 0x0000FF43, 0x0000FF44, 0x0000FF45, 0x0000FF46,
	0x0000FF47, 0x0000FF48, 0x0000FF49, 0x0000FF4A, 0x0000FF4B, 0x0000FF4C, 0x0000FF4D, 0x0000FF4E, 0x0000FF4F, 0x0000FF50, 0x0000FF51, 0x0000FF52, 0x0000FF53, 0x0000FF54, 0x0000FF55, 0x0000FF56,
	0x0000FF57, 0x0000FF58, 0x0000FF59, 0x0000FF5A, 0x00010428, 0x00010429, 0x0001042A, 0x0001042B, 0x0001042C, 0x0001042D, 0x0001042E, 0x0001042F, 0x00010430, 0x00010431, 0x00010432, 0x00010433,
	// 0200
	0x00010434, 0x00010435, 0x00010436, 0x00010437, 0x00010438, 0x00010439, 0x0001043A, 0x0001043B, 0x0001043C, 0x0001043D, 0x0001043E, 0x0001043F, 0x00010440, 0x00010441, 0x00010442, 0x00010443,
	0x00010444, 0x00010445, 0x00010446, 0x00010447, 0x00010448, 0x00010449, 0x0001044A, 0x0001044B, 0x0001044C, 0x0001044D, 0x0001044E, 0x0001044F, 0x000104D8, 0x000104D9, 0x000104DA, 0x000104DB,
	0x000104DC, 0x000104DD, 0x000104DE, 0x000104DF, 0x000104E0, 0x000104E1, 0x000104E2, 0x000104E3, 0x000104E4, 0x000104E5, 0x000104E6, 0x000104E7, 0x000104E8, 0x000104E9, 0x000104EA, 0x000104EB,
	0x000104EC, 0x000104ED, 0x000104EE, 0x000104EF, 0x000104F0, 0x000104F1, 0x000104F2, 0x000104F3, 0x000104F4, 0x000104F5, 0x000104F6, 0x000104F7, 0x000104F8, 0x000104F9, 0x000104FA, 0x000104FB,
	0x00010CC0, 0x00010CC1, 0x00010CC2, 0x00010CC3, 0x00010CC4, 0x00010CC5, 0x00010CC6, 0x00010CC7, 0x00010CC8, 0x00010CC9, 0x00010CCA, 0x00010CCB, 0x00010CCC, 0x00010CCD, 0x00010CCE, 0x00010CCF,
	0x00010CD0, 0x00010CD1, 0x00010CD2, 0x00010CD3, 0x00010CD4, 0x00010CD5, 0x00010CD6, 0x00010CD7, 0x00010CD8, 0x00010CD9, 0x00010CDA, 0x00010CDB, 0x00010CDC, 0x00010CDD, 0x00010CDE, 0x00010CDF,
	0x00010CE0, 0x00010CE1, 0x00010CE2, 0x00010CE3, 0x00010CE4, 0x00010CE5, 0x00010CE6, 0x00010CE7, 0x00010CE8, 0x00010CE9, 0x00010CEA, 0x00010CEB, 0x00010CEC, 0x00010CED, 0x00010CEE, 0x00010CEF,
	0x00010CF0, 0x00010CF1, 0x00010CF2, 0x000118C0, 0x000118C1, 0x000118C2, 0x000118C3, 0x000118C4, 0x000118C5, 0x000118C6, 0x000118C7, 0x000118C8, 0x000118C9, 0x000118CA, 0x000118CB, 0x000118CC,
	// 0280
	0x000118CD, 0x000118CE, 0x000118CF, 0x000118D0, 0x000118D1, 0x000118D2, 0x000118D3, 0x000118D4, 0x000118D5, 0x000118D6, 0x000118D7, 0x000118D8, 0x000118D9, 0x000118DA, 0x000118DB, 0x000118DC,
	0x000118DD, 0x000118DE, 0x000118DF, 0x0001E922, 0x0001E923, 0x0001E924, 0x0001E925, 0x0001E926, 0x0001E927, 0x0001E928, 0x0001E929, 0x0001E92A, 0x0001E92B, 0x0001E92C, 0x0001E92D, 0x0001E92E,
	0x0001E92F, 0x0001E930, 0x0001E931, 0x0001E932, 0x0001E933, 0x0001E934, 0x0001E935, 0x0001E936, 0x0001E937, 0x0001E938, 0x0001E939, 0x0001E93A, 0x0001E93B, 0x0001E93C, 0x0001E93D, 0x0001E93E,
	0x0001E93F, 0x0001E940, 0x0001E941, 0x0001E942, 0x0001E943, 0x00020122, 0x0002063A, 0x0002051C, 0x0002054B, 0x000291DF, 0x00020A2C, 0x00020B63, 0x000214E4, 0x000216A8, 0x000216EA, 0x000219C8,
	0x00021B18, 0x00021DE4, 0x00021DE6, 0x00022183, 0x0002A392, 0x00022331, 0x00022331, 0x000232B8, 0x000261DA, 0x000226D4, 0x00022B0C, 0x00022BF1, 0x0002300A, 0x000233C3, 0x0002346D, 0x000236A3,
	0x000238A7, 0x00023A8D, 0x00021D0B, 0x00023AFA, 0x00023CBC, 0x00023D1E, 0x00023ED1, 0x00023F5E, 0x00023F8E, 0x00020525, 0x00024263, 0x000243AB, 0x00024608, 0x00024735, 0x00024814, 0x00024C36,
	0x00024C92, 0x0002219F, 0x00024FA1, 0x00024FB8, 0x00025044, 0x000250F3, 0x000250F2, 0x00025119, 0x00025133, 0x0002541D, 0x00025626, 0x0002569A, 0x000256C5, 0x0002597C, 0x00025AA7, 0x00025AA7,
	0x00025BAB, 0x00025C80, 0x00025F86, 0x00026228, 0x00026247, 0x000262D9, 0x0002633E, 0x000264DA, 0x00026523, 0x000265A8, 0x0002335F, 0x000267A7, 0x000267B5, 0x00023393, 0x0002339C, 0x00026B3C,
	// 0300
	0x00026C36, 0x00026D6B, 0x00026CD5, 0x000273CA, 0x00026F2C, 0x00026FB1, 0x000270D2, 0x00027667, 0x000278AE, 0x00027966, 0x00027CA8, 0x00027F2F, 0x00020804, 0x000208DE, 0x000285D2, 0x000285ED,
	0x0002872E, 0x00028BFA, 0x00028D77, 0x00029145, 0x0002921A, 0x0002940A, 0x00029496, 0x000295B6, 0x00029B30, 0x0002A0CE, 0x0002A105, 0x0002A20E, 0x0002A291, 0x0002A600, 0x00000000, 0x00000000
};

static const uint32_t nf_u32_seq_misc[0x54] =
{
	// 0000
	0x00000002, 0x00011099, 0x000110BA, 0x00000002, 0x0001109B, 0x000110BA, 0x00000002, 0x000110A5, 0x000110BA, 0x00000002, 0x00011131, 0x00011127, 0x00000002, 0x00011132, 0x00011127, 0x00000002,
	0x00011347, 0x0001133E, 0x00000002, 0x00011347, 0x00011357, 0x00000002, 0x000114B9, 0x000114BA, 0x00000002, 0x000114B9, 0x000114B0, 0x00000002, 0x000114B9, 0x000114BD, 0x00000002, 0x000115B8,
	0x000115AF, 0x00000002, 0x000115B9, 0x000115AF, 0x00000002, 0x0001D157, 0x0001D165, 0x00000002, 0x0001D158, 0x0001D165, 0x00000003, 0x0001D158, 0x0001D165, 0x0001D16E, 0x00000003, 0x0001D158,
	0x0001D165, 0x0001D16F, 0x00000003, 0x0001D158, 0x0001D165, 0x0001D170, 0x00000003, 0x0001D158, 0x0001D165, 0x0001D171, 0x00000003, 0x0001D158, 0x0001D165, 0x0001D172, 0x00000002, 0x0001D1B9,
	0x0001D165, 0x00000002, 0x0001D1BA, 0x0001D165, 0x00000003, 0x0001D1B9, 0x0001D165, 0x0001D16E, 0x00000003, 0x0001D1BA, 0x0001D165, 0x0001D16E, 0x00000003, 0x0001D1B9, 0x0001D165, 0x0001D16F,
	0x00000003, 0x0001D1BA, 0x0001D165, 0x0001D16F
};

