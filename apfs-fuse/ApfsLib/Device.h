/*
	This file is part of apfs-fuse, a read-only implementation of APFS
	(Apple File System) for FUSE.
	Copyright (C) 2017 Simon <PERSON>

	Apfs-fuse is free software: you can redistribute it and/or modify
	it under the terms of the GNU General Public License as published by
	the Free Software Foundation, either version 2 of the License, or
	(at your option) any later version.

	Apfs-fuse is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU General Public License for more details.

	You should have received a copy of the GNU General Public License
	along with apfs-fuse.  If not, see <http://www.gnu.org/licenses/>.
*/

#pragma once

#include <cstdint>

class Device
{
protected:
	Device();

public:
	virtual ~Device();

	virtual bool Open(const char *name) = 0;
	virtual void Close() = 0;

	virtual bool Read(void *data, uint64_t offs, uint64_t len) = 0;
	virtual uint64_t GetSize() const = 0;

	unsigned int GetSectorSize() const { return m_sector_size; }
	void SetSectorSize(unsigned int size) { m_sector_size = size; }

	static Device *OpenDevice(const char *name);

private:
	unsigned int m_sector_size;
};
